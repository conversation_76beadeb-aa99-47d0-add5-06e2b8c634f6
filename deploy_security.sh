#!/bin/bash

# Security Deployment Script for MassacreMC
# Automated setup of firewall, monitoring, and vulnerability auditing

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/var/log/security_deployment.log"

log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

check_root() {
    if [ "$EUID" -ne 0 ]; then
        echo "This script must be run as root"
        echo "Please run: sudo $0"
        exit 1
    fi
}

install_dependencies() {
    log_message "Installing security dependencies..."
    
    # Update package list
    apt-get update -qq
    
    # Install essential packages
    local packages=(
        "iptables-persistent"
        "fail2ban"
        "ufw"
        "mailutils"
        "bc"
        "curl"
        "wget"
        "net-tools"
        "htop"
        "iotop"
        "tcpdump"
        "nmap"
        "nikto"
        "lynis"
        "chkrootkit"
        "rkhunter"
        "clamav"
        "clamav-daemon"
        "aide"
        "logwatch"
        "psad"
    )
    
    for package in "${packages[@]}"; do
        if ! dpkg -l | grep -q "^ii  $package "; then
            log_message "Installing $package..."
            apt-get install -y "$package" >/dev/null 2>&1
        else
            log_message "$package already installed"
        fi
    done
}

setup_firewall() {
    log_message "Setting up enhanced firewall..."
    
    # Make firewall script executable
    chmod +x "$SCRIPT_DIR/firewall_setup.sh"
    
    # Run firewall setup
    "$SCRIPT_DIR/firewall_setup.sh"
    
    # Enable iptables persistence
    if command -v iptables-save >/dev/null 2>&1; then
        iptables-save > /etc/iptables/rules.v4
        log_message "Firewall rules saved"
    fi
    
    # Configure UFW as backup
    ufw --force reset >/dev/null 2>&1
    ufw default deny incoming >/dev/null 2>&1
    ufw default allow outgoing >/dev/null 2>&1
    ufw allow 22/tcp >/dev/null 2>&1
    ufw allow 80/tcp >/dev/null 2>&1
    ufw allow 443/tcp >/dev/null 2>&1
    ufw allow 19132/udp >/dev/null 2>&1
    ufw --force enable >/dev/null 2>&1
    
    log_message "UFW backup firewall configured"
}

setup_fail2ban() {
    log_message "Configuring Fail2Ban..."
    
    # Create custom jail configuration
    cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3
backend = auto
usedns = warn
logencoding = auto
enabled = false
mode = normal
filter = %(__name__)s[mode=%(mode)s]

[sshd]
enabled = true
port = ssh
logpath = %(sshd_log)s
backend = %(sshd_backend)s

[apache-auth]
enabled = true
port = http,https
logpath = %(apache_error_log)s

[apache-badbots]
enabled = true
port = http,https
logpath = %(apache_access_log)s
bantime = 86400
maxretry = 1

[apache-noscript]
enabled = true
port = http,https
logpath = %(apache_access_log)s
maxretry = 6

[apache-overflows]
enabled = true
port = http,https
logpath = %(apache_error_log)s
maxretry = 2

[apache-nohome]
enabled = true
port = http,https
logpath = %(apache_error_log)s
maxretry = 2

[apache-botsearch]
enabled = true
port = http,https
logpath = %(apache_access_log)s
maxretry = 2

[php-url-fopen]
enabled = true
port = http,https
logpath = %(apache_access_log)s
maxretry = 1
EOF

    # Create custom filter for malicious requests
    cat > /etc/fail2ban/filter.d/apache-malicious.conf << EOF
[Definition]
failregex = ^<HOST> -.*"(GET|POST).*?(device\.rsp|setup\.cgi|\.env|urbotnet|botnet|malware).*?" \d+ \d+
            ^<HOST> -.*"(GET|POST).*?(wget|curl|chmod|rm\s|cd\s).*?" \d+ \d+
            ^<HOST> -.*"(GET|POST).*?(base64_decode|eval|exec|system).*?" \d+ \d+

ignoreregex =
EOF

    # Add malicious requests jail
    cat >> /etc/fail2ban/jail.local << EOF

[apache-malicious]
enabled = true
port = http,https
logpath = %(apache_access_log)s
filter = apache-malicious
bantime = 86400
maxretry = 1
EOF

    # Restart fail2ban
    systemctl restart fail2ban
    systemctl enable fail2ban
    
    log_message "Fail2Ban configured and started"
}

setup_monitoring() {
    log_message "Setting up security monitoring..."
    
    # Make monitoring scripts executable
    chmod +x "$SCRIPT_DIR/security_monitoring_system.sh"
    chmod +x "$SCRIPT_DIR/security_monitor.sh"
    
    # Initialize monitoring system
    "$SCRIPT_DIR/security_monitoring_system.sh" init
    
    # Create systemd service for monitoring
    cat > /etc/systemd/system/massacremc-security-monitor.service << EOF
[Unit]
Description=MassacreMC Security Monitoring System
After=network.target

[Service]
Type=simple
User=root
ExecStart=$SCRIPT_DIR/security_monitoring_system.sh monitor
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # Enable and start monitoring service
    systemctl daemon-reload
    systemctl enable massacremc-security-monitor
    systemctl start massacremc-security-monitor
    
    log_message "Security monitoring service started"
}

setup_vulnerability_auditing() {
    log_message "Setting up vulnerability auditing..."
    
    # Make audit script executable
    chmod +x "$SCRIPT_DIR/vulnerability_audit.sh"
    
    # Install audit tools
    "$SCRIPT_DIR/vulnerability_audit.sh" install
    
    # Create cron job for daily audits
    cat > /etc/cron.d/massacremc-security-audit << EOF
# MassacreMC Security Audit - Daily at 2 AM
0 2 * * * root $SCRIPT_DIR/vulnerability_audit.sh full >/dev/null 2>&1

# Weekly comprehensive scan - Sundays at 3 AM
0 3 * * 0 root $SCRIPT_DIR/vulnerability_audit.sh full && systemctl restart massacremc-security-monitor
EOF

    log_message "Vulnerability auditing scheduled"
}

setup_log_monitoring() {
    log_message "Setting up log monitoring..."
    
    # Configure logwatch
    if command -v logwatch >/dev/null 2>&1; then
        cat > /etc/logwatch/conf/logwatch.conf << EOF
LogDir = /var/log
TmpDir = /var/cache/logwatch
MailTo = <EMAIL>
MailFrom = <EMAIL>
Print = No
Save = /var/log/logwatch
Range = yesterday
Detail = Med
Service = All
mailer = "/usr/sbin/sendmail -t"
EOF
    fi
    
    # Configure log rotation for security logs
    cat > /etc/logrotate.d/massacremc-security << EOF
/var/log/security/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        systemctl reload massacremc-security-monitor >/dev/null 2>&1 || true
    endscript
}
EOF

    log_message "Log monitoring configured"
}

create_security_dashboard() {
    log_message "Creating security dashboard..."
    
    # Create web-accessible security dashboard
    local dashboard_dir="/var/www/html/security"
    mkdir -p "$dashboard_dir"
    
    cat > "$dashboard_dir/index.php" << 'EOF'
<?php
// Simple security dashboard for MassacreMC
// Requires authentication

session_start();

// Simple authentication (change password!)
$valid_password = "SecurePassword123!";

if (!isset($_SESSION['authenticated'])) {
    if (isset($_POST['password']) && $_POST['password'] === $valid_password) {
        $_SESSION['authenticated'] = true;
    } else {
        ?>
        <!DOCTYPE html>
        <html>
        <head><title>Security Dashboard - Login</title></head>
        <body>
            <h2>Security Dashboard Login</h2>
            <form method="post">
                <input type="password" name="password" placeholder="Password" required>
                <button type="submit">Login</button>
            </form>
        </body>
        </html>
        <?php
        exit;
    }
}

// Dashboard content
?>
<!DOCTYPE html>
<html>
<head>
    <title>MassacreMC Security Dashboard</title>
    <meta http-equiv="refresh" content="30">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .ok { background: #d4edda; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .metric { display: inline-block; margin: 10px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>MassacreMC Security Dashboard</h1>
    <p>Last updated: <?= date('Y-m-d H:i:s') ?></p>
    
    <div class="metric">
        <h3>Blocked IPs</h3>
        <p><?= file_exists('/etc/massacremc/security/blocked_ips.txt') ? count(file('/etc/massacremc/security/blocked_ips.txt')) : 0 ?></p>
    </div>
    
    <div class="metric">
        <h3>System Load</h3>
        <p><?= sys_getloadavg()[0] ?></p>
    </div>
    
    <div class="metric">
        <h3>Disk Usage</h3>
        <p><?= round(disk_free_space('/') / disk_total_space('/') * 100, 1) ?>% free</p>
    </div>
    
    <div class="metric">
        <h3>Active Connections</h3>
        <p><?= count(explode("\n", shell_exec("netstat -tn | grep ESTABLISHED"))) ?></p>
    </div>
    
    <h2>Recent Security Events</h2>
    <pre><?= file_exists('/var/log/security/threats.log') ? htmlspecialchars(shell_exec('tail -20 /var/log/security/threats.log')) : 'No threats logged' ?></pre>
    
    <h2>Service Status</h2>
    <div class="status <?= strpos(shell_exec('systemctl is-active massacremc-security-monitor'), 'active') !== false ? 'ok' : 'error' ?>">
        Security Monitor: <?= shell_exec('systemctl is-active massacremc-security-monitor') ?>
    </div>
    
    <div class="status <?= strpos(shell_exec('systemctl is-active fail2ban'), 'active') !== false ? 'ok' : 'error' ?>">
        Fail2Ban: <?= shell_exec('systemctl is-active fail2ban') ?>
    </div>
    
    <p><a href="?logout=1">Logout</a></p>
    
    <?php if (isset($_GET['logout'])) { session_destroy(); header('Location: index.php'); } ?>
</body>
</html>
EOF

    # Protect dashboard directory
    cat > "$dashboard_dir/.htaccess" << EOF
AuthType Basic
AuthName "Security Dashboard"
AuthUserFile /etc/apache2/.htpasswd
Require valid-user

# Additional security
<Files "*.log">
    Deny from all
</Files>
EOF

    log_message "Security dashboard created at /security/"
}

# Main deployment function
main() {
    log_message "Starting MassacreMC security deployment..."
    
    check_root
    install_dependencies
    setup_firewall
    setup_fail2ban
    setup_monitoring
    setup_vulnerability_auditing
    setup_log_monitoring
    create_security_dashboard
    
    log_message "Security deployment completed successfully!"
    
    echo ""
    echo "=== MassacreMC Security Deployment Complete ==="
    echo ""
    echo "Services started:"
    echo "- Enhanced firewall with iptables"
    echo "- Fail2Ban intrusion prevention"
    echo "- Real-time security monitoring"
    echo "- Automated vulnerability auditing"
    echo "- Log monitoring and rotation"
    echo ""
    echo "Security dashboard: https://your-domain.com/security/"
    echo "Change the dashboard password in /var/www/html/security/index.php"
    echo ""
    echo "Log files:"
    echo "- Security events: /var/log/security/"
    echo "- Deployment log: $LOG_FILE"
    echo ""
    echo "Management commands:"
    echo "- Check firewall: iptables -L"
    echo "- Check monitoring: systemctl status massacremc-security-monitor"
    echo "- Check fail2ban: fail2ban-client status"
    echo "- Run audit: $SCRIPT_DIR/vulnerability_audit.sh full"
    echo ""
}

# Run main function
main "$@"
