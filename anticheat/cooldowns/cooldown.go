package cooldowns

import (
	"anticheat/utils"
	"github.com/df-mc/dragonfly/server/world"
	"sync"
	"time"
)

var (
	coolDowns []*CoolDown
	cdMutex   sync.Mutex
)

type CoolDownCause int

const (
	EPearlHit CoolDownCause = iota
	NoPVP
	Immune
	StartBreak

	FlagAdded
	XrayFlag
)

type CoolDown struct {
	Type   CoolDownCause
	Handle *world.EntityHandle
	Ends   time.Time

	Permanent bool
}

func CanExecute(h *world.EntityHandle, t CoolDownCause, renew bool, durIfCreate time.Duration) bool {
	cdMutex.Lock()
	defer cdMutex.Unlock()

	cds := utils.Filter[*CoolDown](coolDowns, func(c *CoolDown) bool {
		return c.Type == t && c.Handle == h
	})
	var cd *CoolDown
	if len(cds) != 0 {
		cd = cds[0]
	}

	canExecute := cd == nil || (time.Now().After(cd.Ends) && !cd.Permanent)
	if cd != nil && time.Now().After(cd.Ends) && !cd.Permanent {
		coolDowns = utils.Filter[*CoolDown](coolDowns, func(c *CoolDown) bool {
			return c != cd
		})
	}

	newCd := &CoolDown{Handle: h, Type: t, Ends: time.Now().Add(durIfCreate), Permanent: durIfCreate == -1}
	if canExecute {
		if renew {
			if cd == nil {
				coolDowns = append(coolDowns, newCd)
			} else {
				cd.Ends = time.Now().Add(durIfCreate)
			}
		} else if durIfCreate > 0 {
			coolDowns = append(coolDowns, newCd)
		}
	}

	return canExecute
}

func RemoveCoolDown(h *world.EntityHandle, t CoolDownCause) {
	cds := utils.Filter[*CoolDown](coolDowns, func(c *CoolDown) bool {
		return c.Type == t && c.Handle == h
	})
	var cd *CoolDown
	if len(cds) != 0 {
		cd = cds[0]
	}

	if cd != nil {
		cd.Permanent = false
	}
}
