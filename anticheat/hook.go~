package anticheat

import (
	"github.com/df-mc/dragonfly/server"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"server/server/anticheat/core"
	"server/server/commands"
)

func init() {
	commands.RegisterAll()
}

func HookServer(server *server.Server) {
	core.Server = server
}

func HookSession(pl *player.Player) {
	if core.LookupPlayer(pl) == nil {
		if _, err := core.New(pl); err != nil {
			panic(err)
		}
	}
}

func SetPermissions(flagViewer, acInvincible func(src cmd.Source) bool) {
	core.FlagViewerFunc = flagViewer
	core.ACInvincibleFunc = acInvincible
}
