package core

import (
	utils2 "anticheat/utils"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"math/rand"
	"sync"
	"time"
)

var (
	userMu  sync.Mutex
	ACUsers = map[*world.EntityHandle]*ACUser{}
)

type ACUser struct {
	pl *player.Player
	h  *world.EntityHandle

	flags map[Component][]*Flag

	velocity mgl64.Vec3

	LastBlockTouched map[mgl64.Vec3]time.Time
	LastAntiXrayRan  time.Time

	obfuscatedBlocks, visitedBlocks map[mgl64.Vec3]world.Block

	inAirSince      time.Time
	AboveWaterTicks int

	speedRates []float64
	LastAngle  float64

	LastHit, LastClickFrom3Sec, LastClickAt time.Time
	ClickRates                              []time.Duration

	reachData map[int]*ReachProperties

	HitBoxNPC *world.EntityHandle

	RollbackFlyPos, RollbackGlidePos, RollbackGJesusPos mgl64.Vec3

	motionBuffer map[int]*MotionFrame
}

func New(pl *player.Player) (*ACUser, error) {
	if pl == nil {
		panic("New player should not be nil")
	}

	userMu.Lock()
	defer userMu.Unlock()

	u := &ACUser{
		pl: pl,
		h:  pl.H(),

		flags: make(map[Component][]*Flag),

		LastBlockTouched: make(map[mgl64.Vec3]time.Time),

		obfuscatedBlocks: make(map[mgl64.Vec3]world.Block),
		visitedBlocks:    make(map[mgl64.Vec3]world.Block),

		inAirSince: time.Now(),

		reachData: make(map[int]*ReachProperties),

		motionBuffer: make(map[int]*MotionFrame),
	}
	ACUsers[pl.H()] = u
	return u, nil
}

func LookupPlayer(pl *player.Player) *ACUser {
	userMu.Lock()
	defer userMu.Unlock()
	return ACUsers[pl.H()]
}

func LookupHandle(handle *world.EntityHandle) *ACUser {
	userMu.Lock()
	defer userMu.Unlock()
	return ACUsers[handle]
}

func (u *ACUser) Player() *player.Player {
	return u.pl
}

func (u *ACUser) H() *world.EntityHandle {
	return u.h
}

func (u *ACUser) AllFlags() map[Component][]*Flag {
	return u.flags
}

func (u *ACUser) FilterFlags(component Component) []*Flag {
	return u.flags[component]
}

func (u *ACUser) AddFlag(component Component, flag *Flag) {
	flags := u.flags[component]
	flags = append(flags, flag)
	u.flags[component] = flags
}

func (u *ACUser) RemoveFlag(component Component, flag *Flag) {
	flags := utils2.Filter(u.flags[component], func(f *Flag) bool {
		return f != flag
	})
	u.flags[component] = flags
}

func (u *ACUser) RemoveExpiredFlags() {
	for component, flags := range u.flags {
		for _, f := range flags {
			if f.IsExpired() {
				u.RemoveFlag(component, f)
			}
		}
	}
}

func (u *ACUser) PruneFlags() {
	u.flags = make(map[Component][]*Flag)
}

func (u *ACUser) CountAllFlags() int {
	u.RemoveExpiredFlags()
	var sum int
	for _, flags := range u.flags {
		sum += len(flags)
	}
	return sum
}

func (u *ACUser) CountFlags(component Component) int {
	u.RemoveExpiredFlags()
	return len(u.flags[component])
}

func (u *ACUser) Velocity() mgl64.Vec3 {
	return u.velocity
}

func (u *ACUser) SetVelocity(velocity mgl64.Vec3) {
	u.velocity = velocity
}

func (u *ACUser) ObfuscatedBlocks() map[mgl64.Vec3]world.Block {
	return u.obfuscatedBlocks
}

func (u *ACUser) ObfuscatedBlock(v mgl64.Vec3) world.Block {
	return u.obfuscatedBlocks[v]
}

func (u *ACUser) ObfuscateBlock(pos mgl64.Vec3, tx *world.Tx) {
	u.obfuscatedBlocks[pos] = tx.Block(cube.PosFromVec3(pos))

	ores := []world.Block{
		block.DiamondOre{},
		block.EmeraldOre{},
		block.GoldOre{},
		block.IronOre{},
		block.CoalOre{},
	}

	utils2.Session(u.pl).ViewBlockUpdate(
		cube.PosFromVec3(pos),
		ores[rand.Intn(len(ores))],
		0,
	)
}

func (u *ACUser) ClearObfuscatedBlock(pos mgl64.Vec3) {
	utils2.Session(u.pl).ViewBlockUpdate(
		cube.PosFromVec3(pos),
		u.obfuscatedBlocks[pos],
		0,
	)

	delete(u.obfuscatedBlocks, pos)
}

func (u *ACUser) VisitedBlocks() map[mgl64.Vec3]world.Block {
	return u.visitedBlocks
}

func (u *ACUser) BlockVisited(pos mgl64.Vec3) bool {
	return u.visitedBlocks[pos] != nil
}

func (u *ACUser) VisitBlock(pos mgl64.Vec3, tx *world.Tx) {
	u.visitedBlocks[pos] = tx.Block(cube.PosFromVec3(pos))
}

func (u *ACUser) UnvisitBlock(pos mgl64.Vec3) {
	delete(u.visitedBlocks, pos)
}

func (u *ACUser) InAirDuration() time.Duration {
	return time.Now().Sub(u.inAirSince)
}

func (u *ACUser) SetAirTimeSince(t time.Time) {
	u.inAirSince = t
}

func (u *ACUser) AddSpeedRate(speed float64) {
	if len(u.speedRates) >= 5 {
		u.speedRates = u.speedRates[1:]
	}

	u.speedRates = append(u.speedRates, speed)
}

func (u *ACUser) AverageSpeed() (total float64) {
	for _, x := range u.speedRates {
		total += x
	}
	return total / float64(len(u.speedRates))
}

func (u *ACUser) AddReachData(properties *ReachProperties) {
	if len(u.reachData) > 4 {
		minKey := -1
		for k := range u.reachData {
			if minKey == -1 || k < minKey {
				minKey = k
			}
		}
		delete(u.reachData, minKey)
	}

	u.reachData[ServerTick.Load()] = properties
}

func (u *ACUser) AverageReach() float64 {
	if len(u.reachData) == 0 {
		return 0
	}

	var sum float64
	for _, properties := range u.reachData {
		sum += properties.Reach
	}
	avg := sum / float64(len(u.reachData))
	return avg
}

func (u *ACUser) AddMotionFrame(frame *MotionFrame) {
	if len(u.motionBuffer) > 70 {
		minKey := -1
		for k := range u.motionBuffer {
			if minKey == -1 || k < minKey {
				minKey = k
			}
		}
		delete(u.motionBuffer, minKey)
	}

	u.motionBuffer[ServerTick.Load()] = frame
}

func (u *ACUser) RewindMotion(ticks int) *MotionFrame {
	if len(u.motionBuffer) == 0 {
		return nil
	}
	maxKey := -1
	for k := range u.motionBuffer {
		if k > maxKey {
			maxKey = k
		}
	}
	return u.motionBuffer[maxKey-ticks]
}

type ReachProperties struct {
	Reach, DeltaMotion float64
}

type MotionFrame struct {
	ServerTick int
	Position   mgl64.Vec3
	Rotation   cube.Rotation
	BB         cube.BBox
}

func (m MotionFrame) TranslatedBoundingBox() cube.BBox {
	return m.BB.Translate(m.Position)
}
