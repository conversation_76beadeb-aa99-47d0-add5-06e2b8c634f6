package core

import (
	"anticheat/cooldowns"
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"net/http"
	"strings"
	"time"
)

type Flag struct {
	Component Component
	Player    *player.Player
	Handle    *world.EntityHandle
	Ping      int
	Count     int
	FlaggedAt time.Time
	Data      map[string]interface{}
}

func NewFlag(
	component Component,
	pl *player.Player,
	data map[string]interface{},
) *Flag {
	u := LookupPlayer(pl)
	f := &Flag{
		Component: component,
		Player:    pl,
		Handle:    pl.H(),
		Ping:      int(pl.Latency().Milliseconds()),
		Count:     u.CountFlags(component) + 1,
		FlaggedAt: time.Now(),
		Data:      data,
	}

	u.RemoveExpiredFlags()
	u.AddFlag(f.Component, f)

	cooldowns.CanExecute(f.<PERSON>, cooldowns.FlagAdded, true, 1*time.Second)

	return f
}

func (f *Flag) IsExpired() bool {
	return time.Now().After(f.FlaggedAt.Add(time.Duration(Config.IgnoreFlagAfter) * time.Second))
}

func (f *Flag) Broadcast() {
	//if f.Count%Config.BroadcastFlagEvery == 0 || f.Component.Name() == "XRay" {
	go func() {
		for pl := range Server.Players(nil) {
			if (FlagViewer{}).Allow(pl) {
				pl.Messagef(f.inGameMessage())
			}
		}

		jsonData, err := json.Marshal(map[string]string{
			"content": f.discordMessage(),
		})
		if err != nil {
			panic(err)
		}

		resp, err := http.Post(Config.WebhookURL, "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			panic(err)
		}
		_ = resp.Body.Close()
	}()
	//}
}

func (f *Flag) inGameMessage() string {
	dataSlice := []string{
		text.Colourf("<dark-aqua>Flags: <grey>%v</grey></dark-aqua>", f.Count),
		text.Colourf("<dark-aqua>Ping: <grey>%v</grey></dark-aqua>", f.Ping),
	}

	for name, value := range f.Data {
		dataSlice = append(
			dataSlice,
			text.Colourf("<dark-aqua>%v: <grey>%v</grey></dark-aqua>", name, value),
		)
	}

	dataStr := text.Colourf("<dark-grey>[%v]</dark-grey>", strings.Join(dataSlice, text.Colourf("<dark-grey> | </dark-grey>")))

	return text.Colourf("%v<red>» %v</red>",
		Config.Name,
		text.Colourf(Config.FlagMessage,
			f.Player.Name(),
			f.Component.Name(),
			dataStr,
		),
	)
}

func (f *Flag) discordMessage() string {
	dataSlice := []string{
		fmt.Sprintf("**Flags:** `%v`", f.Count),
		fmt.Sprintf("**Ping:** `%v`", f.Ping),
	}

	for name, value := range f.Data {
		dataSlice = append(
			dataSlice,
			fmt.Sprintf("**%v:** `%v`", name, value),
		)
	}

	dataStr := fmt.Sprintf("[%v]", strings.Join(dataSlice, " | "))

	return fmt.Sprintf("**AntiCheat»** `%s` got flagged for `%s` %s",
		f.Player.Name(),
		f.Component.Name(),
		dataStr,
	)
}
