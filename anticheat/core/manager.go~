package core

import (
	"github.com/df-mc/atomic"
	"github.com/df-mc/dragonfly/server"
	"path"
	"server/server/anticheat/utils"
	"time"
)

var (
	Server     *server.Server
	ServerTick atomic.Value[int]
	Config     config
)

func init() {
	cfg, err := utils.ReadConfig[config](path.Join(".", "data", "anticheat_config.json"))
	if err != nil {
		panic(err)
	}
	Config = cfg

	go func() {
		for range time.NewTicker(50 * time.Millisecond).C {
			ServerTick.Store(ServerTick.Load() + 1)
		}
	}()
}

type config struct {
	Name               string
	FlagMessage        string
	IgnoreFlagAfter    int // in seconds
	BroadcastFlagEvery int
	WebhookURL         string
	Components         struct {
		AutoClicker   ComponentConfig
		ChatFilter    ComponentConfig
		ClickTeleport ComponentConfig
		Fly           ComponentConfig
		Glide         ComponentConfig
		HitBox        ComponentConfig
		InstantBreak  ComponentConfig
		Jesus         ComponentConfig
		Reach         ComponentConfig
		Scaffold      ComponentConfig
		Speed         ComponentConfig
		Xray          struct {
			Enabled         bool
			ObfuscateBelowY int
		}
	}
}

type ComponentConfig struct {
	Enabled, Counter bool
	MaxFlagsToPunish int
}
