package core

import (
	"github.com/df-mc/dragonfly/server/cmd"
)

var (
	FlagViewerFunc   func(src cmd.Source) bool
	ACInvincibleFunc func(src cmd.Source) bool
)

type FlagViewer struct {
}

func (FlagViewer) Allow(src cmd.Source) bool {
	if FlagViewerFunc == nil {
		return true
	}
	return FlagViewerFunc(src)
}

type ACInvincible struct {
}

func (ACInvincible) Allow(src cmd.Source) bool {
	if ACInvincibleFunc == nil {
		return true
	}
	return ACInvincibleFunc(src)
}
