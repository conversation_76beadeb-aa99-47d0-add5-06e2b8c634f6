package core

import (
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/skin"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/npc"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/google/uuid"
	"github.com/sandertv/gophertunnel/minecraft/protocol"
	"time"
)

func SpawnHitBoxNPC(pl *player.Player, tx *world.Tx) *player.Player {
	u := LookupPlayer(pl)
	bot := npc.Create(npc.Settings{
		//Name:       name,
		Skin:       pl.Skin(),
		Position:   pl.Position().Add(mgl64.Vec3{0, 2, 0}),
		Scale:      1,
		Vulnerable: false,
	}, tx, nil)

	//utils.WritePacket(utils.Session(pl), &packet.PlayerList{
	//	ActionType: packet.PlayerListActionAdd,
	//	Entries: []protocol.PlayerListEntry{
	//		{
	//			UUID:           pl.UUID(),
	//			EntityUniqueID: 1,
	//			Username:       name,
	//			Skin:           skinToProtocol(pl.Skin()),
	//		},
	//	},
	//})

	go func() {
		i := 0
		for range time.NewTicker(50 * time.Millisecond).C {
			i++
			dh := 1.7
			if pl.Rotation().Pitch() <= 45 && pl.Rotation().Pitch() >= -45 {
				dh = 0
			}
			bot.H().ExecWorld(func(tx *world.Tx, e world.Entity) {
				e.(*player.Player).Teleport(pl.Position().Add(pl.Rotation().Vec3().Normalize().Mul(
					-1 - dh,
				)))
				if i > 30 {
					_ = e.(*player.Player).Close()

					u.HitBoxNPC = nil
				}
			})

		}

	}()

	return bot
}

func skinToProtocol(s skin.Skin) protocol.Skin {
	var animations []protocol.SkinAnimation
	for _, animation := range s.Animations {
		protocolAnim := protocol.SkinAnimation{
			ImageWidth:  uint32(animation.Bounds().Max.X),
			ImageHeight: uint32(animation.Bounds().Max.Y),
			ImageData:   animation.Pix,
			FrameCount:  float32(animation.FrameCount),
		}
		switch animation.Type() {
		case skin.AnimationHead:
			protocolAnim.AnimationType = protocol.SkinAnimationHead
		case skin.AnimationBody32x32:
			protocolAnim.AnimationType = protocol.SkinAnimationBody32x32
		case skin.AnimationBody128x128:
			protocolAnim.AnimationType = protocol.SkinAnimationBody128x128
		}
		protocolAnim.ExpressionType = uint32(animation.AnimationExpression)
		animations = append(animations, protocolAnim)
	}

	return protocol.Skin{
		PlayFabID:                 s.PlayFabID,
		SkinID:                    uuid.New().String(),
		SkinResourcePatch:         s.ModelConfig.Encode(),
		SkinImageWidth:            uint32(s.Bounds().Max.X),
		SkinImageHeight:           uint32(s.Bounds().Max.Y),
		SkinData:                  s.Pix,
		CapeImageWidth:            uint32(s.Cape.Bounds().Max.X),
		CapeImageHeight:           uint32(s.Cape.Bounds().Max.Y),
		CapeData:                  s.Cape.Pix,
		SkinGeometry:              s.Model,
		PersonaSkin:               s.Persona,
		CapeID:                    uuid.New().String(),
		FullID:                    uuid.New().String(),
		Animations:                animations,
		Trusted:                   true,
		OverrideAppearance:        true,
		GeometryDataEngineVersion: []byte(protocol.CurrentVersion),
	}
}
