package core

import (
	"github.com/df-mc/atomic"
	"github.com/df-mc/dragonfly/server"
	"time"
)

var (
	Server     *server.Server
	ServerTick atomic.Value[int]
	Config     ACConfig
)

func init() {
	go func() {
		for range time.NewTicker(50 * time.Millisecond).C {
			ServerTick.Store(ServerTick.Load() + 1)
		}
	}()
}

type ACConfig struct {
	Name               string
	FlagMessage        string
	IgnoreFlagAfter    int // in seconds
	BroadcastFlagEvery int
	WebhookURL         string
	Components         struct {
		AutoClicker   ComponentConfig
		ChatFilter    ComponentConfig
		ClickTeleport ComponentConfig
		Fly           ComponentConfig
		Glide         ComponentConfig
		HitBox        ComponentConfig
		InstantBreak  ComponentConfig
		Jesus         ComponentConfig
		Reach         ComponentConfig
		Scaffold      ComponentConfig
		Speed         ComponentConfig
		Xray          ComponentConfig
	}
}

type ComponentConfig struct {
	Enabled, Counter bool
	MaxFlagsToPunish int

	ObfuscateBelowY int
}
