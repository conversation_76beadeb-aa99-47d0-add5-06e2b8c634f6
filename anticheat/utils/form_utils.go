package utils

import (
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/form"
	"github.com/df-mc/dragonfly/server/world"
)

var Values = make(map[*world.EntityHandle]map[string]interface{})

func Store[T any](pl *world.EntityHandle, key string, value T) {
	if Values[pl] == nil {
		Values[pl] = make(map[string]interface{})
	}
	Values[pl][key] = value
}

func Load[T any](pl *player.Player, key string) T {
	return Values[pl.H()][key].(T)
}

func AddButtonWithValue(pl *player.Player, text, image string, value interface{}) form.Button {
	Store(pl.H(), text, value)
	return form.NewButton(text, image)
}
