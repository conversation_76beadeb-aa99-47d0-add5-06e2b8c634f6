package utils

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/go-gl/mathgl/mgl64"
)

type Ray struct {
	Origin, Direction mgl64.Vec3
}

func IntersectRayBBox(ray Ray, box cube.BBox) (mgl64.Vec3, bool) {
	minimum := box.Min()
	maximum := box.Max()

	tmin := (minimum.X() - ray.Origin.X()) / ray.Direction.X()
	tmax := (maximum.X() - ray.Origin.X()) / ray.Direction.X()
	if tmin > tmax {
		tmin, tmax = tmax, tmin
	}

	tymin := (minimum.Y() - ray.Origin.Y()) / ray.Direction.Y()
	tymax := (maximum.Y() - ray.Origin.Y()) / ray.Direction.Y()
	if tymin > tymax {
		tymin, tymax = tymax, tymin
	}

	if tmin > tymax || tymin > tmax {
		return mgl64.Vec3{}, false
	}

	if tymin > tmin {
		tmin = tymin
	}
	if tymax < tmax {
		tmax = tymax
	}

	tzmin := (minimum.Z() - ray.Origin.Z()) / ray.Direction.Z()
	tzmax := (maximum.Z() - ray.Origin.Z()) / ray.Direction.Z()
	if tzmin > tzmax {
		tzmin, tzmax = tzmax, tzmin
	}

	if tmin > tzmax || tzmin > tmax {
		return mgl64.Vec3{}, false
	}

	if tzmin > tmin {
		tmin = tzmin
	}
	if tzmax < tmax {
		tmax = tzmax
	}

	if tmin < 0 && tmax < 0 {
		return mgl64.Vec3{}, false
	}

	t := tmin
	if t < 0 {
		t = tmax
	}

	return ray.Origin.Add(ray.Direction.Mul(t)), true
}
