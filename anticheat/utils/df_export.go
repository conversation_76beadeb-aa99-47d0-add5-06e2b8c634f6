package utils

import (
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/session"
	"github.com/sandertv/gophertunnel/minecraft/protocol/packet"
	_ "unsafe"
)

//go:linkname Session github.com/df-mc/dragonfly/server/player.(*Player).session
func Session(pl *player.Player) *session.Session

//go:linkname WritePacket github.com/df-mc/dragonfly/server/session.(*Session).writePacket
func WritePacket(s *session.Session, pk packet.Packet)
