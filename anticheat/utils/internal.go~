package utils

import (
	"encoding/json"
	"fmt"
	"github.com/df-mc/dragonfly/server/entity/effect"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/go-gl/mathgl/mgl64"
	"math"
	"os"
)

func ReadConfig[T any](file string) (T, error) {
	var zero T
	if _, err := os.Stat(file); os.IsNotExist(err) {
		data, err := json.Marshal(zero)
		if err != nil {
			return zero, fmt.Errorf("encode default config: %v", err)
		}
		if err := os.WriteFile(file, data, 0644); err != nil {
			return zero, fmt.Errorf("create default config: %v", err)
		}
		return zero, err
	}
	data, err := os.ReadFile(file)
	if err != nil {
		return zero, fmt.Errorf("read config: %v", err)
	}
	if err := json.Unmarshal(data, &zero); err != nil {
		return zero, fmt.Errorf("decode config: %v", err)
	}
	return zero, err
}

func Filter[T comparable](slice []T, callable func(v T) bool) []T {
	var newSlice []T

	for _, v := range slice {
		if callable(v) {
			newSlice = append(newSlice, v)
		}
	}
	return newSlice
}

func Distance(v1 mgl64.Vec3, v2 mgl64.Vec3) float64 {
	return math.Sqrt(math.Pow(v2.X()-v1.X(), 2) + math.Pow(v2.Y()-v1.Y(), 2) + math.Pow(v2.Z()-v1.Z(), 2))
}

func HasEffect(pl *player.Player, t effect.Type) bool {
	for _, e := range pl.Effects() {
		if e.Type() == t {
			return true
		}
	}
	return false
}