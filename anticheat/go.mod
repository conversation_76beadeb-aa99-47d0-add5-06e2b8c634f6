module anticheat

go 1.24.4

require (
	github.com/bedrock-gophers/intercept v0.2.4
	github.com/df-mc/atomic v1.10.0
	github.com/df-mc/dragonfly v0.10.5
	github.com/df-mc/npc v1.0.5
	github.com/go-gl/mathgl v1.2.0
	github.com/google/uuid v1.6.0
	github.com/sandertv/gophertunnel v1.48.1
)

require (
	github.com/brentp/intintmap v0.0.0-20190211203843-30dc0ade9af9 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/df-mc/goleveldb v1.1.9 // indirect
	github.com/df-mc/worldupgrader v1.0.19 // indirect
	github.com/go-jose/go-jose/v4 v4.1.1 // indirect
	github.com/golang/snappy v1.0.0 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/muhammadmuzzammil1998/jsonc v1.0.0 // indirect
	github.com/sandertv/go-raknet v1.14.3-0.20250305181847-6af3e95113d6 // indirect
	github.com/segmentio/fasthash v1.0.3 // indirect
	golang.org/x/crypto v0.40.0 // indirect
	golang.org/x/exp v0.0.0-20250718183923-645b1fa84792 // indirect
	golang.org/x/mod v0.26.0 // indirect
	golang.org/x/net v0.42.0 // indirect
	golang.org/x/oauth2 v0.30.0 // indirect
	golang.org/x/text v0.27.0 // indirect
)
