package handlers

import (
	"github.com/bedrock-gophers/intercept/intercept"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/protocol/packet"
	"server/server/anticheat/core"
	"strings"
)

func init() {
	intercept.Hook(acPacketHandler{})
}

type acPacketHandler struct {
}

func (h acPacketHandler) HandleClientPacket(ctx *intercept.Context, pk packet.Packet) {
	switch pkt := pk.(type) {
	case *packet.PlayerSkin:
		if len(pkt.Skin.SkinGeometry) > 4265 && (len(pkt.Skin.SkinGeometry)-4265) >= 78530 {
			ctx.Cancel()
		}
	case *packet.CommandRequest:
		split := strings.Split(pkt.CommandLine, " ")
		if len(split) <= 0 {
			return
		}
		split[0] = strings.ToLower(split[0])
		pkt.CommandLine = strings.Join(split, " ")

		lastArgIndex := len(pkt.CommandLine) - 1
		if lastArgIndex < 0 {
			return
		}

		if pkt.CommandLine[lastArgIndex] == ' ' {
			pkt.CommandLine = pkt.CommandLine[:lastArgIndex]
		}
	case *packet.PlayerAuthInput:
		u := core.LookupHandle(ctx.Val())
		u.AddMotionFrame(&core.MotionFrame{
			ServerTick: core.ServerTick.Load(),
			Position:   mgl64.Vec3{float64(pkt.Position.X()), float64(pkt.Position.Y()), float64(pkt.Position.Z())},
			Rotation: cube.Rotation{float64(pkt.Yaw), float64(pkt.Pitch)},
		})
	}
}

func (h acPacketHandler) HandleServerPacket(ctx *intercept.Context, pk packet.Packet) {
}
