package handlers

import (
	"anticheat/components"
	"anticheat/cooldowns"
	"anticheat/core"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/event"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"time"
)

type ACPlayerHandler struct {
	player.NopHandler
}

func (*ACPlayerHandler) HandleMove(ctx *player.Context, newPos mgl64.Vec3, newRot cube.Rotation) {
	pl := ctx.Val()
	u := core.LookupPlayer(pl)

	if pl.OnGround() || pl.Swimming() {
		u.SetAirTimeSince(time.Now())
	}

	u.SetVelocity(newPos.Sub(pl.Position()))

	if components.ComponentClickTeleport.Config().Enabled {
		components.ClickTeleportHandler{}.HandleMove(ctx, newPos, newRot)
	}

	if components.ComponentFly.Config().Enabled {
		components.FlyHandler{}.HandleMove(ctx, newPos, newRot)
	}

	if components.ComponentGlide.Config().Enabled {
		components.GlideHandler{}.HandleMove(ctx, newPos, newRot)
	}

	if components.ComponentSpeed.Config().Enabled {
		components.SpeedHandler{}.HandleMove(ctx, newPos, newRot)
	}

	if components.ComponentJesus.Config().Enabled {
		components.JesusHandler{}.HandleMove(ctx, newPos, newRot)
	}

	if components.ComponentXray.Config().Enabled {
		components.XRayHandler{}.HandleMove(ctx, newPos, newRot)
	}
}

func (*ACPlayerHandler) HandleHurt(ctx *event.Context[*player.Player], damage *float64, immune bool, attackImmunity *time.Duration, src world.DamageSource) {
	switch srct := src.(type) {
	case entity.AttackDamageSource:
		if attacker, ok := srct.Attacker.(*player.Player); ok {
			if !cooldowns.CanExecute(attacker.H(), cooldowns.NoPVP, false, 0) {
				ctx.Cancel()
				return
			}

			u := core.LookupPlayer(ctx.Val())
			u.LastHit = time.Now()
		}
	case entity.ProjectileDamageSource:
		if srct.Projectile.H().Type() == entity.EnderPearlType {
			if thrower, ok := srct.Owner.(*player.Player); ok {
				cooldowns.CanExecute(thrower.H(), cooldowns.EPearlHit, true, 1*time.Second)
			}
		}
	}
}

func (*ACPlayerHandler) HandleAttackEntity(ctx *player.Context, e world.Entity, force, height *float64, critical *bool) {

	if components.ComponentAutoClicker.Config().Enabled {
		components.AutoClickerHandler{}.HandleAttackEntity(ctx, e, force, height, critical)
	}

	if components.ComponentHitBox.Config().Enabled {
		components.HitBoxHandler{}.HandleAttackEntity(ctx, e, force, height, critical)
	}

	if components.ComponentReach.Config().Enabled {
		components.ReachHandler{}.HandleAttackEntity(ctx, e, force, height, critical)
	}
}

func (*ACPlayerHandler) HandleBlockPlace(ctx *player.Context, pos cube.Pos, b world.Block) {
	if components.ComponentScaffold.Config().Enabled {
		components.ScaffoldHandler{}.HandleBlockPlace(ctx, pos, b)
	}
}

func (*ACPlayerHandler) HandleStartBreak(ctx *player.Context, pos cube.Pos) {
	if components.ComponentXray.Config().Enabled {
		components.XRayHandler{}.HandleStartBreak(ctx, pos)
	}

	if components.ComponentInstantBreak.Config().Enabled {
		components.InstantBreakHandler{}.HandleStartBreak(ctx, pos)
	}
}
func (*ACPlayerHandler) HandleBlockBreak(ctx *player.Context, pos cube.Pos, drops *[]item.Stack, xp *int) {
	if components.ComponentXray.Config().Enabled {
		components.XRayHandler{}.HandleBlockBreak(ctx, pos, drops, xp)
	}

	if components.ComponentInstantBreak.Config().Enabled {
		components.InstantBreakHandler{}.HandleBlockBreak(ctx, pos, drops, xp)
	}
}

func (*ACPlayerHandler) HandleChat(ctx *player.Context, message *string) {
	if components.ComponentChatFilter.Config().Enabled {
		components.ChatFilterHandler{}.HandleChat(ctx, message)
	}
}
