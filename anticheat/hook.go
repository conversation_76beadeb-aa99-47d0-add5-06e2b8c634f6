package anticheat

import (
	"anticheat/commands"
	"anticheat/core"
	"anticheat/utils"
	"github.com/df-mc/dragonfly/server"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
)

func init() {
	cmd.Register(cmd.New("ac", "to spawn an NPC", []string{"npc"}, commands.ACCommand{}))
}

func HookServer(server *server.Server, configPath string) {
	core.Server = server
	cfg, err := utils.ReadConfig[core.ACConfig](configPath)
	if err != nil {
		panic(err)
	}
	core.Config = cfg
}

func HookSession(pl *player.Player) {
	if core.LookupPlayer(pl) == nil {
		if _, err := core.New(pl); err != nil {
			panic(err)
		}
	}
}

func SetPermissions(flagViewer, acInvincible func(src cmd.Source) bool) {
	core.FlagViewerFunc = flagViewer
	core.ACInvincibleFunc = acInvincible
}
