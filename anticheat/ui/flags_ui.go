package ui

import (
	"anticheat/core"
	"anticheat/utils"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/form"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"slices"
)

type FlagsUI struct{}

func (f FlagsUI) Submit(submitter form.Submitter, button form.Button, tx *world.Tx) {
	pl := submitter.(*player.Player)
	u := utils.Load[*core.ACUser](pl, button.Text)
	PlayerFlagsUI{user: u}.SendTo(pl)
}

func (f FlagsUI) Close(_ form.Submitter, _ *world.Tx) {
}

func (f FlagsUI) SendTo(pl *player.Player) {
	var users []*core.ACUser
	for _, u := range core.ACUsers {
		users = append(users, u)
	}
	slices.SortFunc[[]*core.ACUser, *core.ACUser](users, func(a, b *core.ACUser) int {
		return a.CountAllFlags() - b.Count<PERSON>ll<PERSON>lags()
	})

	fm := form.NewMenu(FlagsUI{}, text.Colourf("%v <dark-grey>Violations</dark-grey>", core.Config.Name))
	for _, u := range users {
		fm = fm.WithButtons(utils.AddButtonWithValue(pl, u.Player().Name(), "", u))
	}
	pl.SendForm(fm)
}
