package ui

import (
	"anticheat/components"
	"anticheat/core"
	"fmt"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/form"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"strings"
)

var (
	Teleport  = form.NewButton(text.Colourf("<green>Teleport to player</green>"), "")
	Exonerate = form.NewButton(text.Colourf("<red>Exonerate</red>"), "")
)

type PlayerFlagsUI struct {
	user *core.ACUser
}

func (f PlayerFlagsUI) Submit(submitter form.Submitter, button form.Button, tx *world.Tx) {
	pl := submitter.(*player.Player)
	fmt.Println(f.user)
	targetH := f.user.H()
	switch button {
	case Teleport:
		if t, ok := targetH.Entity(tx); ok {
			pl.Teleport(t.Position())
		} else {
			tx.RemoveEntity(pl)
			if !targetH.ExecWorld(func(tx *world.Tx, e world.Entity) {
				tx.AddEntity(pl.H())
				pl.Teleport(e.Position())
			}) {
				tx.AddEntity(pl.H())
				pl.Message(text.Colourf("<red>Cannot teleport to player because they are offline.</red>"))
				return
			}
		}
		pl.Message(text.Colourf("<green>Teleported.</green>"))
	case Exonerate:
		f.user.PruneFlags()
		pl.Message(text.Colourf("<green>Player exonerated.</green>"))
	}
}

func (f PlayerFlagsUI) Close(_ form.Submitter, _ *world.Tx) {
}

func (f PlayerFlagsUI) SendTo(pl *player.Player) {
	fm := form.NewMenu(PlayerFlagsUI{user: f.user}, text.Colourf("%v <dark-grey>Violations</dark-grey>", core.Config.Name))

	comps := components.AllComponents()

	var str []string
	for _, component := range comps {
		str = append(str, text.Colourf("%v<yellow>: %v</yellow>", component.Name(), f.user.CountFlags(component)))
	}
	fm = fm.WithBody(strings.Join(str, "\n"))
	fm = fm.WithButtons(Teleport, Exonerate)
	pl.SendForm(fm)
}
