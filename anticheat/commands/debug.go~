package commands

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"server/server/entities"
)

type DebugCommand struct {
}

func (d DebugCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		//u := core.LookupPlayer(pl)
		entities.NewZombie(pl.Position(), tx)
	}
}
