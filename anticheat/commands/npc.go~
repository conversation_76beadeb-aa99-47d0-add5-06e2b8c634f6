package commands

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/npc"
	"server/server/anticheat/core"
)

type NPCCommand struct {
}

func (n NPCCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		bot := npc.Create(npc.Settings{
			Name:     "Bot",
			Skin:     pl.Skin(),
			Position: pl.Position(),
			Scale:    1,
		}, tx, nil)

		_, _ = core.New(bot)
	}
}
