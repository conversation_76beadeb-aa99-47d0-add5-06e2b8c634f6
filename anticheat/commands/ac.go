package commands

import (
	"anticheat/core"
	"anticheat/ui"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
)

type ACCommand struct {
}

func (a ACCommand) Allow(src cmd.Source) bool {
	return core.FlagViewer{}.Allow(src)
}

func (a ACCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		ui.FlagsUI{}.SendTo(pl)
	}
}
