package components

import (
	"fmt"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/go-gl/mathgl/mgl64"
	"server/server/anticheat/core"
)

type Jesus struct {
}

func (c *<PERSON>) Name() string {
	return "Jesus"
}

func (c *<PERSON>) Config() core.ComponentConfig {
	return core.Config.Components.Jesus
}

func (c *<PERSON>) Flag(pl *player.Player, data map[string]interface{}) {
	f := core.NewFlag(c, pl, data)
	f.Broadcast()
	if f.Count == c.Config().MaxFlagsToPunish {
		c.<PERSON>(pl)
	}
}

func (c *<PERSON>) Counter(pl *player.Player, _ map[string]interface{}) {
	if c.Config().Counter {
		u := core.LookupPlayer(pl)
		if u.RollbackGJesusPos == (mgl64.Vec3{}) {
			pl.Teleport(pl.Position().Sub(mgl64.Vec3{0, 2, 0}))
			return
		}

		_, ok1 := pl.Tx().Block(cube.PosFromVec3(u.RollbackGJesusPos)).(block.Air)
		_, ok2 := pl.Tx().Block(cube.PosFromVec3(u.RollbackGJesusPos.Sub(mgl64.Vec3{0, 1, 0}))).(block.Water)
		if ok1 && ok2 {
			pl.Teleport(u.RollbackGJesusPos.Sub(mgl64.Vec3{0, 2, 0}))
		}
	}
}

func (c *Jesus) Punish(pl *player.Player) {
}

type JesusHandler struct {
	player.NopHandler
}

func (JesusHandler) HandleMove(ctx *player.Context, newPos mgl64.Vec3, newRot cube.Rotation) {
	pl := ctx.Val()
	u := core.LookupPlayer(pl)

	if pl.Flying() || pl.GameMode().AllowsFlying() {
		return
	}

	bl := pl.Tx().Block(cube.PosFromVec3(newPos))
	blBelow1 := pl.Tx().Block(cube.PosFromVec3(newPos.Sub(mgl64.Vec3{0, 1, 0})))
	blBelow2 := pl.Tx().Block(cube.PosFromVec3(newPos.Sub(mgl64.Vec3{0, 2, 0})))

	_, ok1 := bl.(block.Air)
	_, ok2 := blBelow1.(block.Water)
	_, ok3 := blBelow2.(block.Water)

	if !pl.Swimming() && ok1 && (ok2 || ok3) {
		u.AboveWaterTicks++
	} else {
		u.AboveWaterTicks = 0
		u.RollbackGJesusPos = newPos
	}

	if u.AboveWaterTicks == 5 && u.AverageSpeed() > 0.2 {
		ComponentJesus.Flag(pl, map[string]interface{}{
			"Speed": fmt.Sprintf("%.3f", u.AverageSpeed()),
		})
		ComponentJesus.Counter(pl, nil)
	}
}
