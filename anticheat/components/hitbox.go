package components

import (
	"anticheat/cooldowns"
	"anticheat/core"
	"anticheat/utils"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"math"
	"time"
)

type HitBox struct {
}

func (c *HitBox) Name() string {
	return "HitBox"
}

func (c *HitBox) Config() core.ComponentConfig {
	return core.Config.Components.HitBox
}

func (c *HitBox) Flag(pl *player.Player, data map[string]interface{}) {
	f := core.NewFlag(c, pl, data)
	f.Broadcast()
	if f.Count == c.Config().MaxFlagsToPunish {
		c.<PERSON>sh(pl)
	}
}

func (c *HitBox) Counter(pl *player.Player, _ map[string]interface{}) {
	if c.Config().Counter {
		cooldowns.CanExecute(pl.H(), cooldowns.NoPVP, true, time.Second)
	}
}

func (c *HitBox) Punish(pl *player.Player) {
}

type <PERSON><PERSON><PERSON><PERSON>andler struct {
	player.NopHandler
}

func (HitBoxHandler) HandleAttackEntity(ctx *player.Context, e world.Entity, force, height *float64, critical *bool) {
	pl := ctx.Val()
	u := core.LookupPlayer(pl)

	rewindTick := int(math.Ceil(float64(pl.Latency().Milliseconds())/60)) + 2
	attackerMotionFrame := u.RewindMotion(rewindTick)
	if attackerMotionFrame == nil {
		return
	}

	_, isHit := utils.IntersectRayBBox(
		utils.Ray{Origin: attackerMotionFrame.Position, Direction: attackerMotionFrame.Rotation.Vec3()},
		cube.Box(-0.3, 0, -0.3, 0.3, 1.8, 0.3).Mul(1.5).Translate(e.Position()),
	)

	if u.HitBoxNPC == nil && !isHit && utils.Distance(e.Position(), pl.Position()) > 1 {
		bot := core.SpawnHitBoxNPC(pl, pl.Tx())
		u.HitBoxNPC = bot.H()
	} else if e.H() == u.HitBoxNPC {
		ctx.Cancel()

		if !isHit {
			_ = e.Close()
			u.HitBoxNPC = nil

			ComponentHitBox.Flag(pl, nil)
			ComponentHitBox.Counter(pl, nil)
		}
	}
}
