package components

import "anticheat/core"

func init() {
	ComponentAutoClicker = &AutoClicker{}
}

var (
	ComponentAutoClicker   *AutoClicker
	ComponentChatFilter    *ChatFilter
	ComponentClickTeleport *ClickTeleport
	ComponentFly           *Fly
	ComponentGlide         *Glide
	ComponentHitBox        *HitBox
	ComponentInstantBreak  *InstantBreak
	ComponentJesus         *Jesus
	ComponentReach         *Reach
	ComponentScaffold      *Scaffold
	ComponentSpeed         *Speed
	ComponentXray          *XRay
)

func AllComponents() []core.Component {
	return []core.Component{
		ComponentAutoClicker,
		ComponentChatFilter,
		ComponentClickTeleport,
		ComponentFly,
		ComponentGlide,
		ComponentHitBox,
		ComponentInstantBreak,
		ComponentJesus,
		ComponentReach,
		ComponentScaffold,
		ComponentSpeed,
		ComponentXray,
	}
}
