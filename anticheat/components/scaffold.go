package components

import (
	"anticheat/core"
	"fmt"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/block/model"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"math"
	"time"
)

type Scaffold struct {
}

func (c *Scaffold) Name() string {
	return "Scaffold"
}

func (c *Scaffold) Config() core.ComponentConfig {
	return core.Config.Components.Scaffold
}

func (c *Scaffold) Flag(pl *player.Player, data map[string]interface{}) {
	f := core.NewFlag(c, pl, data)
	f.Broadcast()
	if f.Count == c.Config().MaxFlagsToPunish {
		c.<PERSON>unish(pl)
	}
}

func (c *Scaffold) Counter(pl *player.Player, data map[string]interface{}) {
	if c.Config().Counter {
		data["ctx"].(*player.Context).Cancel()
	}
}

func (c *Scaffold) Punish(pl *player.Player) {
}

type Scaffold<PERSON>andler struct {
	player.NopHandler
}

func (ScaffoldHandler) HandleBlockPlace(ctx *player.Context, pos cube.Pos, b world.Block) {
	pl := ctx.Val()

	if pl.GameMode() == world.GameModeCreative {
		return
	}

	validDir := pos.Vec3().Add(mgl64.Vec3{0.5, 0.5, 0.5}).Sub(pl.Position()).Normalize()
	dir := pl.Rotation().Vec3()
	angle := math.Acos(dir.Dot(validDir))
	_, bPos := firstSightedBlock(pl, 7)

	if pl.Latency() <= 150*time.Millisecond &&
		angle > 1.25 &&
		!pos.Vec3().ApproxEqual(bPos) {

		ComponentScaffold.Flag(pl, map[string]interface{}{
			"Angle deviation": fmt.Sprintf("%.2f", angle*180/math.Pi),
		})
		ComponentScaffold.Counter(pl, map[string]interface{}{
			"ctx": ctx,
		})
	}
}

func firstSightedBlock(pl *player.Player, d int) (world.Block, mgl64.Vec3) {
	for x := 0; x <= d; x++ {
		for y := 0; y <= d; y++ {
			for z := 0; z <= d; z++ {
				pos := pl.Position().Add(mgl64.Vec3{float64(x), float64(y), float64(z)})
				block := pl.Tx().Block(cube.PosFromVec3(pos))
				if _, ok := block.Model().(model.Solid); !ok {
					return block, pos
				}
			}
		}
	}
	return nil, mgl64.Vec3{}
}
