package components

import (
	"fmt"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"math"
	"server/server/anticheat/cooldowns"
	"server/server/anticheat/core"
	"server/server/anticheat/utils"
	"time"
)

type Reach struct {
}

func (c *Reach) Name() string {
	return "Reach"
}

func (c *Reach) Config() core.ComponentConfig {
	return core.Config.Components.Reach
}

func (c *Reach) Flag(pl *player.Player, data map[string]interface{}) {
	f := core.NewFlag(c, pl, data)
	f.Broadcast()
	if f.Count == c.Config().MaxFlagsToPunish {
		c.<PERSON>unish(pl)
	}
}

func (c *Reach) Counter(pl *player.Player, _ map[string]interface{}) {
	if c.Config().Counter {
		cooldowns.CanExecute(pl.H(), cooldowns.NoPVP, true, time.Second)
	}
}

func (c *Reach) Punish(pl *player.Player) {
}

type ReachHandler struct {
	player.NopHandler
}

func (ReachHandler) HandleAttackEntity(ctx *player.Context, e world.Entity, force, height *float64, critical *bool) {
	pl := ctx.Val()
	u := core.LookupPlayer(pl)

	if e, ok := e.(*player.Player); ok {
		rewindTick := int(math.Ceil(float64(pl.Latency().Milliseconds())/60)) + 2
		reachData := calculateReachData(pl, e, rewindTick)
		if reachData == nil {
			return
		}

		u.AddReachData(reachData)
		validReach := 4.25
		if u.AverageReach() > validReach {
			ComponentReach.Flag(pl, map[string]interface{}{
				"Reach distance": u.AverageReach(),
				"Extra reach":    fmt.Sprintf("%.2f", u.AverageReach()-validReach),
			})
			ComponentReach.Counter(pl, nil)
		}
	}
}

func getVectorLengthNoY(vec mgl64.Vec3) float64 {
	return math.Sqrt(vec.Sub(mgl64.Vec3{0, vec.Y(), 0}).LenSqr())
}

func calculateReachData(attacker *player.Player, entity *player.Player, rewindTick int) *core.ReachProperties {
	attackerSession := core.LookupPlayer(attacker)
	entitySession := core.LookupPlayer(entity)
	attackerMotionFrame := attackerSession.RewindMotion(rewindTick)
	if attackerMotionFrame == nil {
		return nil
	}

	entityMotionFrame := &core.MotionFrame{
		Position: entity.Position(),
		Rotation: entity.Rotation(),
		BB:       cube.Box(-0.3, 0, -0.3, 0.3, 1.8, 0.3),
	}

	hitPos, isHit := utils.IntersectRayBBox(
		utils.Ray{Origin: attackerMotionFrame.Position, Direction: attackerMotionFrame.Rotation.Vec3()},
		entityMotionFrame.TranslatedBoundingBox(),
	)

	if !isHit {
		return nil
	}

	reach := utils.Distance(hitPos, attackerMotionFrame.Position)
	deltaMotion := getVectorLengthNoY(
		attackerSession.Velocity().Sub(
			entitySession.Velocity(),
		),
	)

	return &core.ReachProperties{
		Reach:       reach,
		DeltaMotion: deltaMotion,
	}
}
