package components

import (
	"anticheat/cooldowns"
	"anticheat/core"
	"anticheat/utils"
	"fmt"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/block/model"
	"github.com/df-mc/dragonfly/server/entity/effect"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/go-gl/mathgl/mgl64"
	"time"
)

type Fly struct {
}

func (c *Fly) Name() string {
	return "Fly"
}

func (c *Fly) Config() core.ComponentConfig {
	return core.Config.Components.Fly
}

func (c *Fly) Flag(pl *player.Player, data map[string]interface{}) {
	f := core.NewFlag(c, pl, data)
	f.Broadcast()
	if f.Count == c.Config().MaxFlagsToPunish {
		c.Punish(pl)
	}
}

func (c *Fly) Counter(pl *player.Player, data map[string]interface{}) {
	if c.Config().Counter {
		pl.Teleport(data["pos"].(mgl64.Vec3))
	}
}

func (c *Fly) Punish(pl *player.Player) {
}

type FlyHandler struct {
	player.NopHandler
}

func (FlyHandler) HandleMove(ctx *player.Context, newPos mgl64.Vec3, newRot cube.Rotation) {
	pl := ctx.Val()
	u := core.LookupPlayer(pl)

	if pl.Flying() || pl.GameMode().AllowsFlying() || utils.HasEffect(pl, effect.JumpBoost) || !cooldowns.CanExecute(pl.H(), cooldowns.FlagAdded, false, 0) {
		return
	}

	direction := newPos.Sub(pl.Position())
	if u.InAirDuration() == 0 {
		u.RollbackFlyPos = pl.Position()
	}

	if direction.Y() >= 0 &&
		u.InAirDuration() > time.Second &&
		(u.LastHit.IsZero() || time.Now().Sub(u.LastHit) > time.Second) {

		for i := 0; i <= 2; i++ {
			b := pl.Tx().Block(cube.PosFromVec3(newPos.Sub(mgl64.Vec3{0, float64(i), 0})))
			if _, ok := b.Model().(model.Solid); ok {
				return
			}
		}

		rbPos := u.RollbackFlyPos
		if u.RollbackFlyPos == (mgl64.Vec3{}) {
			rbPos = pl.Position()
		}

		ComponentFly.Flag(pl, map[string]interface{}{
			"Time in air": fmt.Sprintf("%d ms", u.InAirDuration().Milliseconds()),
		})
		ComponentFly.Counter(pl, map[string]interface{}{
			"pos": rbPos,
		})
	}
}
