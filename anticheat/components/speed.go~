package components

import (
	"fmt"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity/effect"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/go-gl/mathgl/mgl64"
	"math"
	"server/server/anticheat/cooldowns"
	"server/server/anticheat/core"
	"server/server/anticheat/utils"
	"time"
)

type Speed struct {
}

func (c *Speed) Name() string {
	return "Speed"
}

func (c *Speed) Config() core.ComponentConfig {
	return core.Config.Components.Speed
}

func (c *Speed) Flag(pl *player.Player, data map[string]interface{}) {
	f := core.NewFlag(c, pl, data)
	f.Broadcast()
	if f.Count == c.Config().MaxFlagsToPunish {
		c.Punish(pl)
	}
}

func (c *Speed) Counter(pl *player.Player, data map[string]interface{}) {
	if c.Config().Counter {
		pl.Teleport(data["pos"].(mgl64.Vec3))
	}
}

func (c *Speed) Punish(pl *player.Player) {
}

type SpeedHandler struct {
	player.NopHandler
}

func (SpeedHandler) HandleMove(ctx *player.Context, newPos mgl64.Vec3, newRot cube.Rotation) {
	pl := ctx.Val()
	u := core.LookupPlayer(pl)

	if pl.GameMode().AllowsFlying() ||
		pl.Flying() ||
		utils.HasEffect(pl, effect.Speed) {
		return
	}

	speed := u.Velocity().Sub(mgl64.Vec3{0, u.Velocity().Y(), 0}).Len()
	u.AddSpeedRate(speed)

	angle := 0.0
	motion := u.Velocity()
	dir := pl.Rotation().Vec3().Normalize()
	if deno := motion.Len() * dir.Len(); deno != 0 {
		angle = math.Acos(motion.Dot(dir) / deno)
	}

	mean := u.AverageSpeed()

	cond := mean > 0.7 || pl.Speed() == 0.1 && mean > 0.5 || angle >= 1 && mean > 0.45

	if cond &&
		cooldowns.CanExecute(pl.H(), cooldowns.FlagAdded, false, 0) &&
		cooldowns.CanExecute(pl.H(), cooldowns.EPearlHit, false, 0) &&
		(u.LastHit.IsZero() || time.Now().Sub(u.LastHit) > 1*time.Second) {

		fmt.Println("Angle: ", angle)
		fmt.Println(mean > 0.7, pl.Speed() == 0.1 && mean > 0.5, angle >= 1, mean > 0.45)

		ComponentSpeed.Flag(pl, map[string]interface{}{
			"Speed": fmt.Sprintf("%.3f", mean),
		})

		if pl.Latency() <= 150*time.Millisecond {
			ComponentSpeed.Counter(pl, map[string]interface{}{
				"pos": pl.Position(),
			})
		}
	}
}
