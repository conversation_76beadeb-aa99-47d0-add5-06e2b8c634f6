package components

import (
	"fmt"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity/effect"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"server/server/anticheat/cooldowns"
	"server/server/anticheat/core"
	"server/server/anticheat/utils"
	"time"
)

type InstantBreak struct {
}

func (c *InstantBreak) Name() string {
	return "Instant Break"
}

func (c *InstantBreak) Config() core.ComponentConfig {
	return core.Config.Components.InstantBreak
}

func (c *InstantBreak) Flag(pl *player.Player, data map[string]interface{}) {
	f := core.NewFlag(c, pl, data)
	f.Broadcast()
	if f.Count == c.Config().MaxFlagsToPunish {
		c.<PERSON>(pl)
	}
}

func (c *InstantBreak) Counter(pl *player.Player, data map[string]interface{}) {
	if c.Config().Counter {
		data["ctx"].(*player.Context).Cancel()
	}
}

func (c *InstantBreak) Punish(pl *player.Player) {
}

type InstantBreakHandler struct {
	player.NopHandler
}

func (InstantBreakHandler) HandleBlockBreak(ctx *player.Context, pos cube.Pos, drops *[]item.Stack, xp *int) {
	pl := ctx.Val()
	u := core.LookupPlayer(pl)
	bl := pl.Tx().Block(pos)

	if utils.HasEffect(pl, effect.Haste) || pl.GameMode() == world.GameModeCreative {
		return
	}

	main, _ := pl.HeldItems()
	legalDur := block.BreakDuration(bl, main)
	dur := time.Now().Sub(u.LastBlockTouched[pos.Vec3()])
	cooldowns.RemoveCoolDown(u.H(), cooldowns.StartBreak)

	if diff := legalDur - dur; diff > 500*time.Millisecond {
		ComponentInstantBreak.Flag(pl, map[string]interface{}{
			"Break time difference": fmt.Sprintf("%d s", diff.Milliseconds()),
		})
		ComponentInstantBreak.Counter(pl, map[string]interface{}{
			"ctx": ctx,
		})
	}
}

func (InstantBreakHandler) HandleStartBreak(ctx *player.Context, pos cube.Pos) {
	pl := ctx.Val()
	u := core.LookupPlayer(pl)
	if cooldowns.CanExecute(u.H(), cooldowns.StartBreak, true, -1) {
		u.LastBlockTouched[pos.Vec3()] = time.Now()
	}
}
