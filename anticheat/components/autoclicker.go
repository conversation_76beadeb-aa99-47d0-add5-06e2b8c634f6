package components

import (
	"anticheat/cooldowns"
	"anticheat/core"
	"fmt"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"math"
	"time"
)

type AutoClicker struct {
}

func (c *AutoClicker) Name() string {
	return "Auto Clicker"
}

func (c *AutoClicker) Config() core.ComponentConfig {
	return core.Config.Components.AutoClicker
}

func (c *AutoClicker) Flag(pl *player.Player, data map[string]interface{}) {
	f := core.NewFlag(c, pl, data)
	f.Broadcast()
	if f.Count == c.Config().MaxFlagsToPunish {
		c.<PERSON>unish(pl)
	}
}

func (c *AutoClicker) Counter(pl *player.Player, _ map[string]interface{}) {
	if c.Config().Counter {
		cooldowns.CanExecute(pl.H(), cooldowns.NoPVP, true, 250*time.Millisecond)
	}
}

func (c *AutoClicker) Punish(pl *player.Player) {
}

type AutoClickerHandler struct {
	player.NopHandler
}

func (AutoClickerHandler) HandleAttackEntity(ctx *player.Context, e world.Entity, force, height *float64, critical *bool) {
	pl := ctx.Val()
	u := core.LookupPlayer(pl)

	if u.LastClickFrom3Sec.IsZero() {
		u.LastClickFrom3Sec = time.Now()
		u.LastClickAt = u.LastClickFrom3Sec
	} else {
		currentTime := time.Now()
		duration := time.Second
		if currentTime.Sub(u.LastClickFrom3Sec) < duration {
			u.ClickRates = append(u.ClickRates, currentTime.Sub(u.LastClickAt))
		} else {
			u.LastClickFrom3Sec = currentTime
			cps := len(u.ClickRates) / int(duration.Seconds())
			if cps > 6 {
				mad := calculateMeanAbsoluteDeviation(u.ClickRates)
				if cps >= 17 || mad <= 100 {
					ComponentAutoClicker.Flag(pl, map[string]interface{}{
						"CPS": cps,
						"SD":  fmt.Sprintf("%.2f", mad),
					})
					ComponentAutoClicker.Counter(pl, nil)
				}
			}
			u.ClickRates = []time.Duration{currentTime.Sub(u.LastClickAt)}
		}
		u.LastClickAt = currentTime
	}
}

func calculateMeanAbsoluteDeviation(data []time.Duration) float64 {
	n := float64(len(data))
	if n == 0 {
		return 0
	}

	var sum float64
	for _, x := range data {
		sum += x.Seconds()
	}
	mean := sum / n

	var num float64
	for _, x := range data {
		abs := math.Abs(float64(x) - mean)
		//if abs <= 0.1 {
		num += abs
		//}
	}

	return (num / n) / 1000000
}
