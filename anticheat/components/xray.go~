package components

import (
	"fmt"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/block/model"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"server/server/anticheat/cooldowns"
	"server/server/anticheat/core"
	"server/server/anticheat/utils"
	"time"
)

type XRay struct {
}

func (c *XRay) Name() string {
	return "XRay"
}

func (c *XRay) Config() core.ComponentConfig {
	return core.Config.Components.Xray
}

func (c *XRay) Flag(pl *player.Player, data map[string]interface{}) {
	f := core.NewFlag(c, pl, data)
	f.Broadcast()
	if f.Count == c.Config().MaxFlagsToPunish {
		c.<PERSON>unish(pl)
	}
}

func (c *XRay) Counter(pl *player.Player, data map[string]interface{}) {
	if c.Config().Counter {
		data["ctx"].(*player.Context).Cancel()
	}
}

func (c *XRay) Punish(pl *player.Player) {
}

var visitedBlocks map[mgl64.Vec3]world.Block

type XRayHandler struct {
	player.NopHandler
}

func (XRayHandler) HandleMove(ctx *player.Context, newPos mgl64.Vec3, newRot cube.Rotation) {
	pl := ctx.Val()
	u := core.LookupPlayer(pl)

	if pl.Position().Y() > float64(ComponentXray.Config().ObfuscateBelowY) {
		return
	}

	go func() {
		current := time.Now()

		if current.Sub(u.LastAntiXrayRan) <= 250*time.Millisecond {
			return
		}
		u.LastAntiXrayRan = current

		visitedBlocks = make(map[mgl64.Vec3]world.Block)
		u.H().ExecWorld(func(tx *world.Tx, e world.Entity) {
			RecursiveObfuscation(cube.PosFromVec3(pl.Position()), u, tx)
		})
	}()
}

func RecursiveObfuscation(pos cube.Pos, u *core.ACUser, tx *world.Tx) {
	if utils.Distance(u.Player().Position(), pos.Vec3()) > 5 {
		return
	}

	for _, face := range cube.Faces() {
		sp := pos.Side(face)
		sb := tx.Block(sp)

		if _, ok := sb.Model().(model.Solid); ok && u.ObfuscatedBlock(sp.Vec3()) == nil && visitedBlocks[sp.Vec3()] == nil {
			flag := true
			for _, face2 := range cube.Faces() {
				sp2 := sp.Side(face2)
				sb2 := tx.Block(sp2)

				if _, ok := sb2.Model().(model.Solid); !ok {
					flag = false
				}
			}

			if flag {
				u.ObfuscateBlock(sp.Vec3(), tx)
			}

			visitedBlocks[sp.Vec3()] = sb
			RecursiveObfuscation(sp, u, tx)
		}
	}
}

func (XRayHandler) HandleStartBreak(ctx *player.Context, pos cube.Pos) {
	pl := ctx.Val()
	u := core.LookupPlayer(pl)

	if pl.Position().Y() > float64(ComponentXray.Config().ObfuscateBelowY) {
		return
	}

	for _, face := range cube.Faces() {
		sp := pos.Side(face)
		sb := pl.Tx().Block(sp)

		if _, ok := sb.(block.Air); ok {
			continue
		}

		if b := u.ObfuscatedBlock(sp.Vec3()); b != nil {
			u.ClearObfuscatedBlock(sp.Vec3())
		}
	}
}

func (XRayHandler) HandleBlockBreak(ctx *player.Context, pos cube.Pos, drops *[]item.Stack, xp *int) {
	pl := ctx.Val()
	u := core.LookupPlayer(pl)

	for v := range u.VisitedBlocks() {
		if utils.Distance(pl.Position(), v) <= 5 {
			u.UnvisitBlock(v)
		}
	}

	b := pl.Tx().Block(pos)
	if _, ok := b.(block.DiamondOre); ok &&
		cooldowns.CanExecute(pl.H(), cooldowns.XrayFlag, true, 10*time.Second) {

		ComponentXray.Flag(pl, map[string]interface{}{
			"Diamond vein": fmt.Sprintf("%d ores", getVeinAmount(pos.Vec3(), pl.Tx())),
		})
	}
}

var visitedDiamonds map[mgl64.Vec3]world.Block

func getVeinAmount(v mgl64.Vec3, tx *world.Tx) int {
	visitedDiamonds = make(map[mgl64.Vec3]world.Block)
	return getVeinAmountHelper(v, tx)
}

func getVeinAmountHelper(v mgl64.Vec3, tx *world.Tx) int {
	pos := cube.PosFromVec3(v)
	b := tx.Block(pos)
	if _, ok := b.(block.DiamondOre); !ok {
		return 0
	}

	visitedDiamonds[v] = b

	sum := 1

	for _, face := range cube.Faces() {
		sv := pos.Side(face).Vec3()
		if _, ok := visitedDiamonds[sv]; ok {
			continue
		}
		sum += getVeinAmountHelper(sv, tx)
	}
	return sum
}
