package components

import (
	"github.com/df-mc/dragonfly/server/player"
	"server/server/anticheat/cooldowns"
	"server/server/anticheat/core"
	"strings"
	"time"
)

type ChatFilter struct {
}

func (c *ChatFilter) Name() string {
	return "Chat Filter"
}

func (c *ChatFilter) Config() core.ComponentConfig {
	return core.Config.Components.ChatFilter
}

func (c *ChatFilter) Flag(pl *player.Player, data map[string]interface{}) {
	f := core.NewFlag(c, pl, data)
	f.Broadcast()
	if f.Count == c.Config().MaxFlagsToPunish {
		c.<PERSON>(pl)
	}
}

func (c *ChatFilter) Counter(pl *player.Player, _ map[string]interface{}) {
	if c.Config().Counter {
		cooldowns.CanExecute(pl.H(), cooldowns.NoPVP, true, 250*time.Millisecond)
	}
}

func (c *ChatFilter) Punish(pl *player.Player) {
}

type ChatFilter<PERSON>andler struct {
	player.NopHandler
}

func (ChatFilterHandler) HandleChat(ctx *player.Context, message *string) {
	pl := ctx.Val()

	if strings.Contains(*message, "horion.download") {
		ComponentChatFilter.Flag(pl, map[string]interface{}{
			"Message": *message,
		})
		ComponentChatFilter.Counter(pl, nil)
	}
}
