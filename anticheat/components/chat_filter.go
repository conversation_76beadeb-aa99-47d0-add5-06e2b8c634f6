package components

import (
	"anticheat/core"
	"github.com/df-mc/dragonfly/server/player"
	"strings"
)

type ChatFilter struct {
}

func (c *ChatFilter) Name() string {
	return "Chat Filter"
}

func (c *ChatFilter) Config() core.ComponentConfig {
	return core.Config.Components.ChatFilter
}

func (c *ChatFilter) Flag(pl *player.Player, data map[string]interface{}) {
	f := core.NewFlag(c, pl, data)
	f.Broadcast()
	if f.Count == c.Config().MaxFlagsToPunish {
		c.<PERSON>unish(pl)
	}
}

func (c *ChatFilter) Counter(pl *player.Player, data map[string]interface{}) {
	if c.Config().Counter {
		data["ctx"].(*player.Context).Cancel()
	}
}

func (c *ChatFilter) Punish(pl *player.Player) {
}

type ChatFilterHandler struct {
	player.NopHandler
}

func (ChatFilterHandler) HandleChat(ctx *player.Context, message *string) {
	pl := ctx.Val()

	if strings.Contains(*message, "horion.download") {
		ComponentChatFilter.Flag(pl, map[string]interface{}{
			"Message": *message,
		})
		ComponentChatFilter.Counter(pl, map[string]interface{}{
			"ctx": ctx,
		})
	}
}
