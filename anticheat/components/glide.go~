package components

import (
	"fmt"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/go-gl/mathgl/mgl64"
	"server/server/anticheat/core"
	"time"
)

type Glide struct {
}

func (c *Glide) Name() string {
	return "Glide"
}

func (c *Glide) Config() core.ComponentConfig {
	return core.Config.Components.Glide
}

func (c *Glide) Flag(pl *player.Player, data map[string]interface{}) {
	f := core.NewFlag(c, pl, data)
	f.Broadcast()
	if f.Count == c.Config().MaxFlagsToPunish {
		c.<PERSON>sh(pl)
	}
}

func (c *Glide) Counter(pl *player.Player, data map[string]interface{}) {
	if c.Config().Counter {
		pl.Teleport(data["pos"].(mgl64.Vec3))
	}
}

func (c *Glide) Punish(pl *player.Player) {
}

type GlideHandler struct {
	player.NopHandler
}

func (GlideHandler) HandleMove(ctx *player.Context, newPos mgl64.Vec3, newRot cube.Rotation) {
	pl := ctx.Val()
	u := core.LookupPlayer(pl)

	if pl.Flying() || pl.GameMode().AllowsFlying() {
		return
	}

	direction := newPos.Sub(pl.Position())
	if u.InAirDuration() == 0 {
		u.RollbackGlidePos = pl.Position()
	}

	if direction.Y() < 0 &&
		direction.Y() > -1 &&
		u.InAirDuration() > time.Second &&
		(u.LastHit.IsZero() || time.Now().Sub(u.LastHit) > 2*time.Second) {

		rbPos := u.RollbackFlyPos
		if u.RollbackFlyPos == (mgl64.Vec3{}) {
			rbPos = pl.Position()
		}

		ComponentGlide.Flag(pl, map[string]interface{}{
			"Time in air": fmt.Sprintf("%d ms", u.InAirDuration().Milliseconds()),
			"Velocity":    fmt.Sprintf("%.2f bps", direction.Y()),
		})
		ComponentGlide.Counter(pl, map[string]interface{}{
			"pos": rbPos,
		})
	}
}
