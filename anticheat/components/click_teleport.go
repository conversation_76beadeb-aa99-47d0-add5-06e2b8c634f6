package components

import (
	"anticheat/cooldowns"
	"anticheat/core"
	"anticheat/utils"
	"fmt"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/go-gl/mathgl/mgl64"
)

type ClickTeleport struct {
}

func (c *ClickTeleport) Name() string {
	return "Click Teleport"
}

func (c *ClickTeleport) Config() core.ComponentConfig {
	return core.Config.Components.ClickTeleport
}

func (c *ClickTeleport) Flag(pl *player.Player, data map[string]interface{}) {
	f := core.NewFlag(c, pl, data)
	f.Broadcast()
	if f.Count == c.Config().MaxFlagsToPunish {
		c.<PERSON>unish(pl)
	}
}

func (c *ClickTeleport) Counter(pl *player.Player, data map[string]interface{}) {
	if c.Config().Counter {
		pl.Teleport(data["pos"].(mgl64.Vec3))
	}
}

func (c *ClickTeleport) Punish(pl *player.Player) {
}

type ClickTeleportHandler struct {
	player.NopHandler
}

func (ClickTeleportHandler) HandleMove(ctx *player.Context, newPos mgl64.Vec3, newRot cube.Rotation) {
	pl := ctx.Val()

	distance := utils.Distance(pl.Position(), newPos)

	if pl.Sneaking() &&
		distance > 3 &&
		cooldowns.CanExecute(pl.H(), cooldowns.EPearlHit, false, 0) {

		ComponentClickTeleport.Flag(pl, map[string]interface{}{
			"Distance": fmt.Sprintf("%.2f", distance),
		})
		ComponentClickTeleport.Counter(pl, map[string]interface{}{
			"pos": pl.Position(),
		})
	}
}
