#!/bin/bash

# Enhanced Firewall Setup for MassacreMC
# Implements comprehensive security rules and rate limiting

LOG_FILE="/var/log/firewall_setup.log"

log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Backup existing rules
backup_iptables() {
    log_message "Backing up existing iptables rules..."
    iptables-save > /etc/iptables/rules.backup.$(date +%Y%m%d_%H%M%S)
}

# Basic firewall setup
setup_basic_firewall() {
    log_message "Setting up basic firewall rules..."

    # Save Docker rules before clearing
    iptables-save | grep -E "(DOCKER|docker)" > /tmp/docker_rules_backup.txt

    # Clear existing rules but preserve Docker chains
    iptables -F INPUT
    iptables -F OUTPUT
    # Don't clear FORWARD chain completely as Docker uses it

    # Set default policies
    iptables -P INPUT DROP
    iptables -P FORWARD ACCEPT  # Docker needs this
    iptables -P OUTPUT ACCEPT

    # Allow loopback
    iptables -A INPUT -i lo -j ACCEPT
    iptables -A OUTPUT -o lo -j ACCEPT

    # Allow established connections
    iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT

    # Allow Docker networking
    iptables -I FORWARD -i docker0 -j ACCEPT
    iptables -I FORWARD -o docker0 -j ACCEPT
}

# Web server protection
setup_web_protection() {
    log_message "Setting up web server protection..."
    
    # Allow HTTP/HTTPS with rate limiting
    iptables -A INPUT -p tcp --dport 80 -m connlimit --connlimit-above 20 -j DROP
    iptables -A INPUT -p tcp --dport 443 -m connlimit --connlimit-above 20 -j DROP
    
    # Rate limit HTTP requests (max 25 per minute per IP)
    iptables -A INPUT -p tcp --dport 80 -m recent --set --name HTTP
    iptables -A INPUT -p tcp --dport 80 -m recent --update --seconds 60 --hitcount 25 --name HTTP -j DROP
    iptables -A INPUT -p tcp --dport 80 -j ACCEPT
    
    # Rate limit HTTPS requests (max 50 per minute per IP)
    iptables -A INPUT -p tcp --dport 443 -m recent --set --name HTTPS
    iptables -A INPUT -p tcp --dport 443 -m recent --update --seconds 60 --hitcount 50 --name HTTPS -j DROP
    iptables -A INPUT -p tcp --dport 443 -j ACCEPT
}

# Minecraft server protection
setup_minecraft_protection() {
    log_message "Setting up Minecraft server protection..."
    
    # Allow Minecraft with connection limits
    iptables -A INPUT -p udp --dport 19132 -m connlimit --connlimit-above 100 -j DROP
    iptables -A INPUT -p udp --dport 19132 -m recent --set --name MINECRAFT
    iptables -A INPUT -p udp --dport 19132 -m recent --update --seconds 10 --hitcount 10 --name MINECRAFT -j DROP
    iptables -A INPUT -p udp --dport 19132 -j ACCEPT
}

# SSH protection
setup_ssh_protection() {
    log_message "Setting up SSH protection..."
    
    # SSH rate limiting (max 3 attempts per minute)
    iptables -A INPUT -p tcp --dport 22 -m recent --set --name SSH
    iptables -A INPUT -p tcp --dport 22 -m recent --update --seconds 60 --hitcount 3 --name SSH -j DROP
    iptables -A INPUT -p tcp --dport 22 -j ACCEPT
}

# Block known malicious patterns
setup_malicious_blocking() {
    log_message "Setting up malicious traffic blocking..."
    
    # Block known botnet IPs (add more as needed)
    iptables -A INPUT -s ************* -j DROP
    iptables -A INPUT -s ************* -j DROP
    
    # Block common attack ports
    iptables -A INPUT -p tcp --dport 23 -j DROP    # Telnet
    iptables -A INPUT -p tcp --dport 135 -j DROP   # RPC
    iptables -A INPUT -p tcp --dport 139 -j DROP   # NetBIOS
    iptables -A INPUT -p tcp --dport 445 -j DROP   # SMB
    iptables -A INPUT -p tcp --dport 1433 -j DROP  # MSSQL
    iptables -A INPUT -p tcp --dport 3389 -j DROP  # RDP
    
    # Block invalid packets
    iptables -A INPUT -m conntrack --ctstate INVALID -j DROP
    
    # Block NULL packets
    iptables -A INPUT -p tcp --tcp-flags ALL NONE -j DROP
    
    # Block SYN flood attacks
    iptables -A INPUT -p tcp ! --syn -m conntrack --ctstate NEW -j DROP
    
    # Block XMAS packets
    iptables -A INPUT -p tcp --tcp-flags ALL ALL -j DROP
}

# Geographic blocking (optional)
setup_geo_blocking() {
    log_message "Setting up geographic blocking..."
    
    # Block traffic from high-risk countries (customize as needed)
    # This requires xtables-addons package
    if command -v xtables-addons-common >/dev/null 2>&1; then
        # Example: Block traffic from specific countries
        # iptables -A INPUT -m geoip --src-cc CN,RU,KP -j DROP
        log_message "Geographic blocking available but not configured"
    else
        log_message "Geographic blocking not available (xtables-addons not installed)"
    fi
}

# Docker networking support
setup_docker_networking() {
    log_message "Setting up Docker networking support..."

    # Allow Docker bridge networks
    iptables -I FORWARD -s **********/16 -j ACCEPT
    iptables -I FORWARD -d **********/16 -j ACCEPT
    iptables -I FORWARD -s **********/16 -j ACCEPT
    iptables -I FORWARD -d **********/16 -j ACCEPT

    # Allow Docker container outbound connections
    iptables -I FORWARD -i docker0 -o eth0 -j ACCEPT
    iptables -I FORWARD -i eth0 -o docker0 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT

    # Allow DNS for containers
    iptables -I OUTPUT -p udp --dport 53 -j ACCEPT
    iptables -I OUTPUT -p tcp --dport 53 -j ACCEPT

    # Allow container registry access
    iptables -I OUTPUT -p tcp --dport 443 -j ACCEPT
    iptables -I OUTPUT -p tcp --dport 80 -j ACCEPT
}

# DDoS protection
setup_ddos_protection() {
    log_message "Setting up DDoS protection..."

    # Limit concurrent connections per IP
    iptables -A INPUT -p tcp -m connlimit --connlimit-above 50 -j REJECT --reject-with tcp-reset

    # Protect against ping floods
    iptables -A INPUT -p icmp --icmp-type echo-request -m limit --limit 1/s --limit-burst 2 -j ACCEPT
    iptables -A INPUT -p icmp --icmp-type echo-request -j DROP

    # Protect against port scans
    iptables -A INPUT -m recent --name portscan --rcheck --seconds 86400 -j DROP
    iptables -A INPUT -m recent --name portscan --remove
    iptables -A INPUT -p tcp -m tcp --dport 139 -m recent --name portscan --set -j LOG --log-prefix "portscan:"
    iptables -A INPUT -p tcp -m tcp --dport 139 -m recent --name portscan --set -j DROP
}

# Save rules
save_rules() {
    log_message "Saving iptables rules..."
    
    # Save rules (method depends on distribution)
    if command -v iptables-save >/dev/null 2>&1; then
        iptables-save > /etc/iptables/rules.v4
        log_message "Rules saved to /etc/iptables/rules.v4"
    fi
    
    # For systemd systems
    if command -v systemctl >/dev/null 2>&1; then
        systemctl enable iptables
        log_message "iptables service enabled"
    fi
}

# Main setup function
main() {
    log_message "Starting enhanced firewall setup..."
    
    # Check if running as root
    if [ "$EUID" -ne 0 ]; then
        echo "This script must be run as root"
        exit 1
    fi
    
    backup_iptables
    setup_basic_firewall
    setup_docker_networking
    setup_web_protection
    setup_minecraft_protection
    setup_ssh_protection
    setup_malicious_blocking
    setup_geo_blocking
    setup_ddos_protection
    save_rules
    
    log_message "Enhanced firewall setup completed"
    
    # Display current rules
    echo "Current iptables rules:"
    iptables -L -n -v
}

# Run main function
main "$@"
