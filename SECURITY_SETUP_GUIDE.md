# MassacreMC Security Setup Guide

## Quick Deployment

### 1. **One-Command Setup** (Recommended)
```bash
sudo ./deploy_security.sh
```
This will automatically:
- Install all security tools
- Configure enhanced firewall
- Set up real-time monitoring
- Enable automated vulnerability audits
- Configure intrusion prevention
- Create security dashboard

### 2. **Manual Step-by-Step Setup**

#### A. Enhanced Firewall
```bash
sudo ./firewall_setup.sh
```
**Features:**
- Rate limiting for HTTP/HTTPS
- Minecraft server protection
- SSH brute-force protection
- Malicious traffic blocking
- DDoS protection

#### B. Security Monitoring
```bash
sudo ./security_monitoring_system.sh init
sudo ./security_monitoring_system.sh monitor &
```
**Features:**
- Real-time threat detection
- Automatic IP blocking
- System resource monitoring
- File integrity checking
- Email alerts

#### C. Vulnerability Auditing
```bash
sudo ./vulnerability_audit.sh install
sudo ./vulnerability_audit.sh full
```
**Features:**
- System vulnerability scanning
- Web application security testing
- Network security assessment
- Malware detection
- Automated reporting

## Security Features Implemented

### 🛡️ **Firewall Protection**
- **Port Security:** Only essential ports open (22, 80, 443, 19132)
- **Rate Limiting:** Max 25 HTTP, 50 HTTPS requests per minute per IP
- **Connection Limits:** Max 20 concurrent connections per IP
- **Attack Prevention:** Blocks NULL packets, SYN floods, XMAS attacks
- **Geographic Blocking:** Ready for country-based blocking

### 🔍 **Real-Time Monitoring**
- **Web Attack Detection:** SQL injection, XSS, malicious patterns
- **Botnet Detection:** Identifies urbotnet and similar threats
- **Resource Monitoring:** CPU, memory, disk usage alerts
- **Network Monitoring:** Suspicious connections and port scans
- **File Integrity:** Critical system file monitoring

### 🚨 **Intrusion Prevention (Fail2Ban)**
- **SSH Protection:** 3 failed attempts = 1 hour ban
- **Web Protection:** Malicious requests = 24 hour ban
- **Bot Protection:** Bad bots permanently banned
- **Custom Filters:** Detects IoT exploits and malware attempts

### 📊 **Vulnerability Auditing**
- **Daily Scans:** Automated security assessments
- **Package Updates:** Identifies outdated software
- **Web Security:** Nikto and Nmap vulnerability scans
- **Malware Detection:** ClamAV and rootkit scanning
- **Compliance Reports:** HTML reports with recommendations

## Management Commands

### **Firewall Management**
```bash
# View current rules
sudo iptables -L -n -v

# Block specific IP
sudo iptables -A INPUT -s ******* -j DROP

# Save rules
sudo iptables-save > /etc/iptables/rules.v4
```

### **Monitoring Management**
```bash
# Check monitoring status
sudo systemctl status massacremc-security-monitor

# View security logs
tail -f /var/log/security/security_monitor.log

# Check blocked IPs
./security_monitoring_system.sh status

# Unblock IP
./security_monitoring_system.sh unblock *******
```

### **Fail2Ban Management**
```bash
# Check status
sudo fail2ban-client status

# Check specific jail
sudo fail2ban-client status apache-malicious

# Unban IP
sudo fail2ban-client set apache-malicious unbanip *******
```

### **Vulnerability Auditing**
```bash
# Run full audit
sudo ./vulnerability_audit.sh full

# Run specific scans
sudo ./vulnerability_audit.sh system
sudo ./vulnerability_audit.sh web
sudo ./vulnerability_audit.sh malware

# View latest report
ls -la /var/log/security/reports/
```

## Security Dashboard

Access the web-based security dashboard at:
```
https://your-domain.com/security/
```

**Default Password:** `SecurePassword123!`
**⚠️ IMPORTANT:** Change the password in `/var/www/html/security/index.php`

### Dashboard Features:
- Real-time security metrics
- Blocked IP count
- System resource usage
- Recent security events
- Service status monitoring

## Log Files

### **Security Logs**
- Main log: `/var/log/security/security_monitor.log`
- Threats: `/var/log/security/threats.log`
- Audits: `/var/log/security/audits/`
- Reports: `/var/log/security/reports/`

### **System Logs**
- Apache access: `/var/log/apache2/access.log`
- Apache errors: `/var/log/apache2/error.log`
- Fail2Ban: `/var/log/fail2ban.log`
- Firewall: `/var/log/kern.log`

## Automated Tasks

### **Cron Jobs Created**
```bash
# Daily vulnerability audit at 2 AM
0 2 * * * root /path/to/vulnerability_audit.sh full

# Weekly comprehensive scan on Sundays at 3 AM
0 3 * * 0 root /path/to/vulnerability_audit.sh full
```

### **Systemd Services**
- `massacremc-security-monitor.service` - Real-time monitoring
- `fail2ban.service` - Intrusion prevention
- `iptables.service` - Firewall rules

## Customization

### **Email Alerts**
Edit these files to configure email notifications:
- `/etc/massacremc/security/alert_config.conf`
- `/etc/fail2ban/jail.local`
- `/etc/logwatch/conf/logwatch.conf`

### **Firewall Rules**
Modify `firewall_setup.sh` to add custom rules:
```bash
# Example: Allow specific service
iptables -A INPUT -p tcp --dport 8080 -j ACCEPT
```

### **Monitoring Thresholds**
Edit `/etc/massacremc/security/alert_config.conf`:
```bash
MAX_404_PER_MINUTE=20
MAX_CONNECTIONS_PER_IP=50
MAX_FAILED_LOGINS=5
```

## Troubleshooting

### **Common Issues**

1. **Service won't start:**
   ```bash
   sudo systemctl status massacremc-security-monitor
   sudo journalctl -u massacremc-security-monitor
   ```

2. **Firewall blocking legitimate traffic:**
   ```bash
   # Temporarily disable
   sudo iptables -F
   # Or add exception
   sudo iptables -I INPUT -s trusted.ip.address -j ACCEPT
   ```

3. **False positive blocks:**
   ```bash
   # Unblock IP
   ./security_monitoring_system.sh unblock *******
   # Add to whitelist
   echo "*******" >> /etc/massacremc/security/whitelist.txt
   ```

### **Emergency Access**
If locked out, access via console and run:
```bash
sudo iptables -F  # Clear all rules
sudo systemctl stop fail2ban
```

## Security Best Practices

1. **Regular Updates:** Keep all software updated
2. **Strong Passwords:** Use complex passwords for all accounts
3. **Key-based SSH:** Disable password authentication for SSH
4. **Regular Audits:** Review security logs weekly
5. **Backup Security:** Secure your backup systems
6. **Incident Response:** Have a plan for security incidents

## Support

For issues or questions:
- Check logs in `/var/log/security/`
- Review this guide
- Contact system administrator

---
**Last Updated:** $(date)
**Version:** 1.0
