<?php
/**
 * API Client for Go API Integration
 * 
 * Provides functions to interact with the Go API endpoints
 */

class ApiClient {
    private $base_url;
    private $jwt_token;
    private $timeout;

    public function __construct() {
        // Load environment configuration
        require_once __DIR__ . '/config_parser.php';
        
        try {
            $env = load_env_config([
                '/etc/massacremc/config/api.env',
                '/etc/massacremc/config/admin.env'
            ]);
        } catch (Exception $e) {
            secure_log("API Client: Failed to load environment config: " . $e->getMessage());
            $env = [];
        }

        // Set API configuration
        // Default to HTTP for local/internal development to avoid TLS issues
        $this->base_url = $env['API_BASE_URL'] ?? 'http://localhost:8080';
        $this->jwt_token = $env['SECRET_KEY'] ?? null; // Use SECRET_KEY from environment
        if (!$this->jwt_token) {
            $is_production = getenv('APP_ENV') === 'production';
            if ($is_production) {
                throw new Exception('SECRET_KEY not configured');
            }
            secure_log("API Client: SECRET_KEY not configured; using temporary dev token", "warning");
            $this->jwt_token = bin2hex(random_bytes(16));
        }
        $this->timeout = intval($env['API_TIMEOUT'] ?? '30');
    }

    /**
     * Make a GET request to the API
     */
    private function makeRequest($endpoint, $method = 'GET', $data = null) {
        $url = rtrim($this->base_url, '/') . '/' . ltrim($endpoint, '/');
        $scheme = parse_url($url, PHP_URL_SCHEME) ?: 'http';
        $host = parse_url($url, PHP_URL_HOST) ?: '';

        $context_options = [
            'http' => [
                'method' => $method,
                'header' => [
                    'Authorization: Bearer ' . $this->jwt_token,
                    'Content-Type: application/json',
                    'User-Agent: MassacreMC-Website/1.0'
                ],
                'timeout' => $this->timeout,
                'ignore_errors' => true
            ]
        ];
        // Only attach SSL options for HTTPS
        if ($scheme === 'https') {
            $isInternal = (strpos($host, '.') === false) || preg_match('/^(localhost|127\.0\.0\.1|production-servers-\d+|servers-\d+)$/i', $host);
            $allowInsecure = $isInternal && (getenv('API_INSECURE_INTERNAL_TLS') === false || strtolower((string)getenv('API_INSECURE_INTERNAL_TLS')) !== 'false');
            if ($allowInsecure) {
                // For internal hosts, allow self-signed/insecure TLS unless explicitly disabled
                $context_options['ssl'] = [
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                ];
            } else {
                $context_options['ssl'] = [
                    'verify_peer' => true,
                    'verify_peer_name' => true,
                    'allow_self_signed' => false
                ];
            }
        }

        if ($data && in_array($method, ['POST','PUT','PATCH'], true)) {
            $context_options['http']['content'] = json_encode($data);
        }

        $context = stream_context_create($context_options);
        secure_log("API Client: Making {$method} request to {$url}");
        $response = @file_get_contents($url, false, $context);


        if ($response === false) {
            $error = error_get_last();
            secure_log("API Client: Request failed - " . ($error['message'] ?? 'Unknown error'));
            throw new Exception('API request failed: ' . ($error['message'] ?? 'Unknown error'));
        }

        // Check HTTP response code
        if (isset($http_response_header)) {
            $status_line = $http_response_header[0] ?? '';
            preg_match('/HTTP\/\d\.\d\s+(\d+)/', $status_line, $matches);
            $status_code = isset($matches[1]) ? intval($matches[1]) : 200;
            if ($status_code >= 400) {
                secure_log("API Client: HTTP {$status_code} error for {$url}");
                throw new Exception("API request failed with HTTP {$status_code}");
            }
        }

        $decoded = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            secure_log("API Client: JSON decode error - " . json_last_error_msg());
            throw new Exception('Invalid JSON response from API');
        }

        return $decoded;
    }


    /**
     * Search for a player by username
     * First converts username to UUID via database, then fetches data from API
     */
    public function searchPlayer($username) {
        try {
            if (empty($username)) {
                return ['error' => 'Username is required', 'status' => 400];
            }

            // First, get the UUID from the database (this doesn't call the API)
            require_once __DIR__ . '/db_access.php';
            $db = new DatabaseAccess();
            secure_log("API Client: Looking up UUID for username: {$username}");
            $uuid_result = $db->get_player_uuid($username);
            secure_log("API Client: UUID lookup result: " . json_encode($uuid_result));

            if (isset($uuid_result['error'])) {
                secure_log("API Client: Failed to get UUID for username {$username}: " . $uuid_result['error']);
                return $uuid_result; // Return the error from database lookup
            }

            if (!isset($uuid_result['uuid'])) {
                secure_log("API Client: No UUID found for username {$username}");
                return ['error' => 'Player not found', 'status' => 404];
            }

            // Make a single API call to get the player data using the UUID
            $uuid = $uuid_result['uuid'];
            secure_log("API Client: About to call Go API with UUID: {$uuid} for username: {$username}");
            $endpoint = 'api/players/' . urlencode($uuid);
            secure_log("API Client: Full endpoint URL: {$endpoint}");
            $response = $this->makeRequest($endpoint);

            if (isset($response['data'])) {
                secure_log("API Client: Successfully found player data for username {$username} (UUID: {$uuid_result['uuid']})");
                return $response['data'];
            } else {
                secure_log("API Client: No data field in response for UUID {$uuid_result['uuid']}");
                return ['error' => 'Invalid response format from API', 'status' => 500];
            }

        } catch (Exception $e) {
            secure_log("API Client: Error searching for player {$username}: " . $e->getMessage());
            return ['error' => 'Failed to search for player: ' . $e->getMessage(), 'status' => 500];
        }
    }

    /**
     * Get player data from Go API
     */
    public function getPlayerData($uuid) {
        try {
            if (empty($uuid)) {
                return ['error' => 'UUID is required', 'status' => 400];
            }

            $endpoint = 'api/players/' . urlencode($uuid);
            $response = $this->makeRequest($endpoint);

            if (isset($response['data'])) {
                secure_log("API Client: Successfully retrieved player data for UUID {$uuid}");
                return $response['data'];
            } else {
                secure_log("API Client: No data field in response for UUID {$uuid}");
                return ['error' => 'Invalid response format from API', 'status' => 500];
            }

        } catch (Exception $e) {
            secure_log("API Client: Error getting player data for UUID {$uuid}: " . $e->getMessage());
            return ['error' => 'Failed to retrieve player data: ' . $e->getMessage(), 'status' => 500];
        }
    }

    /**
     * Get faction data from Go API
     */
    public function getFactionData($factionName) {
        try {
            if (empty($factionName)) {
                return ['error' => 'Faction name is required', 'status' => 400];
            }

            $endpoint = 'api/factions/' . urlencode($factionName);
            $response = $this->makeRequest($endpoint);

            if (isset($response['data'])) {
                secure_log("API Client: Successfully retrieved faction data for {$factionName}");
                return $response['data'];
            } else {
                secure_log("API Client: No data field in faction response for {$factionName}");
                return ['error' => 'Faction not found', 'status' => 404];
            }

        } catch (Exception $e) {
            secure_log("API Client: Error getting faction data for {$factionName}: " . $e->getMessage());
            return ['error' => 'Failed to retrieve faction data: ' . $e->getMessage(), 'status' => 500];
        }
    }

    /**
     * Update player faction data in Go API to clear faction information
     */
    public function updatePlayerFactionData($playerUuid, $clearFaction = true) {
        try {
            if (empty($playerUuid)) {
                return ['error' => 'Player UUID is required', 'status' => 400];
            }

            // Try multiple approaches to update player faction data
            $success = false;
            $lastError = '';

            // Approach 1: Use dedicated faction update endpoint
            try {
                $updateData = [
                    'faction' => [
                        'Name' => '',
                        'Role' => 0,
                        'Request' => null
                    ]
                ];
                $endpoint = 'api/players/' . urlencode($playerUuid) . '/faction';
                $response = $this->makeRequest($endpoint, 'PUT', $updateData);
                $success = true;
                secure_log("API Client: Successfully updated faction data for player {$playerUuid} via faction endpoint");
            } catch (Exception $e) {
                $lastError = $e->getMessage();
                secure_log("API Client: Faction endpoint failed for player {$playerUuid}: " . $lastError);
            }

            // Approach 2: Use general player update endpoint
            if (!$success) {
                try {
                    $updateData = [
                        'Faction' => [
                            'Name' => '',
                            'Role' => 0,
                            'Request' => null
                        ]
                    ];
                    $endpoint = 'api/players/' . urlencode($playerUuid);
                    $response = $this->makeRequest($endpoint, 'PATCH', $updateData);
                    $success = true;
                    secure_log("API Client: Successfully updated faction data for player {$playerUuid} via player endpoint");
                } catch (Exception $e) {
                    $lastError = $e->getMessage();
                    secure_log("API Client: Player endpoint failed for player {$playerUuid}: " . $lastError);
                }
            }

            // Approach 3: Use admin command to force update
            if (!$success) {
                try {
                    $commandData = [
                        'command' => 'player_update_faction',
                        'parameters' => [
                            'player_uuid' => $playerUuid,
                            'faction_name' => '',
                            'faction_role' => 0,
                            'admin_forced' => true
                        ]
                    ];
                    $endpoint = 'api/admin/execute';
                    $response = $this->makeRequest($endpoint, 'POST', $commandData);
                    $success = true;
                    secure_log("API Client: Successfully updated faction data for player {$playerUuid} via admin command");
                } catch (Exception $e) {
                    $lastError = $e->getMessage();
                    secure_log("API Client: Admin command failed for player {$playerUuid}: " . $lastError);
                }
            }

            if ($success) {
                return ['success' => true, 'data' => $response ?? null];
            } else {
                return ['error' => 'All update approaches failed: ' . $lastError, 'status' => 500];
            }

        } catch (Exception $e) {
            secure_log("API Client: Error updating player faction data for {$playerUuid}: " . $e->getMessage());
            return ['error' => 'Failed to update player faction data: ' . $e->getMessage(), 'status' => 500];
        }
    }

    /**
     * Notify Go API about faction deletion to properly disband the faction
     * Also updates all faction members' player data
     */
    public function notifyFactionDeletion($factionName) {
        try {
            if (empty($factionName)) {
                return ['error' => 'Faction name is required', 'status' => 400];
            }

            // First, get all faction members before deletion
            $factionMembers = [];
            try {
                $factionData = $this->getFactionData($factionName);
                if (isset($factionData['Members']) && is_array($factionData['Members'])) {
                    $factionMembers = $factionData['Members'];
                    secure_log("API Client: Found " . count($factionMembers) . " members in faction {$factionName}");
                }
            } catch (Exception $e) {
                secure_log("API Client: Could not get faction members before deletion: " . $e->getMessage());
            }

            // Try multiple approaches to ensure faction is properly disbanded
            $success = false;
            $warnings = [];

            // Approach 1: Try proper faction disbanding endpoint (matches Go code logic)
            try {
                $disbandEndpoint = 'api/admin/factions/disband';
                $disbandData = [
                    'faction_name' => $factionName,
                    'admin_action' => true,
                    'reason' => 'Admin deletion'
                ];
                $response = $this->makeRequest($disbandEndpoint, 'POST', $disbandData);
                secure_log("API Client: Successfully sent faction disband request for {$factionName}");
                $success = true;
            } catch (Exception $e) {
                secure_log("API Client: Faction disband request failed for {$factionName}: " . $e->getMessage());
                $warnings[] = "Disband request failed: " . $e->getMessage();
            }

            // Approach 2: Try faction management endpoint
            if (!$success) {
                try {
                    $manageEndpoint = 'api/factions/manage';
                    $manageData = [
                        'action' => 'disband',
                        'faction_name' => $factionName,
                        'admin_override' => true
                    ];
                    $response = $this->makeRequest($manageEndpoint, 'POST', $manageData);
                    secure_log("API Client: Successfully sent faction management disband for {$factionName}");
                    $success = true;
                } catch (Exception $e) {
                    secure_log("API Client: Faction management disband failed for {$factionName}: " . $e->getMessage());
                    $warnings[] = "Management disband failed: " . $e->getMessage();
                }
            }

            // Approach 3: Try server command execution (triggers the exact Go code logic)
            if (!$success) {
                try {
                    $commandEndpoint = 'api/admin/execute';
                    $commandData = [
                        'command' => 'faction_disband',
                        'parameters' => [
                            'faction_name' => $factionName,
                            'admin_forced' => true
                        ]
                    ];
                    $response = $this->makeRequest($commandEndpoint, 'POST', $commandData);
                    secure_log("API Client: Successfully executed faction disband command for {$factionName}");
                    $success = true;
                } catch (Exception $e) {
                    secure_log("API Client: Command execution failed for faction {$factionName}: " . $e->getMessage());
                    $warnings[] = "Command execution failed: " . $e->getMessage();
                }
            }

            // Approach 4: Try direct DELETE request to faction endpoint
            if (!$success) {
                try {
                    $deleteEndpoint = 'api/factions/' . urlencode($factionName);
                    $response = $this->makeRequest($deleteEndpoint, 'DELETE');
                    secure_log("API Client: Successfully sent DELETE request for faction {$factionName}");
                    $success = true;
                } catch (Exception $e) {
                    secure_log("API Client: DELETE request failed for faction {$factionName}: " . $e->getMessage());
                    $warnings[] = "DELETE request failed: " . $e->getMessage();
                }
            }

            // Final verification: Check if faction still exists
            try {
                $checkEndpoint = 'api/factions/' . urlencode($factionName);
                $checkResponse = $this->makeRequest($checkEndpoint, 'GET');
                if (isset($checkResponse['data']) && !empty($checkResponse['data'])) {
                    secure_log("API Client: Warning - Faction {$factionName} still exists in Go API after deletion attempts");
                    $warnings[] = "Faction still exists in Go API";
                } else {
                    secure_log("API Client: Confirmed - Faction {$factionName} no longer exists in Go API");
                    $success = true;
                }
            } catch (Exception $e) {
                // If GET fails with 404, that's actually good - faction is deleted
                if (strpos($e->getMessage(), '404') !== false) {
                    secure_log("API Client: Faction {$factionName} not found in Go API (confirmed deleted)");
                    $success = true;
                } else {
                    secure_log("API Client: Error checking faction existence: " . $e->getMessage());
                    $warnings[] = "Could not verify deletion: " . $e->getMessage();
                }
            }

            // If faction deletion was successful, update all member player data
            if ($success && !empty($factionMembers)) {
                secure_log("API Client: Updating player faction data for " . count($factionMembers) . " members");
                $playerUpdateResults = [];

                foreach ($factionMembers as $member) {
                    if (isset($member['UUID']) && !empty($member['UUID'])) {
                        $playerResult = $this->updatePlayerFactionData($member['UUID']);
                        $playerUpdateResults[] = [
                            'uuid' => $member['UUID'],
                            'username' => $member['Username'] ?? 'unknown',
                            'result' => $playerResult
                        ];

                        if (isset($playerResult['error'])) {
                            $warnings[] = "Failed to update player {$member['Username']}: " . $playerResult['error'];
                        } else {
                            secure_log("API Client: Successfully updated faction data for player {$member['Username']} ({$member['UUID']})");
                        }
                    }
                }
            }

            if ($success) {
                $result = ['success' => true];
                if (!empty($warnings)) {
                    $result['warnings'] = $warnings;
                }
                if (!empty($playerUpdateResults)) {
                    $result['player_updates'] = $playerUpdateResults;
                }
                return $result;
            } else {
                return [
                    'error' => 'All deletion approaches failed',
                    'warnings' => $warnings,
                    'status' => 500
                ];
            }

        } catch (Exception $e) {
            secure_log("API Client: Error notifying faction deletion for {$factionName}: " . $e->getMessage());
            return ['error' => 'Failed to notify API: ' . $e->getMessage(), 'status' => 500];
        }
    }

    /**
     * Test API connection
     */
    public function testConnection() {
        try {
            // Try a simple endpoint to test connectivity
            $response = $this->makeRequest('api/health');
            return ['success' => true, 'response' => $response];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Search for players with partial matching
     *
     * @param string $query Search query
     * @param int $limit Maximum number of results
     * @return array Array of matching players
     */
    public function searchPlayersPartial($query, $limit = 5) {
        try {
            $endpoint = 'api/players/search';
            $params = [
                'query' => $query,
                'partial' => true,
                'limit' => $limit
            ];

            $url_params = http_build_query($params);
            $response = $this->makeRequest($endpoint . '?' . $url_params);

            if (isset($response['players']) && is_array($response['players'])) {
                $results = [];
                foreach ($response['players'] as $player) {
                    $results[] = [
                        'username' => $player['username'] ?? $player['Username'] ?? 'Unknown',
                        'uuid' => $player['uuid'] ?? $player['UUID'] ?? '',
                        'known_names' => $player['known_names'] ?? $player['KnownNames'] ?? [],
                        'last_seen' => $player['last_seen'] ?? $player['LastSeen'] ?? null
                    ];
                }
                return $results;
            }

            return [];

        } catch (Exception $e) {
            secure_log("API Client: Error in searchPlayersPartial: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Search for factions with partial matching
     *
     * @param string $query Search query
     * @param int $limit Maximum number of results
     * @return array Array of matching factions
     */
    public function searchFactionsPartial($query, $limit = 5) {
        try {
            $endpoint = 'api/factions/search';
            $params = [
                'query' => $query,
                'partial' => true,
                'limit' => $limit
            ];

            $url_params = http_build_query($params);
            $response = $this->makeRequest($endpoint . '?' . $url_params);

            if (isset($response['factions']) && is_array($response['factions'])) {
                $results = [];
                foreach ($response['factions'] as $faction) {
                    $results[] = [
                        'faction_name' => $faction['name'] ?? $faction['Name'] ?? 'Unknown',
                        'leader' => $faction['leader'] ?? $faction['Leader'] ?? 'Unknown',
                        'member_count' => $faction['member_count'] ?? $faction['MemberCount'] ?? 0,
                        'created_at' => $faction['created_at'] ?? $faction['CreatedAt'] ?? null
                    ];
                }
                return $results;
            }

            return [];

        } catch (Exception $e) {
            secure_log("API Client: Error in searchFactionsPartial: " . $e->getMessage());
            return [];
        }
    }
}