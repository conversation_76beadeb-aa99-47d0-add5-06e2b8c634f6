<?php
/**
 * Centralized cookie constants for the public portal
 *
 * These constants standardize cookie behavior across environments.
 * - COOKIE_PATH: path scope for cookies
 * - COOKIE_SAMESITE: SameSite policy for cookies
 * - COOKIE_DOMAIN: domain for cookies (empty string means current host)
 */

if (!defined('COOKIE_PATH')) {
    define('COOKIE_PATH', '/');
}

if (!defined('COOKIE_SAMESITE')) {
    // Choose between 'Lax' (recommended for auth flows) or 'Strict' if appropriate
    define('COOKIE_SAMESITE', 'Lax');
}

if (!defined('COOKIE_DOMAIN')) {
    // Default to scoped host in development; explicit domain in production
    $host = $_SERVER['HTTP_HOST'] ?? '';
    if (stripos($host, 'portal.massacremc.net') !== false) {
        // Player portal specific domain for better security isolation
        define('COOKIE_DOMAIN', 'portal.massacremc.net');
    } elseif (stripos($host, 'massacremc.net') !== false) {
        // Fallback for other subdomains - use current host
        define('COOKIE_DOMAIN', $host);
    } else {
        // Empty means "use current host" (no explicit domain attribute)
        define('COOKIE_DOMAIN', '');
    }
}

