<?php
/**
 * Submit Appeal API (public)
 * Handles appeal submissions from JS (appealsubmit.js) via POST FormData
 */

header('Content-Type: application/json');

require_once __DIR__ . '/includes/core.php';
require_once __DIR__ . '/includes/player-auth.php';
require_once __DIR__ . '/includes/rate_limit.php';

try {
    if (!is_player_authenticated()) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Authentication required']);
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        exit;
    }

    // Enforce CSRF for POST requests
    require_csrf_check(true);

    // Apply rate limiting (3 appeals per 10 minutes per IP)
    apply_rate_limit('appeal_submission', 3, 600, true);

    $player = get_player_data();

    $punishmentId = trim($_POST['punishment_id'] ?? '');
    $reason       = trim($_POST['reason'] ?? '');

    if ($punishmentId === '' || $reason === '') {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Please fill in all required fields.']);
        exit;
    }

    if (strlen($reason) < 15 || strlen($reason) > 500) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Appeal reason must be between 15 and 500 characters.']);
        exit;
    }

    // Resolve Xbox username linked to the Discord account
    $xbox_username = null;
    try {
        $dbAccess = new DatabaseAccess();
        if (!$dbAccess || !$dbAccess->db) {
            throw new Exception('Database connection unavailable');
        }
        $xbox_link = $dbAccess->db->linked_accounts->findOne([
            'discord_id' => $player['discord_id'],
            'platform'   => 'xbox',
            'status'     => 'verified'
        ]);
        if ($xbox_link) {
            $xbox_username = $xbox_link['username'] ?? null;
        }
    } catch (Exception $e) {
        secure_log('Error checking Xbox link for submit_appeal', 'error', [
            'discord_id' => $player['discord_id'] ?? 'unknown',
            'error' => $e->getMessage()
        ]);
    }

    if (!$xbox_username) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'You must have a linked Xbox account to submit appeals.']);
        exit;
    }

    // Submit the appeal using shared DB layer
    $result = $dbAccess->submit_appeal(
        $xbox_username,
        $punishmentId,
        $player['discord_email'] ?? '',
        $reason,
        $player['discord_id'] ?? ''
    );

    // Normalize response for JS handler
    if (!empty($result['duplicate'])) {
        echo json_encode([
            'success' => false,
            'duplicate' => true,
            'message' => $result['message'] ?? 'You already have an active appeal for this punishment.',
            'appeal_id' => $result['appeal_id'] ?? null,
            'status' => $result['status'] ?? null
        ]);
        exit;
    }

    if (!empty($result['success'])) {
        echo json_encode([
            'success' => true,
            'message' => $result['message'] ?? 'Your appeal has been submitted successfully.',
            'appeal_id' => $result['appeal_id'] ?? null
        ]);
        exit;
    }

    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $result['message'] ?? 'Failed to submit appeal. Please try again.']);
} catch (Throwable $t) {
    secure_log('Unhandled error in submit_appeal.php', 'error', ['error' => $t->getMessage()]);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

