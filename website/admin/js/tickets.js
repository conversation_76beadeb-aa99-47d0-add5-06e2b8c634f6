/**
 * Tickets Dashboard JavaScript
 * Handles ticket management functionality
 */

let currentTickets = [];
let currentFilters = {
    status: '',
    priority: '',
    category: '',
    search: ''
};


document.addEventListener('DOMContentLoaded', function() {
    // Clear any existing data to prevent memory leaks
    currentTickets = [];

    // Force garbage collection if available
    if (window.gc && typeof window.gc === 'function') {
        try {
            window.gc();
        } catch (e) {
            // Ignore if gc is not available
        }
    }

    loadTicketsStats();
    loadTickets();
    setupEventListeners();

    // Clean up memory when leaving the page
    window.addEventListener('beforeunload', function() {
        currentTickets = null;
        currentFilters = null;
    });
});

function setupEventListeners() {
    // Filter change handlers
    document.getElementById('statusFilter').addEventListener('change', function() {
        currentFilters.status = this.value;
        loadTickets();
    });
    
    document.getElementById('priorityFilter').addEventListener('change', function() {
        currentFilters.priority = this.value;
        loadTickets();
    });
    
    document.getElementById('categoryFilter').addEventListener('change', function() {
        currentFilters.category = this.value;
        loadTickets();
    });
    
    // Search with debounce
    let searchTimeout;
    document.getElementById('searchFilter').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            currentFilters.search = this.value;
            loadTickets();
        }, 500);
    });
}

function loadTicketsStats() {
    fetch('/adminapi/tickets?action=stats', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateStatsDisplay(data.stats);
        } else {
            console.error('Failed to load stats:', data.error);
        }
    })
    .catch(error => {
        console.error('Error loading stats:', error);
    });
}

function updateStatsDisplay(stats) {
    document.getElementById('openTicketsCount').textContent = stats.openTickets || 0;
    document.getElementById('pendingResponseCount').textContent = stats.pendingResponse || 0;
    document.getElementById('resolvedTodayCount').textContent = stats.resolvedToday || 0;
    document.getElementById('avgResponseTime').textContent = stats.avgResponseTime || '-';
}

function loadTickets() {
    const params = new URLSearchParams({
        action: 'list',
        ...currentFilters,
        limit: 20,
        offset: 0
    });
    
    fetch(`/adminapi/tickets?${params}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Limit tickets to prevent memory issues
            const limitedTickets = data.tickets ? data.tickets.slice(0, 500) : [];
            currentTickets = limitedTickets;
            renderTickets(limitedTickets);
        } else {
            showError('Failed to load tickets: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error loading tickets:', error);
        showError('Network error while loading tickets');
    });
}

function renderTickets(tickets) {
    const container = document.getElementById('ticketsContainer');
    
    if (tickets.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No tickets found</h5>
                <p class="text-muted">No tickets match your current filters.</p>
            </div>
        `;
        return;
    }
    
    const ticketsHTML = tickets.map(ticket => `
        <div class="ticket-card card mb-3 ${getPriorityClass(ticket.priority)}" onclick="viewTicket('${ticket.ticket_id}')">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="card-title mb-1">
                            <strong>${escapeHtml(ticket.subject)}</strong>
                            <span class="badge ${getStatusBadgeClass(ticket.status)} ms-2">${ticket.status}</span>
                        </h6>
                        <p class="ticket-meta mb-2">
                            <i class="fas fa-ticket-alt me-1"></i> ${ticket.ticket_id} •
                            <i class="fas fa-user me-1"></i> ${getCustomerDisplayInfo(ticket)} •
                            <i class="fas fa-tag me-1"></i> ${formatCategory(ticket.category)}
                        </p>
                        <small class="text-muted">
                            Created: ${formatDate(ticket.created_at)} • 
                            Last activity: ${formatDate(ticket.last_activity)}
                        </small>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); viewTicket('${ticket.ticket_id}')">
                            <i class="fas fa-eye me-1"></i> View
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = ticketsHTML;
}

function viewTicket(ticketId) {
    fetch(`/adminapi/tickets?action=detail&id=${encodeURIComponent(ticketId)}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showTicketModal(data.ticket, data.conversations);
        } else {
            showError('Failed to load ticket details: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error loading ticket details:', error);
        showError('Network error while loading ticket details');
    });
}

function showTicketModal(ticket, conversations) {
    const modalBody = document.getElementById('ticketModalBody');
    
    const conversationsHTML = conversations.map(conv => `
        <div class="message border-bottom py-3">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div class="d-flex align-items-center">
                    <i class="fas ${conv.author_type === 'customer' ? 'fa-user' : 'fa-user-shield'} me-2"></i>
                    <strong>${escapeHtml(conv.author_name)}</strong>
                    <span class="badge ${conv.author_type === 'customer' ? 'bg-primary' : 'bg-success'} ms-2">
                        ${conv.author_type}
                    </span>
                    ${conv.is_internal ? '<span class="badge bg-warning ms-1">Internal</span>' : ''}
                </div>
                <small class="text-muted">${formatDate(conv.created_at)}</small>
            </div>
            <div class="message-content">
                ${escapeHtml(conv.message).replace(/\n/g, '<br>')}
            </div>
        </div>
    `).join('');
    
    modalBody.innerHTML = `
        <div class="ticket-header mb-4">
            <h5>${escapeHtml(ticket.subject)}</h5>
            <div class="row">
                <div class="col-md-6">
                    <strong>Ticket ID:</strong> ${ticket.ticket_id}<br>
                    <strong>Customer:</strong> ${getCustomerDetailInfo(ticket)}<br>
                    <strong>Category:</strong> ${formatCategory(ticket.category)}
                </div>
                <div class="col-md-6">
                    <strong>Status:</strong> <span class="badge ${getStatusBadgeClass(ticket.status)}">${ticket.status}</span><br>
                    <strong>Priority:</strong> <span class="badge ${getPriorityBadgeClass(ticket.priority)}">${ticket.priority}</span><br>
                    <strong>Created:</strong> ${formatDate(ticket.created_at)}
                </div>
            </div>
        </div>
        
        <div class="conversation-history mb-4" style="max-height: 400px; overflow-y: auto;">
            ${conversationsHTML || '<p class="text-muted">No conversation history</p>'}
        </div>
        
        <div class="reply-form">
            <h6>Add Reply</h6>
            <form onsubmit="sendReply(event, '${ticket.ticket_id}')">
                <div class="mb-3">
                    <textarea class="form-control" id="replyMessage" rows="4" placeholder="Type your reply..." required></textarea>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="internalReply">
                        <label class="form-check-label" for="internalReply">
                            Internal note (not visible to customer)
                        </label>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-secondary me-2" onclick="updateTicketStatus('${ticket.ticket_id}', 'resolved')">
                            Mark Resolved
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-1"></i> Send Reply
                        </button>
                    </div>
                </div>
            </form>
        </div>
    `;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('ticketModal'));
    modal.show();
}

function sendReply(event, ticketId) {
    event.preventDefault();

    const message = document.getElementById('replyMessage').value;
    const isInternal = document.getElementById('internalReply').checked;
    const submitButton = event.target.querySelector('button[type="submit"]');

    if (!message.trim()) {
        showError('Please enter a message');
        return;
    }

    console.log('Sending reply:', { ticketId, message: message.substring(0, 50) + '...', isInternal });

    // Disable submit button and show loading state
    if (submitButton) {
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Sending...';
    }
    
    fetch('/adminapi/tickets?action=reply', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include',
        body: JSON.stringify({
            ticket_id: ticketId,
            message: message,
            is_internal: isInternal
        })
    })
    .then(response => {
        console.log('Response status:', response.status, response.statusText);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.text().then(text => {
            console.log('Raw response:', text);
            try {
                // Clean up any potential whitespace or non-JSON content before parsing
                const cleanText = text.trim();
                // Find the first { or [ to start JSON parsing from there
                const jsonStart = Math.max(cleanText.indexOf('{'), cleanText.indexOf('['));
                if (jsonStart > 0) {
                    console.log('Found JSON at position:', jsonStart);
                    const jsonText = cleanText.substring(jsonStart);
                    return JSON.parse(jsonText);
                } else {
                    return JSON.parse(cleanText);
                }
            } catch (e) {
                console.error('Invalid JSON response:', text);
                console.error('Parse error:', e);
                throw new Error('Server returned invalid JSON response: ' + text.substring(0, 100));
            }
        });
    })
    .then(data => {
        if (data.success) {
            showSuccess('Reply sent successfully');
            // Close modal and refresh tickets
            bootstrap.Modal.getInstance(document.getElementById('ticketModal')).hide();
            loadTickets();
            loadTicketsStats();
        } else {
            showError('Failed to send reply: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error sending reply:', error);
        showError('Network error while sending reply: ' + error.message);
    })
    .finally(() => {
        // Restore submit button state
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-paper-plane me-1"></i> Send Reply';
        }
    });
}

function updateTicketStatus(ticketId, status) {
    fetch('/adminapi/tickets?action=update_status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include',
        body: JSON.stringify({
            ticket_id: ticketId,
            status: status
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.text().then(text => {
            try {
                // Clean up any potential whitespace or non-JSON content before parsing
                const cleanText = text.trim();
                // Find the first { or [ to start JSON parsing from there
                const jsonStart = Math.max(cleanText.indexOf('{'), cleanText.indexOf('['));
                if (jsonStart > 0) {
                    const jsonText = cleanText.substring(jsonStart);
                    return JSON.parse(jsonText);
                } else {
                    return JSON.parse(cleanText);
                }
            } catch (e) {
                console.error('Invalid JSON response:', text);
                console.error('Parse error:', e);
                throw new Error('Server returned invalid JSON response');
            }
        });
    })
    .then(data => {
        if (data.success) {
            showSuccess(`Ticket status updated to ${status}`);
            // Close modal and refresh tickets
            bootstrap.Modal.getInstance(document.getElementById('ticketModal')).hide();
            loadTickets();
            loadTicketsStats();
        } else {
            showError('Failed to update status: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error updating status:', error);
        showError('Network error while updating status: ' + error.message);
    });
}


function getPriorityClass(priority) {
    switch (priority) {
        case 'high': return 'priority-high';
        case 'medium': return 'priority-medium';
        case 'low': return 'priority-low';
        default: return '';
    }
}

function getStatusBadgeClass(status) {
    switch (status) {
        case 'open': return 'bg-success';
        case 'pending': return 'bg-warning';
        case 'resolved': return 'bg-info';
        case 'closed': return 'bg-secondary';
        default: return 'bg-primary';
    }
}

function getPriorityBadgeClass(priority) {
    switch (priority) {
        case 'high': return 'bg-danger';
        case 'medium': return 'bg-warning';
        case 'low': return 'bg-success';
        default: return 'bg-secondary';
    }
}

function formatDate(dateString) {
    // Handle the "MMM D, YYYY H:MM AM/PM ET" format from server
    if (dateString.includes(' ET')) {
        // The server already formats it correctly, just return as-is
        return dateString;
    }

    // Fallback for other date formats
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
        return dateString; // Return original if parsing fails
    }

    // Format in Eastern Time with the desired format
    const options = {
        timeZone: 'America/New_York',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    };

    return date.toLocaleString('en-US', options) + ' ET';
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function isDeveloperOrHigher() {
    // Check if user has developer role or higher
    const authData = window.AUTH_DATA || {};
    const userRole = authData.role || '';

    // Developer and Owner roles can see customer emails
    // Based on the role constants: ROLE_DEVELOPER = 'DEVELOPER', ROLE_OWNER = 'OWNER'
    return userRole === 'DEVELOPER' || userRole === 'OWNER';
}

function formatCategory(category) {
    const categories = {
        'technical': 'Technical Support',
        'billing': 'Billing & Payments',
        'general': 'General Inquiry',
        'bug': 'Bug Report',
        'feature': 'Feature Request',
        'privacy': 'GDPR Request'
    };
    return categories[category] || category.charAt(0).toUpperCase() + category.slice(1);
}

function getCustomerDisplayInfo(ticket) {
    if (isDeveloperOrHigher()) {
        // Developers can see the full email
        return escapeHtml(ticket.customer_email || 'No email');
    } else {
        // Non-developers see customer name if available, otherwise masked email from server
        if (ticket.customer_name && ticket.customer_name.trim() !== '') {
            return escapeHtml(ticket.customer_name);
        } else if (ticket.customer_email && ticket.customer_email.trim() !== '') {
            // Server already provides masked email for non-developers
            return escapeHtml(ticket.customer_email);
        } else {
            return 'Customer';
        }
    }
}

function getCustomerDetailInfo(ticket) {
    if (isDeveloperOrHigher()) {
        // Developers can see name and full email
        const name = ticket.customer_name && ticket.customer_name.trim() !== '' ?
            escapeHtml(ticket.customer_name) : 'Customer';
        const email = ticket.customer_email || 'No email';
        return `${name} (${escapeHtml(email)}) <span class="badge bg-info ms-1" title="Email visible to developers only"><i class="fas fa-eye"></i></span>`;
    } else {
        // Non-developers see only customer name, no email
        if (ticket.customer_name && ticket.customer_name.trim() !== '') {
            return escapeHtml(ticket.customer_name) + ' <span class="badge bg-secondary ms-1" title="Email hidden for privacy"><i class="fas fa-eye-slash"></i></span>';
        } else {
            return 'Customer <span class="badge bg-secondary ms-1" title="Email hidden for privacy"><i class="fas fa-eye-slash"></i></span>';
        }
    }
}

function showError(message) {
    // Use SweetAlert2 for better user experience
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: message,
            confirmButtonColor: '#dc3545'
        });
    } else {
        alert('Error: ' + message);
    }
}

function showSuccess(message) {
    // Use SweetAlert2 for better user experience
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: message,
            timer: 3000,
            showConfirmButton: false,
            confirmButtonColor: '#28a745'
        });
    } else {
        alert('Success: ' + message);
    }
}

function refreshTickets() {
    // Add visual feedback to refresh button
    const refreshButton = document.querySelector('button[onclick="refreshTickets()"]');
    if (refreshButton) {
        const originalHTML = refreshButton.innerHTML;
        refreshButton.disabled = true;
        refreshButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';

        // Reset button after refresh completes
        setTimeout(() => {
            refreshButton.disabled = false;
            refreshButton.innerHTML = originalHTML;
        }, 1000);
    }

    // Show loading message
    const container = document.getElementById('ticketsContainer');
    if (container) {
        container.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Refreshing tickets...</span>
                </div>
                <p class="mt-2 text-muted">Refreshing tickets...</p>
            </div>
        `;
    }

    loadTicketsStats();
    loadTickets();
}
