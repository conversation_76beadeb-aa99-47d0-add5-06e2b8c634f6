/**
 * Common admin functionality shared across all admin pages
 */


function checkSession() {
    $.ajax({
        url: '/auth/check_status',  // Updated path to match your PHP file structure
        method: 'GET',
        dataType: 'json',
        cache: false,
        success: function(data) {
            if (!data.authenticated) {
                let title = 'Session Expired';
                let message = data.message || 'Your session has expired. Please log in again.';

                // Check if the message indicates role changes
                if (message.includes('Discord roles have changed')) {
                    title = 'Access Revoked';
                }

                Swal.fire({
                    icon: 'warning',
                    title: title,
                    text: message,
                    confirmButtonText: 'Login Again',
                    allowOutsideClick: false
                }).then(() => {
                    // Clear all cookies
                    document.cookie.split(";").forEach(function(c) {
                        document.cookie = c.replace(/^ +/, "").replace(/=.*/,
                            "=;expires=" + new Date().toUTCString() + ";path=/");
                    });
                    window.location.href = '/login?force_logout=1';
                });
            } else if (data.hasRequiredRole === false) {
                Swal.fire({
                    icon: 'error',
                    title: 'Access Denied',
                    text: 'You do not have the required permissions for this page.',
                    confirmButtonText: 'Go to Dashboard',
                    allowOutsideClick: false
                }).then(() => {
                    window.location.href = '/dashboard';
                });
            }
        },
        error: function(xhr, status, error) {
            // Check if this is a server offline scenario
            if ((xhr.status === 0 || xhr.status === 502 || xhr.status === 503 || xhr.status === 504 || status === 'timeout') && !window.serverOfflineShown) {
                // Server is likely offline - automatically log out
                window.serverOfflineShown = true;
                console.log('Server appears to be offline during session check, logging out user');

                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'error',
                        title: 'Server Offline',
                        text: 'The server is currently offline. You will be logged out automatically.',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: true,
                        confirmButtonText: 'OK'
                    }).then(() => {
                        // Clear local session data
                        if (typeof Storage !== "undefined") {
                            localStorage.clear();
                            sessionStorage.clear();
                        }

                        // Redirect to login with server offline indicator
                        window.location.href = '/logout?reason=server_offline';
                    });
                } else {
                    // Fallback if SweetAlert is not available
                    alert('Server is offline. You will be logged out.');
                    window.location.href = '/logout?reason=server_offline';
                }
                return;
            }

            // Parse response to get detailed error information
            let errorMessage = 'Please log in again to continue.';
            let errorTitle = 'Authentication Error';

            try {
                const response = JSON.parse(xhr.responseText);
                if (response.message) {
                    errorMessage = response.message;

                    // Check if the message indicates role changes
                    if (errorMessage.includes('Discord roles have changed')) {
                        errorTitle = 'Access Revoked';
                    }
                }
            } catch (e) {
                // Use default message if parsing fails
            }

            if (xhr.status === 401 || xhr.status === 403) {
                Swal.fire({
                    icon: 'warning',
                    title: errorTitle,
                    text: errorMessage,
                    confirmButtonText: 'Login',
                    allowOutsideClick: false
                }).then(() => {
                    window.location.href = '/login';
                });
            }
        }
    });
}


$(document).ready(function() {

    const authData = window.AUTH_DATA || {};


    // Store interval ID for cleanup
    window.sessionCheckInterval = setInterval(checkSession, 5 * 60 * 1000);

    // Add a more frequent server heartbeat check (every 15 seconds)
    let consecutiveFailures = 0;
    const maxFailures = 2; // Require 2 consecutive failures before logout

    window.serverHeartbeatInterval = setInterval(function() {
        // Lightweight ping to detect server offline
        $.ajax({
            url: '/auth/ping.php',
            method: 'GET',
            timeout: 5000, // 5 second timeout (reduced from 10)
            cache: false,
            success: function() {
                // Server is online, reset counters and flags
                consecutiveFailures = 0;
                window.serverOfflineShown = false;
                console.log('Server heartbeat: OK');
            },
            error: function(xhr, status, error) {
                consecutiveFailures++;
                console.log(`Server heartbeat failed (${consecutiveFailures}/${maxFailures}):`, status, error, 'Status code:', xhr.status);

                // Trigger logout if we have enough consecutive failures and haven't shown the dialog yet
                if (consecutiveFailures >= maxFailures && !window.serverOfflineShown) {
                    window.serverOfflineShown = true;

                    console.log('Server appears offline after multiple failures, logging out user');

                    if (typeof Swal !== 'undefined') {
                        Swal.fire({
                            icon: 'error',
                            title: 'Server Offline',
                            text: 'The server is currently offline. You will be logged out automatically.',
                            allowOutsideClick: false,
                            allowEscapeKey: false,
                            showConfirmButton: true,
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Clear local session data
                            if (typeof Storage !== "undefined") {
                                localStorage.clear();
                                sessionStorage.clear();
                            }

                            // Redirect to login with server offline indicator
                            window.location.href = '/logout?reason=server_offline';
                        });
                    } else {
                        // Fallback if SweetAlert is not available
                        alert('Server is offline. You will be logged out.');
                        window.location.href = '/logout?reason=server_offline';
                    }
                }
            }
        });
    }, 15 * 1000); // Check every 15 seconds (increased frequency)

    setTimeout(checkSession, 3000);

    // Clean up intervals when page unloads
    window.addEventListener('beforeunload', function() {
        cleanupMemory();
    });

    // Global memory cleanup function
    window.cleanupMemory = function() {
        // Clear all known intervals
        const intervals = [
            'sessionCheckInterval',
            'serverHeartbeatInterval',
            'dashboardStatsInterval',
            'playerInfoSessionInterval'
        ];

        intervals.forEach(intervalName => {
            if (window[intervalName]) {
                clearInterval(window[intervalName]);
                window[intervalName] = null;
            }
        });

        // Clear large data arrays
        const dataArrays = [
            'allReports',
            'filteredReports',
            'currentTickets',
            'currentFilters'
        ];

        dataArrays.forEach(arrayName => {
            if (window[arrayName]) {
                window[arrayName] = null;
            }
        });

        // Force garbage collection if available
        if (window.gc && typeof window.gc === 'function') {
            try {
                window.gc();
            } catch (e) {
                // Ignore if gc is not available
            }
        }
    };




    initDarkMode();


    initMobileSearch();


    initLogoutButtons();


    window.addEventListener('resize', handleResize);


    handleResize();



    let csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');


    if (!csrfToken) {

        csrfToken = Math.random().toString(36).substring(2, 15);
    }


    window.CSRF_TOKEN = csrfToken;

    $.ajaxSetup({
        xhrFields: {
            withCredentials: true  // Ensure cookies are sent with requests
        },
        beforeSend: function(xhr) {

            if (window.AUTH_DATA) {
                xhr.setRequestHeader('X-Discord-ID', window.AUTH_DATA.userId || '');
                xhr.setRequestHeader('X-Discord-Username', window.AUTH_DATA.username || '');
                xhr.setRequestHeader('X-Discord-Role', window.AUTH_DATA.role || '');
                xhr.setRequestHeader('X-Discord-Avatar', window.AUTH_DATA.avatar || '');
                xhr.setRequestHeader('X-Session-ID', window.AUTH_DATA.sessionId || '');
            }


            if (csrfToken) {
                xhr.setRequestHeader('X-CSRF-TOKEN', csrfToken);
            }


            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        },
        error: function(xhr, status, error) {

            if (xhr.status === 401) {

            } else if (xhr.status === 403) {

            } else if (xhr.status === 500) {

                try {
                    const response = JSON.parse(xhr.responseText);
                } catch (e) {

                }
            }
        }
    });


    $(document).ajaxError(function(_, jqXHR, settings) {
        // Skip heartbeat/ping requests to avoid false positives
        if (settings.url && (settings.url.includes('/auth/ping') || settings.url.includes('/auth/check_status'))) {
            return;
        }

        // Check for server offline scenarios (status 0 = network error, 502/503/504 = server errors)
        if ((jqXHR.status === 0 || jqXHR.status === 502 || jqXHR.status === 503 || jqXHR.status === 504) && !window.serverOfflineShown) {
            window.serverOfflineShown = true;

            console.log('Server appears to be offline during AJAX request:', settings.url, 'Status:', jqXHR.status);

            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'Server Offline',
                    text: 'The server is currently offline. You will be logged out automatically.',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: true,
                    confirmButtonText: 'OK'
                }).then(() => {
                    // Clear local session data
                    if (typeof Storage !== "undefined") {
                        localStorage.clear();
                        sessionStorage.clear();
                    }

                    // Redirect to login with server offline indicator
                    window.location.href = '/logout?reason=server_offline';
                });
            } else {
                // Fallback if SweetAlert is not available
                alert('Server is offline. You will be logged out.');
                window.location.href = '/logout?reason=server_offline';
            }
            return;
        }

        if (jqXHR.status === 401 || jqXHR.status === 403) {
            if (!window.authErrorShown) {
                window.authErrorShown = true;

                let errorReason = "Your session has expired.";
                let errorTitle = "Authentication Required";
                let errorIcon = "warning";

                try {
                    const response = JSON.parse(jqXHR.responseText);

                    // Handle specific error reasons
                    if (response.reason === 'role_verification_failed') {
                        errorTitle = "Access Revoked";
                        errorReason = "Your Discord roles have changed. You no longer have access to this system.";
                        errorIcon = "error";
                    } else if (response.reason) {
                        errorReason = `Session error: ${response.reason}`;
                    } else if (response.message) {
                        errorReason = response.message;
                    } else if (response.error) {
                        errorReason = response.error;
                    }
                } catch (e) {
                    // Use default message if parsing fails
                }

                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: errorIcon,
                        title: errorTitle,
                        html: `Please log in again.<br><br><small class="text-muted">${errorReason}</small>`,
                        showCancelButton: true,
                        confirmButtonText: 'Login',
                        cancelButtonText: 'Try Again',
                        allowOutsideClick: false
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '/login?force_logout=1';
                        } else {
                            window.location.reload();
                        }
                    });
                } else {
                    if (confirm(`${errorTitle}: ${errorReason}. Please log in again.`)) {
                        window.location.href = '/login?force_logout=1';
                    } else {
                        window.location.reload();
                    }
                }
            }
        }
    });
});


function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const content = document.getElementById('content');
    const body = document.body;
    const backdrop = document.querySelector('.sidebar-backdrop');

    sidebar.classList.toggle('active');
    content.classList.toggle('active');
    body.classList.toggle('sidebar-open');


    if (backdrop) {
        if (body.classList.contains('sidebar-open')) {
            backdrop.style.display = 'block';

            setTimeout(() => {
                backdrop.style.opacity = '1';
            }, 10);
        } else {
            backdrop.style.opacity = '0';

            setTimeout(() => {
                backdrop.style.display = 'none';
            }, 300); // Match the transition duration
        }
    }
}


function openMobileSearch() {
    document.getElementById('mobileSearchOverlay').classList.add('active');
    document.querySelector('.mobile-search-input').focus();
    document.body.classList.add('search-open');
}

function closeMobileSearch() {
    document.getElementById('mobileSearchOverlay').classList.remove('active');
    document.body.classList.remove('search-open');
}


function toggleDarkMode() {
    // Always force dark mode - no toggle functionality
    document.documentElement.classList.add('dark-mode');
    document.body.classList.add('dark-mode');

    // Always save as enabled
    localStorage.setItem('darkMode', 'enabled');
    document.cookie = 'darkMode=enabled; path=/; max-age=31536000; SameSite=Lax';
}

function initDarkMode() {
    // Always force dark mode regardless of saved preferences
    document.documentElement.classList.add('dark-mode');
    document.body.classList.add('dark-mode');

    // Always save as enabled
    localStorage.setItem('darkMode', 'enabled');
    document.cookie = 'darkMode=enabled; path=/; max-age=31536000; SameSite=Lax';
}


function logout() {

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'Logout',
            text: 'Are you sure you want to log out?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, log out'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '/logout';
            }
        });
    } else {

        if (confirm('Are you sure you want to log out?')) {
            window.location.href = '/logout';
        }
    }
}

function formatDate(dateObj) {
    if (!dateObj) return 'N/A';


    const date = parseMongoDate(dateObj);


    if (!date || isNaN(date.getTime())) {
        return String(dateObj); // Return original if invalid
    }

    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}


function handleApiError(xhr, fallbackMessage = 'An error occurred') {
    let errorMsg = fallbackMessage;

    try {
        if (xhr.responseJSON && xhr.responseJSON.error) {
            errorMsg = xhr.responseJSON.error;
        } else if (xhr.responseText) {
            const parsed = JSON.parse(xhr.responseText);
            errorMsg = parsed.error || parsed.message || fallbackMessage;
        }
    } catch (e) {

    }

    return errorMsg;
}

function parseMongoDate(dateObj) {
    if (!dateObj) return null;


    if (dateObj && typeof dateObj === 'object' && dateObj.$date) {
        return new Date(dateObj.$date);
    }


    if (dateObj && typeof dateObj === 'object' && dateObj.milliseconds) {
        return new Date(Number(dateObj.milliseconds));
    }


    if (dateObj instanceof Date) {
        return dateObj;
    }


    if (typeof dateObj === 'string') {

        const date = new Date(dateObj);
        if (!isNaN(date.getTime())) {
            return date;
        }


    }


    if (typeof dateObj === 'number') {
        return new Date(dateObj);
    }

    return null;
}



function initDarkModeSecondary() {
    // Always force dark mode regardless of saved preferences
    document.documentElement.classList.add('dark-mode');
    document.body.classList.add('dark-mode');

    // Always save as enabled
    localStorage.setItem('darkMode', 'enabled');
    document.cookie = 'darkMode=enabled; path=/; max-age=31536000; SameSite=Lax';

    // Remove dark mode toggle functionality since we only use dark mode
    const darkModeToggle = document.querySelector('.dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.style.display = 'none'; // Hide the toggle
    }

    // Remove dark mode menu item since we only use dark mode
    const darkModeMenuItem = document.querySelector('.dropdown-item[data-action="toggle-dark-mode"]');
    if (darkModeMenuItem) {
        darkModeMenuItem.style.display = 'none'; // Hide the menu item
    }
}


function initLogoutButtons() {

    const logoutLinks = document.querySelectorAll('a[href="#"][onclick="logout()"], a.logout-btn');
    logoutLinks.forEach(link => {

        link.removeAttribute('onclick');


        link.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    });
}


function initMobileSearch() {

    const mobileSearchButton = document.querySelector('.mobile-search-button');
    if (mobileSearchButton) {
        mobileSearchButton.addEventListener('click', openMobileSearch);
    }


    const mobileSearchInput = document.querySelector('.mobile-search-input');
    if (mobileSearchInput) {
        mobileSearchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {

                const searchTerm = this.value.trim();
                if (searchTerm) {




                }
            }
        });
    }
}


function handleResize() {
    const width = window.innerWidth;


    if (width < 992) {
        document.getElementById('sidebar')?.classList.remove('active');
        document.getElementById('content')?.classList.remove('active');
        document.body.classList.remove('sidebar-open');
    }
}


function copyDiscordId(event) {
    event.preventDefault();
    event.stopPropagation();

    const discordId = document.querySelector('.discord-id-text').textContent.trim();


    const tempInput = document.createElement('input');
    tempInput.value = discordId;
    document.body.appendChild(tempInput);


    tempInput.select();
    document.execCommand('copy');


    document.body.removeChild(tempInput);


    const copyIcon = event.target;
    const originalTitle = copyIcon.getAttribute('title');
    copyIcon.setAttribute('title', 'Copied!');
    copyIcon.classList.add('text-success');


    setTimeout(() => {
        copyIcon.setAttribute('title', originalTitle);
        copyIcon.classList.remove('text-success');
    }, 2000);
}


window.parseMongoDate = parseMongoDate;
window.toggleSidebar = toggleSidebar;
window.logout = logout;
window.formatDate = formatDate;
window.handleApiError = handleApiError;
window.checkSession = checkSession;
window.toggleDarkMode = toggleDarkMode;
window.openMobileSearch = openMobileSearch;
window.closeMobileSearch = closeMobileSearch;
window.copyDiscordId = copyDiscordId;

