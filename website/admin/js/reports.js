/**
 * Format a date in Eastern Time
 * @param {string|Date|Object} dateInput - Date to format
 * @param {string} timeZone - Timezone to use (default: America/New_York)
 * @returns {string} Formatted date string with ET timezone
 */
function formatDate(dateInput, timeZone = 'America/New_York') {
    if (!dateInput) return 'Unknown';


    if (typeof dateInput === 'string' && dateInput.endsWith(' ET')) {
        return dateInput;
    }

    try {
        let date;


        if (typeof dateInput === 'object') {
            if (dateInput.$date) {

                if (typeof dateInput.$date === 'string') {
                    date = new Date(dateInput.$date);
                } else if (dateInput.$date.$numberLong) {
                    date = new Date(parseInt(dateInput.$date.$numberLong));
                } else {
                    date = new Date(dateInput.$date);
                }
            } else if (dateInput instanceof Date) {
                date = dateInput;
            } else {

                date = new Date(String(dateInput));
            }
        } else if (typeof dateInput === 'number') {

            date = new Date(dateInput);
        } else {

            date = new Date(dateInput);
        }


        if (isNaN(date.getTime())) {
            return 'Unknown';
        }


        const options = {
            timeZone,
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };

        return date.toLocaleString('en-US', options) + ' ET';
    } catch (e) {
        return 'Unknown'; // Return Unknown for invalid dates
    }
}

document.addEventListener('DOMContentLoaded', function() {

    const authData = {
        userId: window.AUTH_DATA ? window.AUTH_DATA.userId : '',
        username: window.AUTH_DATA ? window.AUTH_DATA.username : '',
        role: window.AUTH_DATA ? window.AUTH_DATA.role : '',
        avatar: window.AUTH_DATA ? window.AUTH_DATA.avatar : ''
    };


    let allReports = [];
    let filteredReports = [];
    let currentPage = 1;
    let reportsPerPage = 12;
    let reportModal;


    initializePage();

    function initializePage() {

        reportModal = new bootstrap.Modal(document.getElementById('reportModal'));


        loadReports();


        setupEventListeners();
    }

    function loadReports(offset = 0, append = false) {
        if (!append) {
            // Show loading indicator only for initial load
            document.getElementById('reportsContainer').innerHTML = `
                <div class="col-12 text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading reports...</p>
                </div>
            `;
        }

        // Build URL with pagination parameters
        const url = `/adminapi/reports.php?limit=100&offset=${offset}`; // Added offset parameter

        fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Discord-ID': authData.userId,
                'X-Discord-Username': authData.username,
                'X-Discord-Role': authData.role,
                'X-Discord-Avatar': authData.avatar || '',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'include'
        })
        .then(response => {
            if (!response.ok) {
                if (response.status === 403) {
                    throw new Error('Permission denied. You may not have the required role to access reports.');
                } else if (response.status === 401) {
                    throw new Error('Authentication required. Please log in again.');
                } else if (response.status === 500) {
                    throw new Error('Server error. The reports database may be experiencing high load. Please try again in a moment.');
                } else if (response.status === 503) {
                    throw new Error('Database is initializing. Please try again in a moment.');
                } else {
                    throw new Error(`Network response was not ok (Status: ${response.status})`);
                }
            }
            return response.json();
        })
        .then(data => {

            if (data.reports) {

                allReports = data.reports;
            } else if (Array.isArray(data)) {

                allReports = data;
            } else {

                allReports = [];
            }


            allReports.sort((a, b) => {

                if (a.status === 'Pending' && b.status !== 'Pending') {
                    return -1;
                }
                if (a.status !== 'Pending' && b.status === 'Pending') {
                    return 1;
                }


                if (a.status === 'Pending' && b.status === 'Pending') {

                    const priorityA = parseInt(a.priority) || 4;
                    const priorityB = parseInt(b.priority) || 4;


                    if (priorityA !== priorityB) {
                        return priorityA - priorityB;
                    }
                }


                const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
                const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
                return dateB - dateA;
            });


            updateCounters();


            populateRuleFilter();


            applyFilters();
        })
        .catch(error => {


            const isAuthError = error.message.includes('Authentication required') ||
                               error.message.includes('Permission denied');

            document.getElementById('reportsContainer').innerHTML = `
                <div class="col-12">
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <h3 class="empty-state-title">Error Loading Reports</h3>
                        <p class="empty-state-text">
                            ${error.message || 'There was a problem loading the reports. Please try again later.'}
                        </p>
                        ${isAuthError ? `
                            <a href="/login" class="btn btn-primary mt-3">
                                <i class="fas fa-sign-in-alt me-2"></i> Log In Again
                            </a>
                        ` : `
                            <button class="btn btn-primary mt-3" onclick="location.reload()">
                                <i class="fas fa-sync-alt me-2"></i> Retry
                            </button>
                        `}
                        <p class="mt-3 text-muted small">If the problem persists, please contact an administrator.</p>
                    </div>
                </div>
            `;
        });
    }

    function updateCounters() {
        const totalCount = allReports.length;
        const pendingCount = allReports.filter(r => r.status === 'Pending').length;

        document.getElementById('totalCount').textContent = totalCount;
        document.getElementById('pendingCount').textContent = pendingCount;
    }

    function populateRuleFilter() {

        const ruleSelect = document.getElementById('ruleFilter');
        const uniqueRules = [...new Set(allReports.map(r => r.rule_broken).filter(Boolean))];


        uniqueRules.sort();


        uniqueRules.forEach(rule => {
            const option = document.createElement('option');
            option.value = rule;
            option.textContent = rule;
            ruleSelect.appendChild(option);
        });
    }

    function setupEventListeners() {

        document.getElementById('statusFilter').addEventListener('change', applyFilters);
        document.getElementById('ruleFilter').addEventListener('change', applyFilters);
        document.getElementById('priorityFilter').addEventListener('change', applyFilters);
        document.getElementById('searchFilter').addEventListener('input', debounce(applyFilters, 300));


        document.getElementById('resetFilters').addEventListener('click', resetFilters);


        document.getElementById('prevPage').addEventListener('click', () => changePage(currentPage - 1));
        document.getElementById('nextPage').addEventListener('click', () => changePage(currentPage + 1));


        document.getElementById('reportsContainer').addEventListener('click', function(e) {

            if (e.target.closest('.view-report')) {
                const reportId = e.target.closest('.view-report').dataset.id;
                showReportModal(reportId);
            }


            if (e.target.closest('.accept-report')) {
                const reportId = e.target.closest('.accept-report').dataset.id;
                acceptReport(reportId);
            }


            if (e.target.closest('.reject-report')) {
                const reportId = e.target.closest('.reject-report').dataset.id;
                rejectReport(reportId);
            }
        });


        document.getElementById('reportModal').addEventListener('click', function(e) {

            if (e.target.closest('.modal-accept-report')) {
                const reportId = e.target.closest('.modal-accept-report').dataset.id;
                acceptReport(reportId);
            }


            if (e.target.closest('.modal-reject-report')) {
                const reportId = e.target.closest('.modal-reject-report').dataset.id;
                rejectReport(reportId);
            }
        });
    }

    function applyFilters() {
        const statusFilter = document.getElementById('statusFilter').value;
        const ruleFilter = document.getElementById('ruleFilter').value;
        const priorityFilter = document.getElementById('priorityFilter').value;
        const searchFilter = document.getElementById('searchFilter').value.toLowerCase();


        filteredReports = allReports.filter(report => {

            if (statusFilter !== 'all' && report.status !== statusFilter) {
                return false;
            }


            if (ruleFilter !== 'all' && report.rule_broken !== ruleFilter) {
                return false;
            }


            if (priorityFilter !== 'all') {
                const priorityValue = parseInt(priorityFilter);

                const reportPriority = parseInt(report.priority);
                if (isNaN(reportPriority) || reportPriority !== priorityValue) {
                    return false;
                }
            }


            if (searchFilter && !(
                (report.offender_name && report.offender_name.toLowerCase().includes(searchFilter)) ||
                (report.report_id && report.report_id.toLowerCase().includes(searchFilter))
            )) {
                return false;
            }

            return true;
        });


        filteredReports.sort((a, b) => {

            if (a.status === 'Pending' && b.status !== 'Pending') {
                return -1;
            }
            if (a.status !== 'Pending' && b.status === 'Pending') {
                return 1;
            }


            if (a.status === 'Pending' && b.status === 'Pending') {

                const priorityA = parseInt(a.priority) || 4;
                const priorityB = parseInt(b.priority) || 4;


                if (priorityA !== priorityB) {
                    return priorityA - priorityB;
                }
            }


            const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
            const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
            return dateB - dateA;
        });


        currentPage = 1;


        renderReports();


        updatePagination();
    }

    function resetFilters() {
        document.getElementById('statusFilter').value = 'all';
        document.getElementById('ruleFilter').value = 'all';
        document.getElementById('priorityFilter').value = 'all';
        document.getElementById('searchFilter').value = '';

        applyFilters();
    }

    function renderReports() {
        const reportsContainer = document.getElementById('reportsContainer');


        const startIndex = (currentPage - 1) * reportsPerPage;
        const endIndex = startIndex + reportsPerPage;
        const paginatedReports = filteredReports.slice(startIndex, endIndex);


        if (filteredReports.length === 0) {
            reportsContainer.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-5">
                        <div class="empty-state">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5>No Reports Found</h5>
                            <p class="text-muted">
                                No reports match your current filters. Try adjusting your filters or resetting them.
                            </p>
                            <button class="btn btn-primary mt-3" id="emptyStateReset">
                                <i class="fas fa-filter me-2"></i> Reset Filters
                            </button>
                        </div>
                    </td>
                </tr>
            `;


            document.getElementById('emptyStateReset').addEventListener('click', resetFilters);
            return;
        }


        let rowsHTML = '';

        paginatedReports.forEach(report => {

            let statusClass = 'secondary';
            let statusText = report.status || 'Unknown';

            if (report.status === 'Pending') {
                statusClass = 'badge bg-warning text-dark';
            } else if (report.status === 'Accepted') {
                statusClass = 'badge bg-success text-white';
            } else if (report.status === 'Denied') {
                statusClass = 'badge bg-danger text-white';
            } else {
                statusClass = 'badge bg-secondary text-white';
            }


            let priorityClass = 'secondary';
            let priorityText = 'Low';

            if (report.priority === 1) {
                priorityClass = 'badge bg-danger text-white';
                priorityText = 'High';
            } else if (report.priority === 2) {
                priorityClass = 'badge bg-warning text-dark';
                priorityText = 'Urgent';
            } else if (report.priority === 3) {
                priorityClass = 'badge bg-info text-white';
                priorityText = 'Medium';
            } else if (report.priority === 4 || !report.priority) {
                priorityClass = 'badge bg-secondary text-white';
                priorityText = 'Low';
            }


            const formattedDate = report.created_at_formatted || formatDate(report.created_at);


            rowsHTML += `
                <tr data-status="${report.status}" data-rule="${report.rule_broken}" data-priority="${report.priority}">
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="fas fa-user text-primary"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">${report.reporter_name || 'Anonymous'}</div>
                                <div class="text-muted small">#${report.report_id || 'Unknown'}</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="fw-semibold">${report.offender_name || 'Unknown'}</div>
                    </td>
                    <td>
                        <span class="report-type-badge">
                            <i class="fas fa-${report.report_type === 'faction' ? 'users' : 'user'} me-1"></i>
                            ${report.report_type === 'faction' ? 'Faction' : 'Player'}
                        </span>
                    </td>
                    <td>
                        <div class="text-truncate" style="max-width: 150px;" title="${report.rule_broken || 'Unspecified Rule'}">
                            ${report.rule_broken || 'Unspecified Rule'}
                        </div>
                    </td>
                    <td>
                        <span class="${priorityClass}">${priorityText}</span>
                    </td>
                    <td>
                        <span class="${statusClass}">${statusText}</span>
                    </td>
                    <td>
                        <div class="text-muted small">${formattedDate}</div>
                    </td>
                    <td>
                        <div class="d-flex gap-1">
                            <button class="btn btn-sm btn-outline-primary view-report" data-id="${report.report_id || report._id}" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${report.status === 'Pending' ? `
                                <button class="btn btn-sm btn-success accept-report" data-id="${report.report_id || report._id}" title="Accept">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-danger reject-report" data-id="${report.report_id || report._id}" title="Reject">
                                    <i class="fas fa-times"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `;
        });


        reportsContainer.innerHTML = rowsHTML;
    }

    function updatePagination() {
        const totalPages = Math.ceil(filteredReports.length / reportsPerPage);
        const prevButton = document.getElementById('prevPage');
        const nextButton = document.getElementById('nextPage');


        const startIndex = filteredReports.length > 0 ? (currentPage - 1) * reportsPerPage + 1 : 0;
        const endIndex = Math.min(startIndex + reportsPerPage - 1, filteredReports.length);

        document.getElementById('showingStart').textContent = startIndex;
        document.getElementById('showingEnd').textContent = endIndex;
        document.getElementById('showingTotal').textContent = filteredReports.length;


        prevButton.disabled = currentPage <= 1;
        nextButton.disabled = currentPage >= totalPages;
    }

    function changePage(newPage) {
        const totalPages = Math.ceil(filteredReports.length / reportsPerPage);

        if (newPage >= 1 && newPage <= totalPages) {
            currentPage = newPage;
            renderReports();
            updatePagination();


            document.getElementById('reportsContainer').scrollIntoView({ behavior: 'smooth' });
        }
    }

    function showReportModal(reportId) {

        const report = allReports.find(r =>
            String(r.report_id) === String(reportId) ||
            (r._id && String(r._id) === String(reportId))
        );

        if (!report) {
            Swal.fire({
                icon: 'error',
                title: 'Report Not Found',
                text: `Could not find details for report #${reportId}.`
            });
            return;
        }


        let statusClass = 'secondary';
        let statusText = report.status || 'Unknown';
        let statusIcon = 'fa-clock';

        if (report.status === 'Pending') {
            statusClass = 'pending';
            statusIcon = 'fa-clock';
        } else if (report.status === 'Accepted') {
            statusClass = 'accepted';
            statusIcon = 'fa-check-circle';
        } else if (report.status === 'Denied') {
            statusClass = 'denied';
            statusIcon = 'fa-times-circle';
        }


        let priorityClass = 'secondary';
        let priorityText = 'Low';

        if (report.priority === 1) {
            priorityClass = 'danger';
            priorityText = 'High';
        } else if (report.priority === 2) {
            priorityClass = 'warning';
            priorityText = 'Urgent';
        } else if (report.priority === 3) {
            priorityClass = 'info';
            priorityText = 'Medium';
        } else if (report.priority === 4 || !report.priority) {
            priorityClass = 'secondary';
            priorityText = 'Low';
        }


        const createdDate = report.created_at_formatted || formatDate(report.created_at);
        const processedDate = report.status !== 'Pending'
            ? (report.processed_at_formatted || formatDate(report.processed_at))
            : null;

        // Start building the modal content with improved design
        let modalContent = `
            <div class="report-modal-improved">
                <!-- Header Section -->
                <div class="report-header bg-gradient-primary text-white p-4 rounded-top">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="report-icon-container me-3">
                                <i class="fas fa-flag fa-2x"></i>
                            </div>
                            <div>
                                <h4 class="mb-1 fw-bold">Report #${report.report_id || reportId}</h4>
                                <p class="mb-0 opacity-75">Reported ${report.report_type === 'faction' ? 'Faction' : 'Player'}: ${report.offender_name || 'Unknown'}</p>
                            </div>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-light text-dark fs-6 px-3 py-2">${statusText}</span>
                        </div>
                    </div>
                </div>

                <!-- Status Overview Cards -->
                <div class="p-4 bg-light border-bottom">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="status-card text-center p-3 bg-white rounded shadow-sm">
                                <div class="status-icon mb-2">
                                    <i class="fas ${statusIcon} fa-2x text-${statusClass}"></i>
                                </div>
                                <h6 class="mb-1 text-muted">Status</h6>
                                <p class="mb-0 fw-bold">${statusText}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="status-card text-center p-3 bg-white rounded shadow-sm">
                                <div class="status-icon mb-2">
                                    <i class="fas fa-calendar-alt fa-2x text-info"></i>
                                </div>
                                <h6 class="mb-1 text-muted">Submitted</h6>
                                <p class="mb-0 fw-bold">${createdDate}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="status-card text-center p-3 bg-white rounded shadow-sm">
                                <div class="status-icon mb-2">
                                    <i class="fas fa-exclamation-triangle fa-2x text-${priorityClass}"></i>
                                </div>
                                <h6 class="mb-1 text-muted">Priority</h6>
                                <p class="mb-0 fw-bold">${priorityText}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="p-4">
                    <!-- Reporter Information Section -->
                    <div class="info-section mb-4">
                        <div class="section-header mb-3">
                            <h5 class="mb-0 d-flex align-items-center">
                                <i class="fas fa-user-shield me-2 text-primary"></i>
                                Reporter Information
                            </h5>
                        </div>
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <div class="info-item">
                                            <label class="form-label text-muted mb-1">
                                                <i class="fas fa-user me-2"></i>
                                                Reporter Name
                                            </label>
                                            <div class="form-control-plaintext rounded px-3 py-2" style="background-color: var(--bs-secondary-bg); color: #ffffff !important; font-weight: 600;">
                                                <span style="color: #ffffff !important;">${report.reporter_name || 'Unknown'}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <!-- Reported Player/Faction Section -->
                    <div class="info-section mb-4">
                        <div class="section-header mb-3">
                            <h5 class="mb-0 d-flex align-items-center">
                                <i class="fas fa-${report.report_type === 'faction' ? 'users' : 'user'}-times me-2 text-danger"></i>
                                Reported ${report.report_type === 'faction' ? 'Faction' : 'Player'}
                            </h5>
                        </div>
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <label class="form-label text-muted mb-1">
                                                <i class="fas fa-${report.report_type === 'faction' ? 'users' : 'user'} me-2"></i>
                                                ${report.report_type === 'faction' ? 'Faction' : 'Player'} Name
                                            </label>
                                            <div class="form-control-plaintext rounded px-3 py-2" style="background-color: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff !important; font-weight: 600;">
                                                <span style="color: #ffffff !important; font-weight: bold;">${report.offender_name || 'Unknown'}</span>
                                                <span class="badge bg-${report.report_type === 'faction' ? 'info' : 'secondary'} ms-2">
                                                    ${report.report_type === 'faction' ? 'Faction' : 'Player'} Report
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <label class="form-label text-muted mb-1">
                                                <i class="fas fa-gavel me-2"></i>
                                                Rule Violation
                                            </label>
                                            <div class="form-control-plaintext rounded px-3 py-2" style="background-color: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2);">
                                                <span class="badge bg-warning text-dark fs-6">
                                                    ${report.rule_broken || 'Not specified'}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <!-- Evidence Section -->
                    <div class="info-section mb-4">
                        <div class="section-header mb-3">
                            <h5 class="mb-0 d-flex align-items-center">
                                <i class="fas fa-search me-2 text-info"></i>
                                Evidence & Description
                            </h5>
                        </div>
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="evidence-container bg-light rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                    ${report.evidence
                                        ? `<div class="evidence-text">${formatEvidenceText(report.evidence)}</div>`
                                        : '<div class="text-muted text-center py-3"><i class="fas fa-info-circle me-2"></i>No evidence provided</div>'}
                                </div>
                            </div>
                        </div>
                    </div>

                    ${report.status !== 'Pending' ? `
                    <!-- Resolution Section -->
                    <div class="info-section mb-4">
                        <div class="section-header mb-3">
                            <h5 class="mb-0 d-flex align-items-center">
                                <i class="fas fa-clipboard-check me-2 text-success"></i>
                                Resolution Details
                            </h5>
                        </div>
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <label class="form-label text-muted mb-1">
                                                <i class="fas fa-user-shield me-2"></i>
                                                Resolved By
                                            </label>
                                            <div class="form-control-plaintext rounded px-3 py-2" style="background-color: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff !important; font-weight: 600;">
                                                <span style="color: #ffffff !important; font-weight: bold;">${report.processed_by_name || 'Unknown'}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <label class="form-label text-muted mb-1">
                                                <i class="fas fa-clock me-2"></i>
                                                Resolved On
                                            </label>
                                            <div class="form-control-plaintext rounded px-3 py-2" style="background-color: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff !important; font-weight: 600;">
                                                <span style="color: #ffffff !important; font-weight: bold;">${processedDate || 'Unknown'}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;


        let footerContent = '';

        if (report.status === 'Pending') {
            footerContent = `
                <div class="d-flex justify-content-between w-100">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Close
                    </button>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-danger modal-reject-report" data-id="${report.report_id || reportId}">
                            <i class="fas fa-ban me-2"></i>Reject Report
                        </button>
                        <button type="button" class="btn btn-success modal-accept-report" data-id="${report.report_id || reportId}">
                            <i class="fas fa-check me-2"></i>Accept Report
                        </button>
                    </div>
                </div>
            `;
        } else {
            footerContent = `
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Close
                </button>
            `;
        }


        document.getElementById('reportModalContent').innerHTML = modalContent;
        document.getElementById('reportModalFooter').innerHTML = footerContent;


        document.getElementById('reportModalLabel').textContent = `Report #${report.report_id || reportId}`;


        reportModal.show();


    }





    function acceptReport(reportId) {

        if (reportModal) {
            reportModal.hide();
        }

        Swal.fire({
            title: '<i class="fas fa-check-circle text-success me-2"></i>Accept Report',
            html: `
                <div class="text-start">
                    <div class="alert alert-info border-0 mb-3" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        <strong>You're about to accept this report</strong>
                    </div>

                    <p class="mb-3" style="color: #ffffff;">This action confirms that the reported behavior violates server rules.</p>

                    <div class="rounded p-3 mb-3" style="border: 1px solid rgba(255, 255, 255, 0.2);">
                        <h6 class="mb-2" style="color: #28a745;"><i class="fas fa-clipboard-check me-2"></i>What happens next:</h6>
                        <ul class="mb-0 ps-3" style="color: #ffffff;">
                            <li class="mb-1" style="color: #ffffff;">✅ Report status changes to <span class="badge bg-success">Accepted</span></li>
                            <li class="mb-1" style="color: #ffffff;">📧 Reporter receives acceptance notification</li>
                            <li class="mb-1" style="color: #ffffff;">⚖️ Appropriate action should be taken against the offender</li>
                            <li class="mb-0" style="color: #ffffff;">📝 This decision is logged for audit purposes</li>
                        </ul>
                    </div>

                    <div class="text-center">
                        <small style="color: #6c757d;">
                            <i class="fas fa-shield-alt me-1"></i>
                            This action cannot be undone
                        </small>
                    </div>
                </div>
            `,
            icon: null,
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-check me-2"></i>Accept Report',
            cancelButtonText: '<i class="fas fa-times me-2"></i>Cancel',
            reverseButtons: true,
            focusCancel: true,
            customClass: {
                popup: 'swal-modern-popup',
                title: 'swal-modern-title',
                confirmButton: 'btn btn-success btn-lg px-4',
                cancelButton: 'btn btn-outline-secondary btn-lg px-4'
            },
            buttonsStyling: false
        }).then((result) => {
            if (result.isConfirmed) {

                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');





                fetch(`/adminapi/reports/accept?id=${reportId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Discord-ID': authData.userId,
                        'X-Discord-Username': authData.username,
                        'X-Discord-Role': authData.role,
                        'X-Discord-Avatar': authData.avatar || '',
                        'X-CSRF-TOKEN': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'include'
                })
                .then(response => {


                    if (!response.ok) {
                        if (response.status === 404) {
                            throw new Error('API endpoint not found. Please check server configuration.');
                        } else if (response.status === 403) {
                            throw new Error('Permission denied. You may not have the required role.');
                        } else if (response.status === 401) {
                            throw new Error('Authentication required. Please log in again.');
                        } else {
                            throw new Error(`Network response was not ok (Status: ${response.status})`);
                        }
                    }
                    return response.json();
                })
                .then(data => {
                    let message = data.message || 'Report has been accepted successfully.';


                    let htmlMessage = `
                        <div class="text-center">
                            <div class="mb-3">
                                <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                            </div>
                            <h4 class="text-success mb-3">Report Successfully Accepted!</h4>
                            <div class="alert alert-success border-0 mb-3" style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);">
                                <p class="mb-0">${message}</p>
                            </div>
                            <div class="bg-light rounded p-3">
                                <p class="mb-0 text-success">
                                    <i class="fas fa-envelope me-2"></i>
                                    The reporter will be automatically notified that appropriate action has been taken.
                                </p>
                            </div>
                        </div>
                    `;

                    Swal.fire({
                        icon: null,
                        title: null,
                        html: htmlMessage,
                        confirmButtonText: '<i class="fas fa-check me-2"></i>Continue',
                        customClass: {
                            popup: 'swal-modern-popup',
                            confirmButton: 'btn btn-success btn-lg px-4'
                        },
                        buttonsStyling: false
                    });


                    if (reportModal._isShown) {
                        reportModal.hide();
                    }


                    loadReports();
                })
                .catch(error => {
                    console.error('Error accepting report:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Failed to Accept Report',
                        text: 'There was a problem accepting the report. Please try again.'
                    });
                });
            }
        });
    }

    function rejectReport(reportId) {

        if (reportModal) {
            reportModal.hide();
        }

        Swal.fire({
            title: '<i class="fas fa-times-circle text-danger me-2"></i>Reject Report',
            html: `
                <div class="text-start">
                    <div class="alert alert-warning border-0 mb-3" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        <strong>You're about to reject this report</strong>
                    </div>

                    <p class="mb-3" style="color: #ffffff;">This action indicates that no server rules were violated or insufficient evidence was provided.</p>

                    <div class="rounded p-3 mb-3" style="border: 1px solid rgba(255, 255, 255, 0.2);">
                        <h6 class="mb-2" style="color: #dc3545;"><i class="fas fa-ban me-2"></i>What happens next:</h6>
                        <ul class="mb-0 ps-3" style="color: #ffffff;">
                            <li class="mb-1" style="color: #ffffff;">❌ Report status changes to <span class="badge bg-danger">Denied</span></li>
                            <li class="mb-1" style="color: #ffffff;">📧 Reporter receives rejection notification</li>
                            <li class="mb-1" style="color: #ffffff;">💡 Reporter is encouraged to provide more evidence if needed</li>
                            <li class="mb-0" style="color: #ffffff;">📝 This decision is logged for audit purposes</li>
                        </ul>
                    </div>

                    <div class="text-center">
                        <small style="color: #6c757d;">
                            <i class="fas fa-shield-alt me-1"></i>
                            This action cannot be undone
                        </small>
                    </div>
                </div>
            `,
            icon: null,
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-ban me-2"></i>Reject Report',
            cancelButtonText: '<i class="fas fa-times me-2"></i>Cancel',
            reverseButtons: true,
            focusCancel: true,
            customClass: {
                popup: 'swal-modern-popup',
                title: 'swal-modern-title',
                confirmButton: 'btn btn-danger btn-lg px-4',
                cancelButton: 'btn btn-outline-secondary btn-lg px-4'
            },
            buttonsStyling: false
        }).then((result) => {
            if (result.isConfirmed) {

                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');





                fetch(`/adminapi/reports/reject?id=${reportId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Discord-ID': authData.userId,
                        'X-Discord-Username': authData.username,
                        'X-Discord-Role': authData.role,
                        'X-Discord-Avatar': authData.avatar || '',
                        'X-CSRF-TOKEN': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'include'
                })
                .then(response => {


                    if (!response.ok) {
                        if (response.status === 404) {
                            throw new Error('API endpoint not found. Please check server configuration.');
                        } else if (response.status === 403) {
                            throw new Error('Permission denied. You may not have the required role.');
                        } else if (response.status === 401) {
                            throw new Error('Authentication required. Please log in again.');
                        } else {
                            throw new Error(`Network response was not ok (Status: ${response.status})`);
                        }
                    }
                    return response.json();
                })
                .then(data => {
                    let message = data.message || 'Report has been rejected successfully.';


                    let emailStatus = '';
                    if (data.notification_sent === true) {
                        emailStatus = '<p class="mt-2 text-success"><i class="fas fa-envelope"></i> A notification email has been sent to the reporter.</p>';
                    } else if (data.notification_sent === false) {
                        emailStatus = '<p class="mt-2 text-warning"><i class="fas fa-exclamation-triangle"></i> No notification email was sent.</p>';
                    }


                    let htmlMessage = `
                        <div class="text-center">
                            <div class="mb-3">
                                <i class="fas fa-times-circle text-danger" style="font-size: 3rem;"></i>
                            </div>
                            <h4 class="text-danger mb-3">Report Successfully Rejected</h4>
                            <div class="alert alert-warning border-0 mb-3" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);">
                                <p class="mb-0">${message}</p>
                            </div>
                            <div class="bg-light rounded p-3 mb-3">
                                <p class="mb-0 text-info">
                                    <i class="fas fa-envelope me-2"></i>
                                    The reporter will be notified that no action was deemed necessary.
                                </p>
                            </div>
                            ${emailStatus ? `<div class="text-muted small">${emailStatus}</div>` : ''}
                        </div>
                    `;

                    Swal.fire({
                        icon: null,
                        title: null,
                        html: htmlMessage,
                        confirmButtonText: '<i class="fas fa-check me-2"></i>Continue',
                        customClass: {
                            popup: 'swal-modern-popup',
                            confirmButton: 'btn btn-primary btn-lg px-4'
                        },
                        buttonsStyling: false
                    });


                    if (reportModal._isShown) {
                        reportModal.hide();
                    }


                    loadReports();
                })
                .catch(error => {
                    console.error('Error rejecting report:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Failed to Reject Report',
                        text: 'There was a problem rejecting the report. Please try again.'
                    });
                });
            }
        });
    }


    function debounce(func, wait) {
        let timeout;
        return function(...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }


    function formatEvidenceText(text) {
        if (!text) return '';


        return text.trim().replace(/\s+/g, ' ');
    }
});
