/* Force dark mode immediately to prevent white flash */
html, body {
    background-color: #1a202c !important;
    color: #f1f5f9 !important;
}

/* Force all elements to use dark theme by default */
* {
    background-color: inherit;
    color: inherit;
}

/* Override Bootstrap light theme components */
.card, .modal-content, .dropdown-menu {
    background-color: #2d3748 !important;
    color: #f1f5f9 !important;
    border-color: rgba(255,255,255,0.1) !important;
}

.bg-white, .bg-light {
    background-color: #2d3748 !important;
}

.text-dark {
    color: #f1f5f9 !important;
}

.text-muted {
    color: #94a3b8 !important;
}

:root {
    /* Layout */
    --sidebar-width: 280px;
    --navbar-height: 64px;
    --border-radius-sm: 6px;
    --border-radius-md: 10px;
    --border-radius-lg: 16px;
    --transition-speed: 0.3s;

    /* Colors - Dark Theme Only */
    --primary-color: #1e2a38;
    --secondary-color: #141c26;
    --accent-color: #3a86ff;
    --accent-hover: #2a76ef;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;

    /* Text Colors - Dark Mode */
    --text-light: #ffffff;
    --text-dark: #f1f5f9;
    --text-muted: #94a3b8;
    --text-secondary: #64748b;

    /* Background Colors - Dark Mode */
    --bg-light: #2d3748;
    --bg-dark: #0f172a;
    --bg-card: #2d3748;
    --bg-hover: rgba(255,255,255,0.03);

    /* Border Colors - Dark Mode */
    --border-color: rgba(255,255,255,0.1);
    --border-color-dark: rgba(255,255,255,0.1);

    /* Shadow */
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.07), 0 1px 3px rgba(0,0,0,0.08);
    --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.025);
}

body {
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    background: var(--bg-light);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    color: var(--text-dark);
    line-height: 1.6;
    font-size: 16px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark Mode Styles */
body.dark-mode {
    --bg-light: var(--bg-dark);
    --bg-card: var(--secondary-color);
    --text-dark: var(--text-light);
    --border-color: var(--border-color-dark);
    background-color: var(--bg-dark);
    color: var(--text-light);
}

/* Dark Mode Card Styles */
body.dark-mode .card {
    background-color: var(--secondary-color);
    border-color: var(--border-color-dark);
}

body.dark-mode .card-header {
    border-bottom-color: var(--border-color-dark);
}

body.dark-mode .card-footer {
    border-top-color: var(--border-color-dark);
}

/* Dark Mode Table Styles */
body.dark-mode .table {
    color: var(--text-light);
}

body.dark-mode .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Dark Mode Form Styles */
body.dark-mode .form-control,
body.dark-mode .form-select {
    background-color: var(--bg-dark);
    border-color: var(--border-color-dark);
    color: var(--text-light);
}

body.dark-mode .form-control:focus,
body.dark-mode .form-select:focus {
    border-color: var(--accent-color);
    background-color: rgba(0, 0, 0, 0.2);
}

/* Specific styling for rank modal form elements */
#rankModal .form-select,
#rankModal .form-control {
    background-color: #1a202c !important;
    border-color: #4a5568 !important;
    color: #ffffff !important;
}

#rankModal .form-select:focus,
#rankModal .form-control:focus {
    background-color: #1a202c !important;
    border-color: var(--accent-color) !important;
    box-shadow: 0 0 0 0.25rem rgba(77, 142, 252, 0.25) !important;
}

/* Style the select dropdown options */
#rankModal .form-select option {
    background-color: #1a202c !important;
    color: #ffffff !important;
}

/* Dark Mode Modal Styles */
body.dark-mode .modal-content {
    background-color: var(--secondary-color);
    border-color: var(--border-color-dark);
}

body.dark-mode .modal-header,
body.dark-mode .modal-footer {
    border-color: var(--border-color-dark);
}

/* Dark Mode Dropdown Styles */
body.dark-mode .dropdown-menu {
    background-color: var(--secondary-color);
    border-color: var(--border-color-dark);
}

body.dark-mode .dropdown-item {
    color: var(--text-light);
}

body.dark-mode .dropdown-item:hover,
body.dark-mode .dropdown-item:focus {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Dark Mode Nav Tabs */
body.dark-mode .nav-tabs {
    border-bottom-color: var(--border-color-dark);
}

body.dark-mode .nav-tabs .nav-link {
    color: var(--text-light);
}

body.dark-mode .nav-tabs .nav-link.active {
    background-color: var(--secondary-color);
    border-color: var(--border-color-dark);
    color: var(--accent-color);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
    line-height: 1.3;
}

h1 { font-size: 2rem; }
h2 { font-size: 1.75rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
}

a {
    color: var(--accent-color);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--accent-hover);
}

/* Navbar styling */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
    height: var(--navbar-height);
    background-color: var(--bg-card);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

/* Ensure navbar dropdowns appear above other elements */
.navbar-actions .dropdown-menu {
    z-index: 1090 !important;
}

/* Fix for the profile dropdown overlapping with buttons */
.navbar-actions .dropdown {
    position: relative; /* Changed from static to relative */
}

.navbar-actions .dropdown-menu {
    position: absolute;
    right: 0;
    top: 100%; /* Position below the dropdown toggle */
    margin-top: 0.5rem;
    width: 200px; /* Fixed width for the dropdown */
    box-shadow: var(--shadow-lg);
}

/* Fix for dropdown-divider overlapping with pages */
.dropdown-divider {
    border-top: 1px solid var(--border-color);
    margin: 0.5rem 0;
    opacity: 0.2;
}

/* Specific styling for user dropdown */
.user-dropdown {
    position: relative !important;
}

.user-dropdown-menu {
    position: absolute !important;
    right: 0 !important;
    top: 100% !important;
    margin-top: 0.5rem !important;
    width: 200px !important;
    box-shadow: var(--shadow-lg) !important;
    z-index: 1090 !important;
}

.navbar-brand {
    font-weight: 700;
    letter-spacing: 0.5px;
    font-size: 1.25rem;
    color: var(--text-dark);
    display: flex;
    align-items: center;
}

.navbar-brand img {
    height: 32px;
    margin-right: 0.75rem;
}

/* Sidebar styling */
.sidebar {
    position: fixed;
    top: var(--navbar-height);
    left: 0;
    bottom: 0;
    width: var(--sidebar-width);
    padding: 1.5rem 0;
    background-color: var(--primary-color);
    color: var(--text-light);
    overflow-y: auto;
    transition: transform var(--transition-speed);
    z-index: 1050; /* Increased z-index to be higher than buttons */
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-md);
}

.sidebar-section {
    margin-bottom: 1.5rem;
}

.sidebar-section-title {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--text-muted);
    padding: 0 1.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.sidebar a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.2s;
    border-left: 3px solid transparent;
    font-weight: 500;
}

.sidebar a:hover {
    background-color: rgba(255, 255, 255, 0.05);
    border-left-color: var(--accent-color);
}

.sidebar a.active {
    background-color: rgba(58, 134, 255, 0.15);
    border-left-color: var(--accent-color);
    color: var(--accent-color);
}

.sidebar a i {
    margin-right: 12px;
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Sidebar notification badge */
.sidebar .notification-badge {
    margin-left: auto;
    display: inline-block;
    position: relative;
}

.sidebar .notification-badge .badge {
    position: relative;
    top: -2px;
    font-size: 0.7rem;
    font-weight: 700;
    padding: 0.25rem 0.45rem;
    min-width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #e74c3c;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transform: scale(0.9);
    transition: transform 0.2s ease;
}

.sidebar a:hover .notification-badge .badge {
    transform: scale(1);
}

/* Hide badge when count is zero */
.sidebar .notification-badge .badge:empty {
    display: none !important;
}

/* Pulse animation for notification badge */
@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
    }

    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
    }

    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
    }
}

.pulse-animation {
    animation: pulse 1s ease-in-out;
}

/* User profile section in sidebar */
.sidebar-section.mt-auto {
    margin-top: auto !important;
    border-top: 1px solid var(--border-color-dark);
    padding-top: 1rem;
}

.sidebar-user-info {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    margin-bottom: 0.5rem;
}

.sidebar-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 0.75rem;
    background-color: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    overflow: hidden;
}

.sidebar-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.sidebar-user-details {
    flex: 1;
}

.sidebar-username {
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.sidebar-role {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.sidebar-discord-id {
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    background-color: rgba(0, 0, 0, 0.15) !important;
    border-left: 3px solid var(--accent-color) !important;
    margin: 1rem 0;
    position: relative;
    padding: 0.75rem 1.5rem;
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
}

.sidebar-discord-id:hover {
    background-color: rgba(0, 0, 0, 0.25) !important;
    transform: translateY(-2px);
    transition: all 0.2s ease;
}

.discord-id-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-family: monospace;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-light);
}

.copy-icon {
    margin-left: 0.5rem;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.copy-icon:hover {
    opacity: 1;
}

.sidebar .logout-btn {
    color: var(--text-muted);
    margin-top: 0.5rem;
}

.sidebar .sidebar-header {
    padding: 0 1.5rem 1rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--border-color-dark);
    display: flex;
    align-items: center;
}

.sidebar .sidebar-header .server-status {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
}

.sidebar .sidebar-header .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.sidebar .sidebar-header .status-indicator.online {
    background-color: var(--success-color);
}

.sidebar .sidebar-header .status-indicator.offline {
    background-color: var(--danger-color);
}

/* Main content area */
.main-content {
    margin-left: var(--sidebar-width);
    margin-top: var(--navbar-height);
    padding: 2rem;
    transition: margin-left var(--transition-speed);
    min-height: calc(100vh - var(--navbar-height));
    background-color: var(--bg-light);
    max-width: 100%;
    overflow-x: hidden; /* Prevent horizontal scrolling */
}

.content-header {
    margin-bottom: 2rem;
}

.content-header h1 {
    margin-bottom: 0.5rem;
}

.content-header .breadcrumb {
    margin-bottom: 0;
    font-size: 0.875rem;
}

.page-container {
    max-width: 1400px;
    margin: 0 auto;
}

/* Responsive sidebar */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
        box-shadow: none;
    }

    .sidebar.active {
        transform: translateX(0);
        box-shadow: var(--shadow-lg);
    }

    .main-content {
        margin-left: 0;
    }

    body.sidebar-open {
        overflow: hidden;
    }

    .sidebar-backdrop {
        display: none;
        position: fixed;
        top: var(--navbar-height);
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1049; /* Just below sidebar but above other content */
        transition: opacity 0.3s ease;
        opacity: 0;
        width: 100vw; /* Ensure it covers the entire viewport width */
        height: calc(100vh - var(--navbar-height)); /* Ensure it covers the entire viewport height */
    }

    body.sidebar-open .sidebar-backdrop {
        display: block;
        opacity: 1;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 1.25rem;
    }

    .navbar {
        padding: 0 1rem;
    }

    /* Fix button overlap with navbar on mobile */
    .btn, .btn-group .btn {
        z-index: 1020 !important; /* Lower than navbar (1030) to prevent overlap */
        position: relative;
    }

    /* Ensure card actions don't overlap */
    .card-actions .btn, .card-footer .btn {
        z-index: 1020 !important;
        position: relative;
    }
}

/* Card styling */
.card {
    box-shadow: var(--shadow-sm);
    border: none;
    border-radius: var(--border-radius-md);
    margin-bottom: 1.5rem;
    background-color: var(--bg-card);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid var(--border-color);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h5, .card-header .card-title {
    margin-bottom: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: transparent;
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

/* Stats Card */
.stats-card {
    display: flex;
    align-items: center;
}

.stats-card .stats-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.5rem;
    color: white;
}

.stats-card .stats-info h3 {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
    font-weight: 700;
}

.stats-card .stats-info p {
    margin-bottom: 0;
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Table styling */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--text-dark);
    vertical-align: middle;
    border-color: var(--border-color);
}

.table th {
    font-weight: 600;
    padding: 1rem;
    border-top: none;
    border-bottom-width: 1px;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    color: var(--text-secondary);
}

.table td {
    padding: 1rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: var(--bg-hover);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.01);
}

/* Button styling */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    position: relative;
    z-index: 1040;
}

.btn-primary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-primary:hover {
    background-color: var(--accent-hover);
    border-color: var(--accent-hover);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

.btn-icon {
    width: 36px;
    height: 36px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-icon.btn-sm {
    width: 30px;
    height: 30px;
}

.btn-icon.btn-lg {
    width: 48px;
    height: 48px;
}

/* Badge styling */
.badge {
    padding: 0.35em 0.65em;
    font-weight: 600;
    font-size: 0.75rem;
    border-radius: var(--border-radius-sm);
}

.badge.bg-primary {
    background-color: var(--accent-color) !important;
}

.badge.bg-success {
    background-color: var(--success-color) !important;
}

.badge.bg-warning {
    background-color: var(--warning-color) !important;
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
}

.badge.bg-info {
    background-color: var(--info-color) !important;
}

.badge.bg-secondary {
    background-color: rgba(41, 128, 185, 0.15) !important;
    color: #2980b9 !important;
}

/* Role badges */
.role-badge {
    padding: 0.35rem 0.65rem;
    border-radius: 50rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Use !important to ensure role-specific colors take precedence over generic badge styling */
.role-owner {
    background-color: rgba(155, 89, 182, 0.15) !important;
    color: #9b59b6 !important;
}

.role-developer {
    background-color: rgba(41, 128, 185, 0.15) !important;
    color: #2980b9 !important;
}

.role-supervisor {
    background-color: rgba(22, 160, 133, 0.15) !important;
    color: #16a085 !important;
}

.role-admin {
    background-color: rgba(231, 76, 60, 0.15) !important;
    color: #e74c3c !important;
}

.role-moderator {
    background-color: rgba(243, 156, 18, 0.15) !important;
    color: #f39c12 !important;
}

.role-trainee {
    background-color: rgba(142, 68, 173, 0.15) !important;
    color: #8e44ad !important;
}

.role-unauthorized {
    background-color: rgba(149, 165, 166, 0.15) !important;
    color: #95a5a6 !important;
}

/* DataTables customization */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    margin: 1rem 0;
}

.dataTables_wrapper .dataTables_filter input {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 0.375rem 0.75rem;
    margin-left: 0.5rem;
}

.dataTables_wrapper .dataTables_length select {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 0.375rem 0.5rem;
    margin: 0 0.5rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: var(--border-radius-sm);
    padding: 0.375rem 0.75rem;
    margin: 0 0.25rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: var(--accent-color);
    border-color: var(--accent-color);
    color: white !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: var(--bg-hover);
    border-color: var(--border-color);
}

/* Modal enhancements */
.modal-content {
    border: none;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
}

/* Ensure modals appear above all other elements */
.modal {
    z-index: 1080 !important;
}

.modal-backdrop {
    z-index: 1075 !important;
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: 1.25rem 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 1.25rem 1.5rem;
}

.modal-title {
    font-weight: 600;
    font-size: 1.25rem;
    line-height: 1.4;
    color: inherit;
    margin: 0;
    padding: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* Form controls */
.form-control, .form-select {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 0.5rem 0.75rem;
    font-size: 0.95rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.25rem rgba(58, 134, 255, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-text {
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Ensure all textareas have left-aligned text */
textarea,
textarea.form-control {
    text-align: left !important;
    text-align-last: left !important;
    direction: ltr;
}

/* Fix for SweetAlert2 textareas */
.swal2-textarea,
.swal2-html-container textarea {
    text-align: left !important;
    text-align-last: left !important;
    direction: ltr;
}

/* Ensure SweetAlert2 dialogs appear above all other elements */
.swal2-container {
    z-index: 2000 !important; /* Higher than any other element */
}

.swal2-popup {
    z-index: 2001 !important;
}

.form-check-input:checked {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 1rem 1.25rem;
}

.alert-primary {
    background-color: rgba(58, 134, 255, 0.15);
    color: var(--accent-color);
}

.alert-success {
    background-color: rgba(46, 204, 113, 0.15);
    color: var(--success-color);
}

.alert-warning {
    color: var(--warning-color);
}

.alert-danger {
    background-color: rgba(231, 76, 60, 0.15);
    color: var(--danger-color);
}

/* Utilities */
.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-sm {
    width: 32px;
    height: 32px;
}

.avatar-lg {
    width: 56px;
    height: 56px;
}

.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.border-dashed {
    border-style: dashed !important;
}

.cursor-pointer {
    cursor: pointer;
}

/* Loading spinner */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h4 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.empty-state p {
    max-width: 400px;
    margin: 0 auto;
}

/* Notifications */
.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 8px;
    background-color: var(--danger-color);
    border-radius: 50%;
    display: none;
}

.notifications-container {
    max-height: 350px;
    overflow-y: auto;
    padding: 0;
}

.notification-item {
    display: flex;
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    position: relative;
    transition: background-color 0.2s ease;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.notification-item.unread {
    background-color: rgba(58, 134, 255, 0.05);
}

.notification-item.unread:hover {
    background-color: rgba(58, 134, 255, 0.1);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(58, 134, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.notification-icon i {
    color: var(--accent-color);
    font-size: 1rem;
}

/* Maintenance notification styling */
.notification-icon.maintenance,
.notification-icon.warning {
    background-color: rgba(243, 156, 18, 0.2);
}

.notification-icon.maintenance i,
.notification-icon.warning i {
    color: var(--warning-color);
}

/* Announcement notification styling */
.notification-icon.announcement {
    background-color: rgba(58, 134, 255, 0.2);
}

.notification-icon.announcement i {
    color: var(--accent-color);
}

/* Make maintenance notifications more visible */
.notification-item.maintenance,
.notification-item.warning {
    background-color: rgba(243, 156, 18, 0.1);
}

.notification-item.maintenance:hover,
.notification-item.warning:hover {
    background-color: rgba(243, 156, 18, 0.15);
}

.notification-item.maintenance.unread,
.notification-item.warning.unread {
    background-color: rgba(243, 156, 18, 0.15);
}

.notification-item.maintenance.unread:hover,
.notification-item.warning.unread:hover {
    background-color: rgba(243, 156, 18, 0.2);
}

/* Announcement notification styling */
.notification-item.announcement {
    background-color: rgba(58, 134, 255, 0.05);
}

.notification-item.announcement:hover {
    background-color: rgba(58, 134, 255, 0.1);
}

.notification-item.announcement.unread {
    background-color: rgba(58, 134, 255, 0.1);
}

.notification-item.announcement.unread:hover {
    background-color: rgba(58, 134, 255, 0.15);
}

body.dark-mode .notification-item.maintenance,
body.dark-mode .notification-item.warning {
    background-color: rgba(243, 156, 18, 0.15);
}

body.dark-mode .notification-item.maintenance:hover,
body.dark-mode .notification-item.warning:hover {
    background-color: rgba(243, 156, 18, 0.2);
}

body.dark-mode .notification-item.maintenance.unread,
body.dark-mode .notification-item.warning.unread {
    background-color: rgba(243, 156, 18, 0.25);
}

body.dark-mode .notification-item.maintenance.unread:hover,
body.dark-mode .notification-item.warning.unread:hover {
    background-color: rgba(243, 156, 18, 0.3);
}

body.dark-mode .notification-item.announcement {
    background-color: rgba(58, 134, 255, 0.1);
}

body.dark-mode .notification-item.announcement:hover {
    background-color: rgba(58, 134, 255, 0.15);
}

body.dark-mode .notification-item.announcement.unread {
    background-color: rgba(58, 134, 255, 0.2);
}

body.dark-mode .notification-item.announcement.unread:hover {
    background-color: rgba(58, 134, 255, 0.25);
}

.notification-content {
    flex-grow: 1;
    padding-right: 24px;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 4px;
    font-size: 0.9rem;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-bottom: 4px;
    white-space: pre-line;
    line-height: 1.4;
}

.notification-time {
    color: var(--text-muted);
    font-size: 0.75rem;
}

.mark-read-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: none;
    color: var(--accent-color);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.notification-item:hover .mark-read-btn {
    opacity: 1;
}

.mark-read-btn:hover {
    background-color: rgba(58, 134, 255, 0.1);
}

body.dark-mode .notification-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .notification-item.unread {
    background-color: rgba(58, 134, 255, 0.15);
}

body.dark-mode .notification-item.unread:hover {
    background-color: rgba(58, 134, 255, 0.2);
}

/* Dark mode for SweetAlert popups */
body.dark-mode .swal2-popup {
    background-color: var(--secondary-color) !important;
    color: var(--text-light) !important;
    border: 1px solid var(--border-color) !important;
}

body.dark-mode .swal2-title {
    color: var(--text-light) !important;
}

body.dark-mode .swal2-content {
    color: var(--text-light) !important;
}

body.dark-mode .swal2-html-container {
    color: var(--text-light) !important;
}

body.dark-mode .swal2-html-container p,
body.dark-mode .swal2-html-container div,
body.dark-mode .swal2-html-container span,
body.dark-mode .swal2-html-container ul

body.dark-mode .swal2-html-container .alert {
    border-color: rgba(255, 255, 255, 0.2) !important;
}

body.dark-mode .swal2-html-container .bg-light {
    color: var(--text-light) !important;
}

body.dark-mode .swal2-html-container .text-muted {
    color: var(--text-muted) !important;
}

body.dark-mode .swal2-backdrop {
    background-color: rgba(0, 0, 0, 0.7) !important;
}

/* Dark mode for SweetAlert buttons */
body.dark-mode .swal2-confirm {
    background-color: var(--success-color) !important;
    border-color: var(--success-color) !important;
}

body.dark-mode .swal2-cancel {
    background-color: var(--secondary-color) !important;
    border-color: var(--border-color) !important;
    color: var(--text-light) !important;
}

body.dark-mode .swal2-deny {
    background-color: var(--danger-color) !important;
    border-color: var(--danger-color) !important;
}

/* Disable Bootstrap bg-dark background in report UI */
.bg-dark {
    background-color: transparent !important;
}

/* Fix text overflow in application responses */
.border.rounded.p-2.bg-dark.border-secondary,
.application-response-box {
    word-wrap: break-word !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    white-space: pre-wrap !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    hyphens: auto !important;
}

/* General text overflow fixes for admin modals */
.modal-body .border.rounded,
.modal-body .bg-dark {
    word-wrap: break-word !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    white-space: pre-wrap !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Ensure table cells don't overflow */
.table td {
    word-wrap: break-word !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    max-width: 200px !important;
    overflow-x: hidden !important;
}