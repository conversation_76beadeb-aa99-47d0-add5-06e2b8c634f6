<?php
/**
 * Admin Dashboard - Staff Applications Management
 * View and manage staff applications
 */


require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/theme-handler.php';
require_once __DIR__ . '/../includes/db_access.php';


require_auth(ROLE_ADMIN);


$applications = [];
try {
    $connection = get_db_connection();
    $db = $connection['db'];

    if ($db) {
        // Check if staff_applications collection exists
        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (in_array('staff_applications', $collections)) {
            $applicationsData = $db->staff_applications->find([], [
                'sort' => ['submitted_at' => -1]
            ]);

            foreach ($applicationsData as $app) {
                // Get Xbox username for this applicant
                $xbox_username = null;
                try {
                    if (isset($app['discord_id'])) {
                        $xbox_link = $db->linked_accounts->findOne([
                            'discord_id' => $app['discord_id'],
                            'platform' => 'xbox',
                            'status' => 'verified'
                        ]);
                        if ($xbox_link) {
                            $xbox_username = $xbox_link['username'];
                        }
                    }
                } catch (Exception $e) {
                    secure_log("Error fetching Xbox username for application: " . $e->getMessage());
                }

                // Add Xbox username to application data
                $app['xbox_username'] = $xbox_username;
                $applications[] = $app;
            }
        }
    }
} catch (Exception $e) {
    secure_log("Error fetching staff applications for admin: " . $e->getMessage());
    secure_log("Error fetching staff applications for admin", "error", [
        'admin_id' => $_SESSION['discord_user_id'] ?? 'unknown',
        'error' => $e->getMessage()
    ]);
}


$totalApplications = count($applications);
$pendingApplications = count(array_filter($applications, fn($app) => $app['status'] === 'pending'));
$approvedApplications = count(array_filter($applications, fn($app) => $app['status'] === 'approved'));
$deniedApplications = count(array_filter($applications, fn($app) => $app['status'] === 'denied'));
$underReviewApplications = count(array_filter($applications, fn($app) => $app['status'] === 'under_review'));
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <title>Staff Applications | MassacreMC Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php include '../includes/auth-data.php'; ?>
    <link href="../css/admin-common.css" rel="stylesheet">
    <link href="../css/modern-theme.css" rel="stylesheet">
    <link href="../css/mobile.css" rel="stylesheet">
    <link href="../css/dashboard.css" rel="stylesheet">
    <style>
        .application-card {
            transition: all 0.2s ease;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .application-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

        .status-badge {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.35rem 0.65rem;
            border-radius: 50rem;
        }

        .position-badge {
            background-color: rgba(58, 134, 255, 0.15);
            color: #3a86ff;
            border: 1px solid rgba(58, 134, 255, 0.3);
        }

        /* Dark dropdown styling */
        .form-select {
            background-color: #2d3748 !important;
            border-color: #4a5568 !important;
            color: #ffffff !important;
        }

        .form-select:focus {
            background-color: #2d3748 !important;
            border-color: #3a86ff !important;
            box-shadow: 0 0 0 0.2rem rgba(58, 134, 255, 0.25) !important;
            color: #ffffff !important;
        }

        .form-select option {
            background-color: #2d3748 !important;
            color: #ffffff !important;
        }

        /* Ensure dropdown menu has dark background */
        .dropdown-menu {
            background-color: #2d3748 !important;
            border-color: #4a5568 !important;
        }

        .dropdown-item {
            color: #ffffff !important;
        }

        .dropdown-item:hover,
        .dropdown-item:focus {
            background-color: #4a5568 !important;
            color: #ffffff !important;
        }






    </style>
</head>
<body>
    <!-- Include standard navbar -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Include standard sidebar -->
    <?php include '../includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div id="content" class="main-content">
        <div class="page-container">
            <!-- Content Header -->
            <div class="content-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1>Staff Applications</h1>
                        <p class="text-muted">Manage and review staff applications</p>
                    </div>
                    <div class="d-flex gap-2">
                        <span class="badge bg-info"><?php echo $totalApplications; ?> Total</span>
                        <span class="badge bg-warning text-dark"><?php echo $pendingApplications; ?> Pending</span>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row g-3 mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-user-tie text-primary fa-lg"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h3 class="mb-0"><?php echo $totalApplications; ?></h3>
                                    <p class="text-muted mb-0">Total Applications</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-clock text-warning fa-lg"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h3 class="mb-0"><?php echo $pendingApplications; ?></h3>
                                    <p class="text-muted mb-0">Pending Review</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-check-circle text-success fa-lg"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h3 class="mb-0"><?php echo $approvedApplications; ?></h3>
                                    <p class="text-muted mb-0">Approved</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-danger bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-times-circle text-danger fa-lg"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h3 class="mb-0"><?php echo $deniedApplications; ?></h3>
                                    <p class="text-muted mb-0">Denied</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (empty($applications)): ?>
            <!-- No Applications -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-user-tie fa-5x text-muted mb-4"></i>
                    <h3>No Applications Found</h3>
                    <p class="text-muted">No staff applications have been submitted yet.</p>
                </div>
            </div>
            <?php else: ?>

            <!-- Applications List -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">All Applications</h5>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" id="statusFilter" style="width: auto;">
                            <option value="">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="under_review">Under Review</option>
                            <option value="approved">Approved</option>
                            <option value="denied">Denied</option>
                        </select>
                        <select class="form-select form-select-sm" id="positionFilter" style="width: auto;">
                            <option value="">All Positions</option>
                            <option value="trainee">Trainee</option>
                            <option value="builder">Builder</option>
                            <option value="developer">Developer</option>
                        </select>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="applicationsTable">
                            <thead>
                                <tr>
                                    <th>Applicant</th>
                                    <th>Position</th>
                                    <th>Age</th>
                                    <th>Timezone</th>
                                    <th>Status</th>
                                    <th>Submitted</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($applications as $app): ?>
                                <tr data-status="<?php echo $app['status']; ?>" data-position="<?php echo $app['position']; ?>">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                                <i class="fas fa-user text-primary"></i>
                                            </div>
                                            <div>
                                                <div class="fw-semibold"><?php echo htmlspecialchars($app['discord_username']); ?></div>
                                                <small class="text-muted"><?php echo htmlspecialchars($app['discord_id']); ?></small>
                                                <?php if (!empty($app['xbox_username'])): ?>
                                                <div class="mt-1">
                                                    <span class="badge bg-success bg-opacity-10 text-success border border-success border-opacity-25">
                                                        <?php echo htmlspecialchars($app['xbox_username']); ?>
                                                    </span>
                                                </div>
                                                <?php else: ?>
                                                <div class="mt-1">
                                                    <span class="badge bg-warning bg-opacity-10 text-warning border border-warning border-opacity-25">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>No Xbox Linked
                                                    </span>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge position-badge"><?php echo htmlspecialchars(ucfirst($app['position'])); ?></span>
                                    </td>
                                    <td><?php echo htmlspecialchars($app['age']); ?> years</td>
                                    <td><?php echo htmlspecialchars($app['timezone']); ?></td>
                                    <td>
                                        <?php
                                        $status = $app['status'];
                                        $statusClass = '';
                                        $statusIcon = '';
                                        switch ($status) {
                                            case 'pending':
                                                $statusClass = 'bg-warning text-dark';
                                                $statusIcon = 'fas fa-clock';
                                                break;
                                            case 'approved':
                                                $statusClass = 'bg-success';
                                                $statusIcon = 'fas fa-check-circle';
                                                break;
                                            case 'denied':
                                                $statusClass = 'bg-danger';
                                                $statusIcon = 'fas fa-times-circle';
                                                break;
                                            case 'under_review':
                                                $statusClass = 'bg-info';
                                                $statusIcon = 'fas fa-eye';
                                                break;
                                            default:
                                                $statusClass = 'bg-secondary';
                                                $statusIcon = 'fas fa-question-circle';
                                        }
                                        ?>
                                        <span class="badge status-badge <?php echo $statusClass; ?>">
                                            <i class="<?php echo $statusIcon; ?> me-1"></i>
                                            <?php echo htmlspecialchars(ucfirst(str_replace('_', ' ', $status))); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="text-muted small">
                                            <?php echo $app['submitted_at']->toDateTime()->format('M j, Y'); ?>
                                            <br>
                                            <?php echo $app['submitted_at']->toDateTime()->format('g:i A'); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="viewApplication('<?php echo $app['application_id']; ?>')" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if ($app['status'] === 'pending' || $app['status'] === 'under_review'): ?>
                                            <button class="btn btn-outline-success" onclick="showStatusModal('<?php echo $app['application_id']; ?>', 'approved')" title="Approve">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="showStatusModal('<?php echo $app['application_id']; ?>', 'denied')" title="Deny">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            <?php endif; ?>
                                            <?php if ($app['status'] === 'pending'): ?>
                                            <button class="btn btn-outline-info" onclick="updateStatus('<?php echo $app['application_id']; ?>', 'under_review')" title="Mark Under Review">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>

        </div>
    </div>

    <!-- Application Details Modal -->
    <div class="modal fade" id="applicationModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-tie me-2"></i>Application Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="applicationDetails">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Update Modal -->
    <div class="modal fade" id="statusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>Update Application Status
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="statusUpdateForm">
                        <input type="hidden" id="statusApplicationId" name="application_id">
                        <input type="hidden" id="statusNewStatus" name="status">

                        <div class="mb-3">
                            <label class="form-label fw-semibold">New Status:</label>
                            <div id="statusDisplay" class="badge fs-6 mb-3"></div>
                        </div>

                        <div class="mb-3">
                            <label for="staffNotes" class="form-label fw-semibold">
                                <i class="fas fa-comment me-1"></i>Staff Notes
                            </label>
                            <textarea class="form-control" id="staffNotes" name="reviewer_notes" rows="4"
                                placeholder="Add notes about this decision (optional)..."></textarea>
                            <div class="form-text">
                                These notes will be visible to the applicant and other staff members.
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-primary" onclick="confirmStatusUpdate()">
                        <i class="fas fa-check me-1"></i>Update Status
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Include common footer with scripts -->
    <?php include '../includes/footer.php'; ?>

    <!-- Page specific scripts -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Filter functionality
        document.getElementById('statusFilter').addEventListener('change', filterApplications);
        document.getElementById('positionFilter').addEventListener('change', filterApplications);

        function filterApplications() {
            const statusFilter = document.getElementById('statusFilter').value;
            const positionFilter = document.getElementById('positionFilter').value;
            const rows = document.querySelectorAll('#applicationsTable tbody tr');

            rows.forEach(row => {
                const status = row.getAttribute('data-status');
                const position = row.getAttribute('data-position');

                const statusMatch = !statusFilter || status === statusFilter;
                const positionMatch = !positionFilter || position === positionFilter;

                if (statusMatch && positionMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function viewApplication(applicationId) {
            // Show loading state
            $('#applicationDetails').html(`
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `);

            // Show modal
            $('#applicationModal').modal('show');

            // Load application details via AJAX
            $.get(`/adminapi/staff-applications/details?id=${applicationId}`)
                .done(function(data) {
                    $('#applicationDetails').html(data);
                })
                .fail(function() {
                    $('#applicationDetails').html(`
                        <div class="text-center py-5">
                            <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                            <h5>Error Loading Application</h5>
                            <p class="text-muted">Failed to load application details. Please try again.</p>
                        </div>
                    `);
                });
        }

        function showStatusModal(applicationId, status) {
            const statusText = status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');
            const statusColors = {
                'approved': 'bg-success',
                'denied': 'bg-danger',
                'under_review': 'bg-info'
            };

            // Set modal data
            document.getElementById('statusApplicationId').value = applicationId;
            document.getElementById('statusNewStatus').value = status;
            document.getElementById('staffNotes').value = '';

            // Update status display
            const statusDisplay = document.getElementById('statusDisplay');
            statusDisplay.textContent = statusText;
            statusDisplay.className = `badge fs-6 mb-3 ${statusColors[status] || 'bg-secondary'}`;

            // Show modal
            new bootstrap.Modal(document.getElementById('statusModal')).show();
        }

        function confirmStatusUpdate() {
            const applicationId = document.getElementById('statusApplicationId').value;
            const status = document.getElementById('statusNewStatus').value;
            const notes = document.getElementById('staffNotes').value;
            const statusText = status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');

            // Hide modal
            bootstrap.Modal.getInstance(document.getElementById('statusModal')).hide();

            // Show loading
            Swal.fire({
                title: 'Processing...',
                text: 'Updating application status',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.post('/adminapi/staff-applications/update-status', {
                application_id: applicationId,
                status: status,
                reviewer_notes: notes,
                csrf_token: $('meta[name="csrf-token"]').attr('content')
            })
            .done(function(response) {
                Swal.fire({
                    title: 'Success!',
                    text: `Application ${statusText.toLowerCase()} successfully`,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    location.reload();
                });
            })
            .fail(function(xhr) {
                let errorMessage = 'Failed to update application status';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                Swal.fire({
                    title: 'Error',
                    text: errorMessage,
                    icon: 'error'
                });
            });
        }

        function updateStatus(applicationId, status) {
            // For simple status updates (like under_review) without notes
            const statusText = status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');

            Swal.fire({
                title: `${statusText} Application?`,
                text: `Are you sure you want to mark this application as ${statusText.toLowerCase()}?`,
                icon: 'info',
                showCancelButton: true,
                confirmButtonText: `Yes, ${statusText}`,
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Processing...',
                        text: 'Updating application status',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    $.post('/adminapi/staff-applications/update-status', {
                        application_id: applicationId,
                        status: status,
                        csrf_token: $('meta[name="csrf-token"]').attr('content')
                    })
                    .done(function(response) {
                        Swal.fire({
                            title: 'Success!',
                            text: `Application ${statusText.toLowerCase()} successfully`,
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        }).then(() => {
                            location.reload();
                        });
                    })
                    .fail(function(xhr) {
                        let errorMessage = 'Failed to update application status';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            title: 'Error',
                            text: errorMessage,
                            icon: 'error'
                        });
                    });
                }
            });
        }

        // Auto-refresh every 30 seconds for real-time updates
        setInterval(() => {
            // Only refresh if no modal is open
            if (!document.querySelector('.modal.show')) {
                location.reload();
            }
        }, 30000);


    </script>

    <!-- Admin Scripts -->
    <script src="../js/admin-common.min.js"></script>
    <script src="../js/toggleSidebar-fix.min.js"></script>
</body>
</html>
