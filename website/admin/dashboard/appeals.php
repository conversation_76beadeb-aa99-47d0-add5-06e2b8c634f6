<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/theme-handler.php'; 


require_auth(ROLE_ADMIN); // ADMIN_ROLE or higher
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Portal - Appeals</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.5/css/dataTables.bootstrap5.min.css">
    <script>
window.AUTH_DATA = {
    authenticated: <?php echo is_authenticated() ? 'true' : 'false'; ?>,
    userId: "<?php echo htmlspecialchars($_SESSION['discord_user_id'] ?? ''); ?>",
    username: "<?php echo htmlspecialchars($_SESSION['discord_username'] ?? ''); ?>",
    role: "<?php echo htmlspecialchars($_SESSION['discord_user_role'] ?? ''); ?>",
    hasRequiredRole: <?php echo has_role(ROLE_TRAINEE) ? 'true' : 'false'; ?>,
    sessionId: "<?php echo session_id(); ?>"
};
</script>
<meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <link href="/css/admin-common.css" rel="stylesheet">
    <link href="/css/appeals.css" rel="stylesheet">
    <link href="/css/appeal-details.css" rel="stylesheet">
    <link href="/css/appeal-details-new.css" rel="stylesheet">
    <link href="/css/appeal-cards.css" rel="stylesheet">
    <style>
        /* Dark dropdown styling */
        .form-select {
            background-color: #2d3748 !important;
            border-color: #4a5568 !important;
            color: #e2e8f0 !important;
        }

        .form-select:focus {
            background-color: #2d3748 !important;
            border-color: #007bff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
            color: #e2e8f0 !important;
        }

        .form-select option {
            background-color: #2d3748 !important;
            color: #e2e8f0 !important;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
      <div class="container-fluid">
        <a class="navbar-brand" href="#">MassacreMC Staff</a>
        <button id="sidebarToggle" class="navbar-toggler text-white" type="button" onclick="toggleSidebar()">
            <i class="fa fa-bars"></i>
        </button>
      </div>
    </nav>

    <!-- Include standard navbar -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Include standard sidebar -->
    <?php include '../includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div id="content" class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">Appeals Management</h1>
                <div>
                    <span class="badge bg-warning me-2">Pending: <span id="pendingCount">0</span></span>
                    <span class="badge bg-secondary">Total: <span id="totalCount">0</span></span>
                </div>
            </div>

            <!-- Filter and Search Controls -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="row g-3 align-items-center">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" id="appealSearch" class="form-control" placeholder="Search appeals...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select id="statusFilter" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="Pending">Pending</option>
                                <option value="Approved">Approved</option>
                                <option value="Denied">Denied</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select id="sortOrder" class="form-select">
                                <option value="newest">Newest First</option>
                                <option value="oldest">Oldest First</option>
                                <option value="player_asc">Player Name (A-Z)</option>
                                <option value="player_desc">Player Name (Z-A)</option>
                            </select>
                        </div>
                        <div class="col-md-2 text-end">
                            <button id="refreshAppeals" class="btn btn-primary">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Appeals Card Container -->
            <div id="appealsContainer" class="row g-3">
                <!-- Appeals will be loaded here -->
                <div class="col-12 text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 text-muted">Loading appeals...</p>
                </div>
            </div>

            <!-- Pagination Controls -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div class="showing-info text-muted">
                    Showing <span id="showingCount">0</span> of <span id="totalCount">0</span> appeals
                </div>
                <div class="pagination-controls">
                    <button id="loadMoreBtn" class="btn btn-outline-primary" style="display: none;">
                        Load More <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Appeal Details Sidebar -->
    <div id="appealDetails" class="appeal-details">
        <div id="appealContent">
            <!-- Content will be loaded dynamically -->
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="../js/admin-common.min.js"></script>
    <script src="../js/appeals.min.js"></script>
</body>
</html>