<?php

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/theme-handler.php'; 

require_auth(ROLE_DEVELOPER); // DEVELOPER_ROLE or higher
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <title>Audit Logs | MassacreMC Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <script>
        window.AUTH_DATA = {
            authenticated: <?php echo is_authenticated() ? 'true' : 'false'; ?>,
            userId: "<?php echo htmlspecialchars($_SESSION['discord_user_id'] ?? ''); ?>",
            username: "<?php echo htmlspecialchars($_SESSION['discord_username'] ?? ''); ?>",
            role: "<?php echo htmlspecialchars($_SESSION['discord_user_role'] ?? ''); ?>",
            hasRequiredRole: <?php echo has_role(ROLE_TRAINEE) ? 'true' : 'false'; ?>,
            sessionId: "<?php echo session_id(); ?>",
            avatar: "<?php echo htmlspecialchars($_SESSION['discord_avatar'] ?? ''); ?>"
        };
    </script>
    <link rel="stylesheet" href="/css/admin-common.css">
    <link rel="stylesheet" href="/css/mobile.css">
    <link rel="stylesheet" href="/css/mobile-fixes.css">
    <link rel="stylesheet" href="/css/audit-cards.css">
    <style>
        .audit-log-table th, .audit-log-table td {
            vertical-align: middle;
        }

        .audit-log-filters {
            background-color: var(--bg-dark);
            border-radius: var(--border-radius-md);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-sm);
        }

        .audit-log-filters .form-label {
            font-weight: 600;
            color: var(--text-light);
        }

        .audit-type-badge {
            padding: 0.35rem 0.65rem;
            border-radius: 50rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .audit-type-login {
            background-color: rgba(52, 152, 219, 0.15);
            color: #3498db;
        }

        .audit-type-report {
            background-color: rgba(46, 204, 113, 0.15);
            color: #2ecc71;
        }

        .audit-type-note {
            background-color: rgba(155, 89, 182, 0.15);
            color: #9b59b6;
        }

        .audit-type-punishment {
            background-color: rgba(231, 76, 60, 0.15);
            color: #e74c3c;
        }

        .audit-type-punishment_removal {
            background-color: rgba(40, 167, 69, 0.15);
            color: #28a745;
        }

        .audit-type-appeal {
            background-color: rgba(243, 156, 18, 0.15);
            color: #f39c12;
        }

        .audit-type-settings {
            background-color: rgba(52, 73, 94, 0.15);
            color: #34495e;
        }

        .audit-type-unknown {
            background-color: rgba(189, 195, 199, 0.15);
            color: #bdc3c7;
        }

        .staff-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 600;
            margin-right: 0.75rem;
        }

        .staff-info {
            display: flex;
            align-items: center;
        }

        .staff-info .staff-name {
            font-weight: 600;
            margin-bottom: 0;
        }

        .audit-details {
            max-width: 300px;
            white-space: normal;
            word-break: break-word;
            background-color: rgba(0, 0, 0, 0.03);
            padding: 8px;
            border-radius: 4px;
            border-left: 3px solid #ccc;
            font-family: monospace;
            font-size: 0.85rem;
        }

        .audit-details code {
            background-color: rgba(0, 0, 0, 0.05);
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
            color: #333;
        }

        .audit-details .badge {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            font-weight: 600;
        }

        body.dark-mode .audit-details code {
            background-color: rgba(255, 255, 255, 0.1);
            color: #eee;
        }

        /* Dark mode styles for audit details */
        body.dark-mode .audit-details {
            background-color: rgba(255, 255, 255, 0.05);
            border-left-color: #555;
        }

        /* DataTables dark mode fixes */
        body.dark-mode .dataTables_wrapper .dataTables_length,
        body.dark-mode .dataTables_wrapper .dataTables_filter,
        body.dark-mode .dataTables_wrapper .dataTables_info,
        body.dark-mode .dataTables_wrapper .dataTables_processing,
        body.dark-mode .dataTables_wrapper .dataTables_paginate {
            color: var(--text-light);
        }

        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button {
            color: var(--text-light) !important;
        }

        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
            color: #333 !important;
        }

        body.dark-mode .dataTables_wrapper .dataTables_length select,
        body.dark-mode .dataTables_wrapper .dataTables_filter input {
            background-color: var(--bg-darker);
            color: var(--text-light);
            border-color: var(--border-color);
        }

        /* Dark dropdown styling */
        .form-select {
            background-color: #2d3748 !important;
            border-color: #4a5568 !important;
            color: #e2e8f0 !important;
        }

        .form-select:focus {
            background-color: #2d3748 !important;
            border-color: #007bff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
            color: #e2e8f0 !important;
        }

        .form-select option {
            background-color: #2d3748 !important;
            color: #e2e8f0 !important;
        }

        .audit-timestamp {
            white-space: nowrap;
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        /* Fix for the sidebar overlap */
        .admin-container {
            display: flex;
            min-height: 100vh;
            width: 100%;
            position: relative;
            overflow-x: hidden;
        }

        /* Removed custom sidebar positioning that was causing the issue */

        .admin-content {
            flex: 1;
            margin-left: 250px;
            width: calc(100% - 250px);
            min-height: 100vh;
        }

        /* Fix for dropdown menus appearing behind other elements */
        .dropdown-menu {
            z-index: 1050 !important;
        }

        /* Fix for modals */
        .modal {
            z-index: 1060 !important;
        }

        /* Fix for the profile dropdown */
        .navbar-actions .dropdown {
            position: relative;
        }

        .navbar-actions .dropdown-menu {
            position: absolute;
            right: 0;
            top: 100%;
            margin-top: 0.5rem;
            width: 200px;
            box-shadow: var(--shadow-lg);
            z-index: 1090 !important;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Include sidebar -->
        <?php include_once __DIR__ . '/../includes/sidebar.php'; ?>

        <div class="admin-content">
            <!-- Include navbar -->
            <?php include_once __DIR__ . '/../includes/navbar.php'; ?>

            <div class="content-wrapper">
                <div class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1 class="m-0">Audit Logs</h1>
                            </div>
                            <div class="col-sm-6">
                                <!-- Breadcrumb removed -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content">
                    <div class="container-fluid">
                        <!-- Filters -->
                        <div class="audit-log-filters">
                            <div class="row">
                                <div class="col-md-3 mb-3 mb-md-0">
                                    <label for="filterStaff" class="form-label">Staff Member</label>
                                    <select class="form-select" id="filterStaff">
                                        <option value="">All Staff</option>
                                        <!-- Will be populated dynamically -->
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3 mb-md-0">
                                    <label for="filterType" class="form-label">Action Type</label>
                                    <select class="form-select" id="filterType">
                                        <option value="">All Types</option>
                                        <option value="login">Login</option>
                                        <option value="report">Report</option>
                                        <option value="note">Note</option>
                                        <option value="punishment">Punishment</option>
                                        <option value="punishment_removal">Punishment Removal</option>
                                        <option value="appeal">Appeal</option>
                                        <option value="settings">Settings</option>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3 mb-md-0">
                                    <label for="filterDateFrom" class="form-label">Date From</label>
                                    <input type="date" class="form-control" id="filterDateFrom">
                                </div>
                                <div class="col-md-3">
                                    <label for="filterDateTo" class="form-label">Date To</label>
                                    <input type="date" class="form-control" id="filterDateTo">
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12 d-flex justify-content-end">
                                    <button class="btn btn-secondary me-2" id="resetFilters">
                                        <i class="fas fa-undo me-1"></i> Reset
                                    </button>
                                    <button class="btn btn-primary" id="applyFilters">
                                        <i class="fas fa-filter me-1"></i> Apply Filters
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Audit Log Cards -->
                        <div id="auditLogCards">
                            <!-- Loading state -->
                            <div class="loading-state">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-3 text-muted">Loading audit logs...</p>
                            </div>

                            <!-- Card container will be populated via JavaScript -->
                            <div class="audit-card-container" style="display: none;"></div>

                            <!-- Empty state (shown when no logs found) -->
                            <div class="empty-state" style="display: none;">
                                <i class="fas fa-history"></i>
                                <h5>No Audit Logs Found</h5>
                                <p>There are no audit logs matching your filter criteria. Try adjusting your filters or check back later.</p>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div class="audit-pagination" style="display: none;">
                            <nav aria-label="Audit log pagination">
                                <ul class="pagination"></ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="/js/admin-common.min.js"></script>
    <script src="/js/sidebar-fix.min.js"></script>
    <script>
        $(document).ready(function() {

            let auditTable;


            function formatTimestamp(timestamp) {
                const date = new Date(timestamp);
                const options = {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    timeZone: 'America/New_York',
                    hour12: true
                };
                return `<span class="audit-timestamp">${date.toLocaleString('en-US', options)} ET</span>`;
            }


            function formatStaffInfo(staff) {
                const initials = staff.split(' ').map(n => n[0]).join('').toUpperCase();
                const avatarHtml = `<div class="staff-avatar">${initials}</div>`;

                return `
                    <div class="staff-info">
                        ${avatarHtml}
                        <span class="staff-name">${staff}</span>
                    </div>
                `;
            }


            function formatTypeBadge(type) {
                const typeClass = `audit-type-${type.toLowerCase()}`;


                let displayText = type;
                let icon = '';

                if (type.toLowerCase() === 'punishment_removal') {
                    displayText = 'Punishment Removal';
                    icon = '<i class="fas fa-check-circle me-1"></i>';
                } else if (type.toLowerCase() === 'punishment') {
                    icon = '<i class="fas fa-gavel me-1"></i>';
                } else if (type.toLowerCase() === 'appeal') {
                    icon = '<i class="fas fa-balance-scale me-1"></i>';
                } else if (type.toLowerCase() === 'ban') {
                    icon = '<i class="fas fa-ban me-1"></i>';
                } else if (type.toLowerCase() === 'mute') {
                    icon = '<i class="fas fa-microphone-slash me-1"></i>';
                }

                return `<span class="audit-type-badge ${typeClass}">${icon}${displayText}</span>`;
            }


            function formatDetails(details, type) {
                if (!details) return '';


                if (type && type.toLowerCase() === 'punishment_removal') {

                    const punishmentIdMatch = details.match(/Punishment\s+([A-Za-z0-9-]+)\s+was\s+removed/i);
                    const punishmentId = punishmentIdMatch ? punishmentIdMatch[1] : '';


                    let formattedDetails = details;


                    if (punishmentIdMatch) {
                        formattedDetails = details.replace(
                            /Punishment\s+([A-Za-z0-9-]+)\s+was\s+removed/i,
                            'Punishment <code>$1</code> was <span class="badge bg-success">removed</span>'
                        );
                    } else if (details.match(/ban\s+was\s+removed/i)) {
                        formattedDetails = details.replace(
                            /(ban\s+was\s+removed)/i,
                            '<span class="badge bg-success">$1</span>'
                        );
                    } else if (details.match(/mute\s+was\s+removed/i)) {
                        formattedDetails = details.replace(
                            /(mute\s+was\s+removed)/i,
                            '<span class="badge bg-success">$1</span>'
                        );
                    }


                    if (details.match(/for\s+player\s+([A-Za-z0-9_]+)/i)) {
                        formattedDetails = formattedDetails.replace(
                            /for\s+player\s+([A-Za-z0-9_]+)/i,
                            'for player <strong>$1</strong>'
                        );
                    }

                    return `
                        <div class="audit-details">
                            <div class="mb-2">${formattedDetails}</div>
                        </div>
                    `;
                }


                const formattedDetails = details
                    .replace(/\n/g, '<br>')
                    .replace(/Type:/g, '<strong>Type:</strong>')
                    .replace(/Reason:/g, '<strong>Reason:</strong>')
                    .replace(/Duration:/g, '<strong>Duration:</strong>')
                    .replace(/Offense:/g, '<strong>Offense:</strong>')
                    .replace(/Appeal ID:/g, '<strong>Appeal ID:</strong>');

                return `<div class="audit-details">${formattedDetails}</div>`;
            }


            function createAuditCard(log) {

                const formattedTime = new Date(log.timestamp).toLocaleString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    timeZone: 'America/New_York',
                    hour12: true
                });

                return `
                    <div class="audit-card">
                        <div class="card-header">
                            ${formatTypeBadge(log.type)}
                            <span class="audit-timestamp">
                                <i class="far fa-clock"></i>
                                ${formattedTime} ET
                            </span>
                        </div>
                        <div class="card-body">
                            ${formatStaffInfo(log.staff)}
                            <div class="audit-action">${log.action}</div>
                            ${log.target ? `<div class="audit-target"><strong>Player:</strong> ${log.target}</div>` : ''}
                            ${log.details ? formatDetails(log.details, log.type) : ''}
                        </div>
                    </div>
                `;
            }


            function loadAuditLogs(filters = {}) {


                $('.loading-state').show();
                $('.audit-card-container').hide().empty();
                $('.empty-state').hide();
                $('.audit-pagination').hide();


                let queryParams = new URLSearchParams();
                if (filters.staff) queryParams.append('staff', filters.staff);
                if (filters.type) queryParams.append('type', filters.type);
                if (filters.dateFrom) queryParams.append('date_from', filters.dateFrom);
                if (filters.dateTo) queryParams.append('date_to', filters.dateTo);


                const authData = window.AUTH_DATA || {};
                const csrfToken = $('meta[name="csrf-token"]').attr('content') || '';



                fetch('/adminapi/audit-logs.php?' + queryParams.toString(), {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'X-Discord-ID': authData.userId || authData.user_id || '',
                        'X-Discord-Username': authData.username || '',
                        'X-Discord-Role': authData.role || '',
                        'X-Discord-Avatar': authData.avatar || '',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        if (response.status === 401) {
                            throw new Error('Authentication required. Please log in again.');
                        } else if (response.status === 403) {
                            throw new Error('You do not have permission to access audit logs. Admin role required.');
                        } else {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                    }
                    return response.json();
                })
                .then(response => {

                    $('.loading-state').hide();

                    if (response.success && response.logs && response.logs.length > 0) {

                        let cardsHtml = '';
                        response.logs.forEach(function(log) {
                            cardsHtml += createAuditCard(log);
                        });


                        $('.audit-card-container').html(cardsHtml).show();


                        if (response.logs.length > 20) {

                            $('.audit-pagination').show();
                        }
                    } else {

                        $('.empty-state').show();
                    }
                })
                .catch(error => {

                    $('.loading-state').hide();


                    $('.empty-state').html(`
                        <i class="fas fa-exclamation-circle text-danger"></i>
                        <h5>Failed to Load Audit Logs</h5>
                        <p>${error.message}</p>
                        <button class="btn btn-primary mt-3" onclick="loadAuditLogs()">
                            <i class="fas fa-sync-alt me-2"></i> Try Again
                        </button>
                    `).show();
                });
            }


            function loadStaffMembers() {
                fetch('/adminapi/staff/list.php', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'X-Discord-ID': window.AUTH_DATA.userId || window.AUTH_DATA.user_id || '',
                        'X-Discord-Username': window.AUTH_DATA.username || '',
                        'X-Discord-Role': window.AUTH_DATA.role || '',
                        'X-Discord-Avatar': window.AUTH_DATA.avatar || '',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(response => {
                    if (response.success && response.staff) {
                        const staffSelect = $('#filterStaff');
                        staffSelect.find('option:not(:first)').remove();

                        response.staff.forEach(staff => {
                            staffSelect.append(`<option value="${staff.user_id}">${staff.username}</option>`);
                        });
                    }
                })
                .catch(error => {

                });
            }


            $('#applyFilters').click(function() {
                const filters = {
                    staff: $('#filterStaff').val(),
                    type: $('#filterType').val(),
                    dateFrom: $('#filterDateFrom').val(),
                    dateTo: $('#filterDateTo').val()
                };

                loadAuditLogs(filters);
            });


            $('#resetFilters').click(function() {
                $('#filterStaff').val('');
                $('#filterType').val('');
                $('#filterDateFrom').val('');
                $('#filterDateTo').val('');

                loadAuditLogs();
            });




            loadStaffMembers();
            loadAuditLogs();
        });
    </script>
</body>
</html>
