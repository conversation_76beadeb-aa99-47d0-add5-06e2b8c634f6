<?php
/**
 * Uptime Monitoring Dashboard
 * 
 * Displays real-time health status and uptime metrics for the staff portal
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/db_access.php';


if (!is_logged_in()) {
    header('Location: /admin/login.php');
    exit;
}

$page_title = 'System Uptime';
$page_description = 'Monitor system health and uptime metrics';

renderHeader($page_title, [
    '/admin/css/dashboard.min.css2',
    '/admin/css/uptime.css'
], $page_description);
?>

<div class="container-fluid">
    <div class="row">
        <?php renderSidebar('uptime'); ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-heartbeat me-2 text-success"></i>
                    System Uptime & Health
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshHealthStatus()">
                        <i class="fas fa-sync-alt me-1"></i>
                        Refresh
                    </button>
                </div>
            </div>

            <!-- Health Status Overview -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Current Health Status
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="health-status-container">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2 text-muted">Checking system health...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Health Checks Detail -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list-check me-2"></i>
                                Detailed Health Checks
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="health-checks-container">
                                <!-- Health checks will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sentry Integration Info -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                Monitoring Integration
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Sentry Uptime Monitoring</h6>
                                <p class="mb-2">This portal is monitored by Sentry for uptime and performance tracking.</p>
                                <ul class="mb-0">
                                    <li><strong>Health Check Endpoint:</strong> <code>/admin/api/health-check.php</code></li>
                                    <li><strong>Monitoring Interval:</strong> Every 5 minutes</li>
                                    <li><strong>Tracked Metrics:</strong> Response time, database connectivity, file system access</li>
                                    <li><strong>Alert Threshold:</strong> 3 consecutive failures</li>
                                </ul>
                            </div>
                            
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6>Quick Actions</h6>
                                    <a href="/admin/api/health-check.php" target="_blank" class="btn btn-sm btn-outline-primary me-2">
                                        <i class="fas fa-external-link-alt me-1"></i>
                                        View Raw Health Data
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <h6>External Monitoring</h6>
                                    <p class="small text-muted">
                                        Configure external monitoring tools to ping the health check endpoint for comprehensive uptime tracking.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
let healthCheckInterval;

function refreshHealthStatus() {
    const container = document.getElementById('health-status-container');
    const checksContainer = document.getElementById('health-checks-container');

    // Show loading state
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Checking system health...</p>
        </div>
    `;

    // Use internal access (server IP is whitelisted)
    fetch('/adminapi/health-check.php')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            displayHealthStatus(data);
            displayHealthChecks(data.checks);
        })
        .catch(error => {
            console.error('Health check failed:', error);
            container.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Health Check Failed</h6>
                    <p class="mb-0">Unable to retrieve system health status: ${error.message}</p>
                    <small class="text-muted">This may be due to security restrictions. Check the monitoring setup page for configuration details.</small>
                </div>
            `;
        });
}

function displayHealthStatus(data) {
    const container = document.getElementById('health-status-container');
    const statusClass = data.status === 'healthy' ? 'success' : data.status === 'degraded' ? 'warning' : 'danger';
    const statusIcon = data.status === 'healthy' ? 'check-circle' : data.status === 'degraded' ? 'exclamation-triangle' : 'times-circle';
    
    container.innerHTML = `
        <div class="row">
            <div class="col-md-3">
                <div class="text-center">
                    <i class="fas fa-${statusIcon} fa-3x text-${statusClass} mb-2"></i>
                    <h4 class="text-${statusClass}">${data.status.toUpperCase()}</h4>
                </div>
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-4">
                        <strong>Response Time:</strong><br>
                        <span class="h5">${data.response_time_ms}ms</span>
                    </div>
                    <div class="col-md-4">
                        <strong>Last Check:</strong><br>
                        <span class="h6">${new Date(data.timestamp).toLocaleString()}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>Service:</strong><br>
                        <span class="h6">${data.service}</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function displayHealthChecks(checks) {
    const container = document.getElementById('health-checks-container');
    let html = '<div class="row">';
    
    Object.entries(checks).forEach(([name, check]) => {
        const statusClass = check.status === 'healthy' ? 'success' : check.status === 'degraded' ? 'warning' : 'danger';
        const statusIcon = check.status === 'healthy' ? 'check' : check.status === 'degraded' ? 'exclamation-triangle' : 'times';
        
        html += `
            <div class="col-md-6 mb-3">
                <div class="card border-${statusClass}">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-${statusIcon} text-${statusClass} me-2"></i>
                            ${name.charAt(0).toUpperCase() + name.slice(1)}
                        </h6>
                        <p class="card-text">
                            <span class="badge bg-${statusClass}">${check.status}</span>
                            ${check.error ? `<br><small class="text-danger">${check.error}</small>` : ''}
                        </p>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}


document.addEventListener('DOMContentLoaded', function() {
    refreshHealthStatus();
    
    // Auto-refresh every 30 seconds
    healthCheckInterval = setInterval(refreshHealthStatus, 30000);
});


window.addEventListener('beforeunload', function() {
    if (healthCheckInterval) {
        clearInterval(healthCheckInterval);
    }
});
</script>

<?php renderFooter(); ?>
