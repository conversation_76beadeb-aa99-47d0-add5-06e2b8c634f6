<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/theme-handler.php'; 
require_once __DIR__ . '/../includes/form_helpers.php';


require_auth(ROLE_TRAINEE); // TRAINEE_ROLE or higher
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Player Information | MassacreMC Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
window.AUTH_DATA = {
    authenticated: <?php echo is_authenticated() ? 'true' : 'false'; ?>,
    userId: "<?php echo htmlspecialchars($_SESSION['discord_user_id'] ?? ''); ?>",
    username: "<?php echo htmlspecialchars($_SESSION['discord_username'] ?? ''); ?>",
    role: "<?php echo htmlspecialchars($_SESSION['discord_user_role'] ?? ''); ?>",
    hasRequiredRole: <?php echo has_role(ROLE_TRAINEE) ? 'true' : 'false'; ?>,
    sessionId: "<?php echo session_id(); ?>"
};
</script>
<meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <link href="/css/admin-common.css" rel="stylesheet">
    <link href="/css/modern-theme.css" rel="stylesheet">
    <link href="/css/mobile.css" rel="stylesheet">
    <link href="/css/playerinfo.css" rel="stylesheet">
    <link href="/css/playerinfo-modern.css" rel="stylesheet">
    <link href="/css/notes.css" rel="stylesheet">
    <link href="/css/punishment.css" rel="stylesheet">
    <link href="/css/punishment-dark-mode-fix.css" rel="stylesheet">
    <link href="/css/punishment-details-fix.css" rel="stylesheet">
    <link href="/css/punishment-details-white-fix.css" rel="stylesheet">
    <link href="/css/dropdown-fix.css" rel="stylesheet">
    <link href="/css/modal-title-fix.css" rel="stylesheet">
</head>
<body>
    <!-- Include standard navbar -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Include standard sidebar -->
    <?php include '../includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div id="content" class="main-content">
        <div class="page-container">
            <!-- Content Header -->
            <div class="content-header">
                <h1>Player & Faction Search</h1>
                <p class="text-muted">Search for players or factions to view detailed information and manage punishments</p>
            </div>

            <!-- Search Card -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="search-container">
                        <div class="search-input-container">
                            <div class="input-group">
                                <select class="form-select" id="searchType">
                                    <option value="player">Player</option>
                                    <option value="faction">Faction</option>
                                </select>
                                <div class="position-relative flex-grow-1">
                                    <div class="search-modern w-100">
                                        <i class="fas fa-search"></i>
                                        <input type="text" class="form-control" id="searchInput"
                                            placeholder="Search for players or factions..."
                                            autocomplete="off"
                                            title="Type a name and press Enter to search">
                                    </div>
                                    <div id="searchSuggestions" class="dropdown-menu w-100" style="display: none;"></div>
                                </div>
                                <button class="btn btn-primary" type="button" id="searchButton">
                                    <i class="fas fa-search me-1"></i> Search
                                </button>
                                <button class="btn btn-outline-primary ms-2" type="button" id="refreshButton"
                                        title="Refresh data (bypasses cache)" onclick="performSearch(true)">
                                    <i class="fas fa-sync-alt me-1"></i> Refresh
                                </button>
                            </div>
                            <div class="mt-2 text-muted small">
                                <i class="fas fa-info-circle me-1"></i> Tip: Enter the full username to search - exact matches only (e.g., "AlphICEter")
                            </div>
                        </div>
                    </div>

                    <div class="search-history mt-4" id="searchHistory">
                        <div class="search-history-title">Recent Searches</div>
                        <div class="history-tags" id="historyTags">
                            <!-- Search history will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading Spinner -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>

            <!-- Search Results -->
            <div id="playerData">
                <!-- Search results will be displayed here -->
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h4>No Search Results</h4>
                    <p>Enter a player or faction name above to see detailed information.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Punishment Modal -->
    <div class="modal fade" id="punishmentModal" tabindex="-1" aria-labelledby="punishmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content punishment-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="punishmentModalLabel">Add Punishment</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="punishmentForm">
                        <input type="hidden" id="punishmentPlayerXuid" name="xuid">
                        <input type="hidden" id="punishmentPlayerName" name="username">
                        <input type="hidden" id="punishmentType" name="type" value="ban">
                        <input type="hidden" id="punishmentDuration" name="duration" value="permanent">
                        <input type="hidden" id="punishmentDurationUnit" name="duration_unit" value="permanent">
                        <input type="hidden" id="punishmentPublic" name="public" value="1">
                        <?php echo csrf_token_input(); ?>

                        <div class="mb-4">
                            <label for="punishmentTypeSelect" class="form-label">Violation Type</label>
                            <select class="form-select" id="punishmentTypeSelect">
                                <option value="">Select a violation type...</option>
                            </select>
                            <div class="form-text">Select a predefined violation type</div>
                        </div>

                        <!-- Offense progression information will be displayed here -->
                        <div id="offenseProgressionInfo" class="mb-4"></div>

                        <div class="mb-4">
                            <label for="punishmentEvidence" class="form-label">Evidence</label>
                            <textarea class="form-control" id="punishmentEvidence" name="evidence" rows="3" placeholder="Provide evidence of the violation (screenshots, videos, etc.)"></textarea>
                            <div class="form-text">Include links to screenshots, videos, report ID, or other evidence</div>
                        </div>

                        <div class="mb-4">
                            <label for="punishmentNotes" class="form-label">Staff Notes</label>
                            <textarea class="form-control" id="punishmentNotes" name="notes" rows="3" placeholder="Add any additional notes for staff reference (not visible to the player)"></textarea>
                            <div class="form-text">These notes will only be visible to staff members</div>
                        </div>

                        <div class="confirmation-section">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="punishmentConfirm" name="confirm" required>
                                <label class="form-check-label" for="punishmentConfirm">
                                    I confirm this punishment is appropriate and follows server guidelines
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="submitPunishment">Apply Punishment</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Punishment Details Modal -->
    <div class="modal fade" id="punishmentDetailsModal" tabindex="-1" aria-labelledby="punishmentDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="punishmentDetailsModalLabel">Punishment Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="punishmentDetailsContent">
                    <!-- Content will be loaded dynamically -->
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading punishment details...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notes Modal -->
    <div class="modal fade" id="noteModal" tabindex="-1" aria-labelledby="noteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header bg-gradient-primary text-white">
                    <h5 class="modal-title" id="noteModalLabel">Add Staff Note</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="noteForm">
                        <input type="hidden" id="notePlayerXuid" name="xuid">
                        <input type="hidden" id="notePlayerName" name="username">
                        <?php echo csrf_token_input(); ?>

                        <div class="mb-4">
                            <label for="noteType" class="form-label">Note Type</label>
                            <div class="row g-3 mb-2">
                                <div class="col-md-4">
                                    <div class="form-check custom-radio">
                                        <input class="form-check-input" type="radio" name="noteTypeRadio" id="noteTypeBehavior" value="behavior">
                                        <label class="form-check-label" for="noteTypeBehavior">
                                            <i class="fas fa-user me-2 text-primary"></i> Player Behavior
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check custom-radio">
                                        <input class="form-check-input" type="radio" name="noteTypeRadio" id="noteTypeSupport" value="support">
                                        <label class="form-check-label" for="noteTypeSupport">
                                            <i class="fas fa-headset me-2 text-success"></i> Support Interaction
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check custom-radio">
                                        <input class="form-check-input" type="radio" name="noteTypeRadio" id="noteTypePayment" value="payment">
                                        <label class="form-check-label" for="noteTypePayment">
                                            <i class="fas fa-credit-card me-2 text-warning"></i> Payment Issue
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check custom-radio">
                                        <input class="form-check-input" type="radio" name="noteTypeRadio" id="noteTypeBug" value="bug">
                                        <label class="form-check-label" for="noteTypeBug">
                                            <i class="fas fa-bug me-2 text-danger"></i> Bug Report
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check custom-radio">
                                        <input class="form-check-input" type="radio" name="noteTypeRadio" id="noteTypeOther" value="other">
                                        <label class="form-check-label" for="noteTypeOther">
                                            <i class="fas fa-sticky-note me-2 text-info"></i> Other
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <select class="form-select d-none" id="noteType" name="type" required>
                                <option value="">Select a note type</option>
                                <option value="behavior">Player Behavior</option>
                                <option value="support">Support Interaction</option>
                                <option value="payment">Payment Issue</option>
                                <option value="bug">Bug Report</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div class="mb-4">
                            <label for="noteContent" class="form-label">Note Content</label>
                            <textarea class="form-control" id="noteContent" name="content" rows="5" required></textarea>
                            <div class="form-text">This note will be visible to all staff members.</div>
                        </div>

                        <div class="confirmation-section">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="noteImportant" name="important">
                                <label class="form-check-label" for="noteImportant">
                                    <i class="fas fa-exclamation-circle text-danger me-1"></i> Mark as important
                                </label>
                            </div>

                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="noteConfirm" name="confirm" required>
                                <label class="form-check-label" for="noteConfirm">
                                    I confirm this note contains accurate information
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="submitNote">
                        <i class="fas fa-save me-1"></i> Save Note
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Rank Management Modal -->
    <div class="modal fade" id="rankModal" tabindex="-1" aria-labelledby="rankModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header bg-gradient-warning text-dark">
                    <h5 class="modal-title" id="rankModalLabel">
                        <i class="fas fa-crown me-2"></i>Change Player Rank
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="rankForm">
                        <input type="hidden" id="rankPlayerXuid" name="xuid">
                        <input type="hidden" id="rankPlayerName" name="username">
                        <?php echo csrf_token_input(); ?>

                        <div class="alert alert-warning" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> Changing a player's rank will immediately affect their permissions and access on the server.
                        </div>

                        <div class="mb-4">
                            <label class="form-label">Current Player</label>
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user me-2 text-primary"></i>
                                        <span id="rankPlayerDisplayName" class="fw-medium"></span>
                                        <span class="badge bg-primary ms-2" id="rankCurrentRank"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="rankSelect" class="form-label">New Rank</label>
                            <select class="form-select" id="rankSelect" name="new_rank" required>
                                <option value="">Select a rank...</option>
                                <option value="player">Player</option>
                                <option value="vip">VIP</option>
                                <option value="mvp">MVP</option>
                                <option value="mmp">MMP</option>
                                <option value="mgp">MGP</option>
                                <option value="mlp">MLP</option>
                                <option value="cc">CC</option>
                                <option value="builder">Builder</option>
                                <option value="trainee">Trainee</option>
                                <option value="support">Support</option>
                                <option value="moderator">Moderator</option>
                                <option value="admin">Admin</option>
                                <option value="owner">Owner</option>
                                <option value="yt">YT</option>
                                <option value="youtuber">YouTuber</option>
                                <option value="secretary">Secretary</option>
                            </select>
                            <div class="form-text">Select the new rank for this player</div>
                        </div>

                        <div class="mb-4">
                            <label for="rankReason" class="form-label">Reason for Rank Change</label>
                            <textarea class="form-control" id="rankReason" name="reason" rows="3" required
                                placeholder="Provide a reason for this rank change (e.g., promotion, demotion, role change)"></textarea>
                            <div class="form-text">This reason will be logged for audit purposes</div>
                        </div>

                        <div class="confirmation-section">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="rankConfirm" name="confirm" required>
                                <label class="form-check-label" for="rankConfirm">
                                    I confirm this rank change is appropriate and authorized
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-warning" id="submitRankChange" onclick="submitRankChange()">
                        <i class="fas fa-crown me-1"></i> Change Rank
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="/js/admin-common.min.js"></script>
    <script src="/js/punishment-types.min.js"></script>
    <script src="/js/playerinfo.min.js"></script>

    <!-- Initialize Punishment UI -->
    <script>
        $(document).ready(function() {

            $('#punishmentModal').on('shown.bs.modal', function() {

                $('.dropdown-menu').css('position', 'absolute');
            });


            $('input[name="noteTypeRadio"]').change(function() {
                $('#noteType').val($(this).val());
            });

            $('#punishmentModal').on('hidden.bs.modal', function() {
                document.getElementById('punishmentForm').reset();
                document.getElementById('offenseProgressionInfo').innerHTML = '';
                document.getElementById('punishmentTypeSelect').value = '';
            });

            // Rank modal initialization
            $('#rankModal').on('shown.bs.modal', function() {
                // Set player display name when modal opens
                const playerName = document.getElementById('rankPlayerName').value;
                document.getElementById('rankPlayerDisplayName').textContent = playerName;
            });

            $('#rankModal').on('hidden.bs.modal', function() {
                document.getElementById('rankForm').reset();
                document.getElementById('rankPlayerDisplayName').textContent = '';
                document.getElementById('rankCurrentRank').textContent = '';
            });
        });
    </script>
</body>
</html>