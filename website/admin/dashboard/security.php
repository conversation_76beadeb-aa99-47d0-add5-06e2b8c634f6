<?php
require_once '../includes/auth.php';
require_once '../includes/config.php';

// Require authentication and developer role
require_auth(ROLE_DEVELOPER);

$pageTitle = 'Security Dashboard';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - MassacreMC Admin</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../css/admin-common.css" rel="stylesheet">
    <link href="../css/dashboard.css" rel="stylesheet">
    
    <style>
        .security-metric {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .security-metric:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .metric-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: var(--text-muted);
            font-size: 0.9rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-offline { background-color: #dc3545; }
        
        .threat-log {
            background: var(--code-bg, #f8f9fa);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .security-alert {
            border-left: 4px solid #dc3545;
            background: rgba(220, 53, 69, 0.1);
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .dark-mode .security-metric {
            background: #2d3748;
            border-color: #4a5568;
        }
        
        .dark-mode .threat-log {
            background: #1a202c;
            border-color: #4a5568;
            color: #e2e8f0;
        }
    </style>
</head>
<body>
    <?php include '../includes/navbar.php'; ?>
    
    <div class="admin-container">
        <?php include '../includes/sidebar.php'; ?>
        
        <div class="main-content">
            <div class="content-header">
                <h1><i class="fas fa-shield-alt"></i> Security Dashboard</h1>
                <p class="text-muted">Real-time security monitoring and threat detection</p>
            </div>
            
            <div class="content-body">
                <!-- Auto-refresh notice -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    This dashboard auto-refreshes every 30 seconds. Last updated: <span id="lastUpdate"><?= date('Y-m-d H:i:s') ?></span>
                </div>
                
                <!-- Security Metrics Grid -->
                <div class="row" id="securityMetrics">
                    <div class="col-md-4">
                        <div class="security-metric text-center">
                            <div class="metric-icon text-danger">
                                <i class="fas fa-ban"></i>
                            </div>
                            <div class="metric-value text-danger" id="blockedIPs">-</div>
                            <div class="metric-label">Blocked IPs</div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="security-metric text-center">
                            <div class="metric-icon text-info">
                                <i class="fas fa-network-wired"></i>
                            </div>
                            <div class="metric-value text-info" id="activeConnections">-</div>
                            <div class="metric-label">Active Connections</div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="security-metric text-center">
                            <div class="metric-icon text-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="metric-value text-warning" id="threatCount">-</div>
                            <div class="metric-label">Threats Today</div>
                        </div>
                    </div>
                </div>
                
                <!-- System Status -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-server"></i> System Status</h5>
                            </div>
                            <div class="card-body" id="systemStatus">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Web Server</span>
                                    <span><span class="status-indicator status-online"></span>Online</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Database</span>
                                    <span><span class="status-indicator status-online"></span>Online</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Game Server</span>
                                    <span><span class="status-indicator status-online"></span>Online</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Security Monitor</span>
                                    <span id="securityMonitorStatus"><span class="status-indicator status-warning"></span>Checking...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line"></i> System Metrics</h5>
                            </div>
                            <div class="card-body" id="systemMetrics">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>CPU Load</span>
                                    <span id="cpuLoad">-</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Memory Usage</span>
                                    <span id="memoryUsage">-</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Disk Usage</span>
                                    <span id="diskUsage">-</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Uptime</span>
                                    <span id="uptime">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Security Events -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-exclamation-triangle"></i> Recent Security Events</h5>
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshSecurityData()">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="threat-log" id="threatLog">
                                    Loading security events...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-tools"></i> Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-primary w-100 mb-2" onclick="runSecurityScan()">
                                            <i class="fas fa-search"></i> Run Security Scan
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-warning w-100 mb-2" onclick="viewFirewallRules()">
                                            <i class="fas fa-shield-alt"></i> Firewall Rules
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-info w-100 mb-2" onclick="viewBlockedIPs()">
                                            <i class="fas fa-ban"></i> Blocked IPs
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-success w-100 mb-2" onclick="downloadReport()">
                                            <i class="fas fa-download"></i> Download Report
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Refresh Button -->
    <button class="btn btn-primary refresh-btn" onclick="refreshSecurityData()" title="Refresh Data">
        <i class="fas fa-sync-alt"></i>
    </button>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../js/admin-common.js"></script>
    
    <script>
        // Auto-refresh every 30 seconds
        let refreshInterval;
        
        function startAutoRefresh() {
            refreshInterval = setInterval(refreshSecurityData, 30000);
        }
        
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }
        
        function refreshSecurityData() {
            fetch('/adminapi/security/dashboard.php')
                .then(response => response.json())
                .then(data => {
                    updateSecurityMetrics(data);
                    updateLastUpdateTime();
                })
                .catch(error => {
                    console.error('Error fetching security data:', error);
                    showError('Failed to fetch security data');
                });
        }
        
        function updateSecurityMetrics(data) {
            // Update metrics
            document.getElementById('blockedIPs').textContent = data.blocked_ips || '0';
            document.getElementById('activeConnections').textContent = data.active_connections || '0';
            document.getElementById('threatCount').textContent = data.threats_today || '0';
            
            // Update system metrics
            document.getElementById('cpuLoad').textContent = data.cpu_load || 'N/A';
            document.getElementById('memoryUsage').textContent = data.memory_usage || 'N/A';
            document.getElementById('diskUsage').textContent = data.disk_usage || 'N/A';
            document.getElementById('uptime').textContent = data.uptime || 'N/A';
            
            // Update security monitor status
            const monitorStatus = document.getElementById('securityMonitorStatus');
            if (data.security_monitor_active) {
                monitorStatus.innerHTML = '<span class="status-indicator status-online"></span>Active';
            } else {
                monitorStatus.innerHTML = '<span class="status-indicator status-offline"></span>Inactive';
            }
            
            // Update threat log
            const threatLog = document.getElementById('threatLog');
            if (data.recent_threats && data.recent_threats.length > 0) {
                threatLog.textContent = data.recent_threats.join('\n');
            } else {
                threatLog.textContent = 'No recent security events';
            }
        }
        
        function updateLastUpdateTime() {
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
        }
        
        function showError(message) {
            // You can implement a toast notification here
            console.error(message);
        }
        
        // Quick action functions
        function runSecurityScan() {
            if (confirm('Run a comprehensive security scan? This may take a few minutes.')) {
                fetch('/adminapi/security/scan.php', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message || 'Security scan initiated');
                    })
                    .catch(error => {
                        alert('Failed to start security scan');
                    });
            }
        }
        
        function viewFirewallRules() {
            window.open('/adminapi/security/firewall.php', '_blank');
        }
        
        function viewBlockedIPs() {
            window.open('/adminapi/security/blocked-ips.php', '_blank');
        }
        
        function downloadReport() {
            window.open('/adminapi/security/report.php?download=1', '_blank');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            refreshSecurityData();
            startAutoRefresh();
        });
        
        // Stop auto-refresh when page is hidden
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
            }
        });
    </script>
</body>
</html>
