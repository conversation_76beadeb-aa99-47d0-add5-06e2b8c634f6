<?php

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/theme-handler.php'; 

require_auth(ROLE_TRAINEE); // TRAINEE_ROLE or higher
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <title>Reports Dashboard | MassacreMC Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
window.AUTH_DATA = {
    authenticated: <?php echo is_authenticated() ? 'true' : 'false'; ?>,
    userId: "<?php echo htmlspecialchars($_SESSION['discord_user_id'] ?? ''); ?>",
    username: "<?php echo htmlspecialchars($_SESSION['discord_username'] ?? ''); ?>",
    role: "<?php echo htmlspecialchars($_SESSION['discord_user_role'] ?? ''); ?>",
    hasRequiredRole: <?php echo has_role(ROLE_TRAINEE) ? 'true' : 'false'; ?>,
    sessionId: "<?php echo session_id(); ?>"
};
</script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php include '../includes/auth-data.php'; ?>
    <link href="../css/admin-common.css" rel="stylesheet">
    <link href="../css/modern-theme.css" rel="stylesheet">
    <link href="../css/mobile.css" rel="stylesheet">
    <link href="../css/dashboard.css" rel="stylesheet">
    <style>
        .report-card {
            transition: all 0.2s ease;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        }

        .status-badge {
            padding: 0.35rem 0.65rem;
            border-radius: 50rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background-color: rgba(243, 156, 18, 0.15);
            color: #f39c12;
        }

        .status-accepted {
            background-color: rgba(46, 204, 113, 0.15);
            color: #2ecc71;
        }

        .status-denied {
            background-color: rgba(231, 76, 60, 0.15);
            color: #e74c3c;
        }

        .priority-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .priority-1 { background-color: rgba(231, 76, 60, 0.15); color: #e74c3c; }
        .priority-2 { background-color: rgba(243, 156, 18, 0.15); color: #f39c12; }
        .priority-3 { background-color: rgba(52, 152, 219, 0.15); color: #3498db; }
        .priority-4 { background-color: rgba(149, 165, 166, 0.15); color: #95a5a6; }

        .report-type-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 600;
            background-color: rgba(155, 89, 182, 0.15);
            color: #9b59b6;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--text-muted);
            font-size: 0.875rem;
            padding: 1rem 0.75rem;
        }

        .table td {
            padding: 1rem 0.75rem;
            vertical-align: middle;
        }

        .avatar-sm {
            width: 32px;
            height: 32px;
            font-size: 0.875rem;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.8125rem;
        }
        /* Linked Account Information Styles */
        .linked-account-info {
            background-color: rgba(25, 135, 84, 0.1);
            border: 1px solid rgba(25, 135, 84, 0.2);
            border-radius: 8px;
            padding: 12px;
        }

        .linked-account-header {
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .linked-account-item {
            margin-bottom: 8px;
        }

        .linked-account-label {
            font-size: 0.75rem;
            color: #6c757d;
            margin-bottom: 2px;
        }

        .linked-account-value {
            font-weight: 500;
            font-size: 0.85rem;
        }

        .monospace {
            font-family: 'Courier New', monospace;
        }

        /* Evidence container styling */
        .evidence-container {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            max-height: 300px;
            overflow-y: auto;
        }

        .evidence-text {
            white-space: pre-wrap;
            word-break: break-word;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* Dark mode adjustments */
        body.dark-mode .linked-account-info {
            background-color: rgba(25, 135, 84, 0.15);
            border-color: rgba(25, 135, 84, 0.3);
        }

        body.dark-mode .linked-account-label {
            color: #adb5bd;
        }

        body.dark-mode .evidence-container {
            background-color: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
        }

        /* Dark dropdown styling */
        .form-select {
            background-color: #2d3748 !important;
            border-color: #4a5568 !important;
            color: #e2e8f0 !important;
        }

        .form-select:focus {
            background-color: #2d3748 !important;
            border-color: #007bff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
            color: #e2e8f0 !important;
        }

        .form-select option {
            background-color: #2d3748 !important;
            color: #e2e8f0 !important;
        }
    </style>
</head>
<body>
    <!-- Include standard navbar -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Include standard sidebar -->
    <?php include '../includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div id="content" class="main-content">
        <div class="page-container">
            <!-- Content Header -->
            <div class="content-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1>Reports Dashboard</h1>
                        <p class="text-muted">Manage and review player and faction reports</p>
                    </div>
                    <div class="d-flex gap-2">
                        <span class="badge bg-info"><span id="totalCount">0</span> Total</span>
                        <span class="badge bg-warning text-dark"><span id="pendingCount">0</span> Pending</span>
                    </div>
                </div>
            </div>



            <!-- Reports List -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">All Reports</h5>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" id="statusFilter" style="width: auto;">
                            <option value="all">All Status</option>
                            <option value="Pending">Pending</option>
                            <option value="Accepted">Accepted</option>
                            <option value="Denied">Denied</option>
                        </select>
                        <select class="form-select form-select-sm" id="ruleFilter" style="width: auto;">
                            <option value="all">All Rules</option>
                            <!-- Will be populated dynamically -->
                        </select>
                        <select class="form-select form-select-sm" id="priorityFilter" style="width: auto;">
                            <option value="all">All Priorities</option>
                            <option value="1">High</option>
                            <option value="2">Urgent</option>
                            <option value="3">Medium</option>
                            <option value="4">Low</option>
                        </select>
                        <input type="text" id="searchFilter" class="form-control form-control-sm" placeholder="Search..." style="width: 200px;">
                        <button id="resetFilters" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-undo me-1"></i>Reset
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="reportsTable">
                            <thead>
                                <tr>
                                    <th>Reporter</th>
                                    <th>Offender</th>
                                    <th>Type</th>
                                    <th>Rule</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Submitted</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="reportsContainer">
                                <!-- Reports will be loaded here -->
                                <tr>
                                    <td colspan="8" class="text-center py-5">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 text-muted">Loading reports...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Pagination Controls -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div class="text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    Showing <span id="showingStart">0</span> to <span id="showingEnd">0</span> of <span id="showingTotal">0</span> reports
                </div>
                <div class="btn-group" id="paginationControls">
                    <button class="btn btn-outline-primary" id="prevPage" disabled>
                        <i class="fas fa-chevron-left me-1"></i>Previous
                    </button>
                    <button class="btn btn-outline-primary" id="nextPage" disabled>
                        Next<i class="fas fa-chevron-right ms-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Modal -->
    <div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content" style="border-radius: 20px; overflow: hidden; border: none;">
                <div class="modal-header text-white" style="background: linear-gradient(135deg, #ef4444, #dc2626); border: none;">
                    <h5 class="modal-title fw-bold" id="reportModalLabel">
                        <i class="fas fa-flag me-2"></i>Report Details
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="reportModalContent" style="padding: 2rem;">
                    <!-- Report content will be loaded here -->
                    <div class="text-center py-4">
                        <div class="spinner-border text-danger" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading report details...</p>
                    </div>
                </div>
                <div class="modal-footer" id="reportModalFooter" style="border: none; padding: 1.5rem 2rem;">
                    <!-- Action buttons will be added here -->
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Admin Scripts -->
    <script src="../js/admin-common.min.js"></script>
    <script src="../js/reports.min.js"></script>

</body>
</html>