<?php
/**
 * Delete Faction API Endpoint
 * Handles faction deletion for staff members
 * Requires MODERATOR role or higher
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-TOKEN, X-Discord-ID, X-Discord-Username, X-Discord-Role, X-Discord-Avatar, X-Session-ID, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/db_access.php';
require_once __DIR__ . '/../includes/auth.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

apply_rate_limit($_SERVER['REMOTE_ADDR'], 10, 60); // 10 requests per minute

try {
    // Verify authentication
    if (!is_authenticated()) {
        $headers = getallheaders();
        if (isset($headers['X-Discord-ID']) && !empty($headers['X-Discord-ID'])) {
            $_SESSION['discord_user_id'] = $headers['X-Discord-ID'];
            $_SESSION['discord_username'] = $headers['X-Discord-Username'] ?? 'Unknown User';
            $_SESSION['discord_user_role'] = $headers['X-Discord-Role'] ?? ROLE_TRAINEE;
            $_SESSION['auth_time'] = time();
        } else {
            http_response_code(401);
            echo json_encode(['error' => 'Not authenticated']);
            exit;
        }
    }

    // Check for DEVELOPER role or higher for faction deletion
    if (!has_role(ROLE_DEVELOPER)) {
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied. Faction deletion requires DEVELOPER role or higher.']);
        exit;
    }

    // Update staff activity
    $headers = getallheaders();
    update_staff_activity(
        $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
        $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
        $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
        $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null
    );

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Get input data
        $input = json_decode(file_get_contents('php://input'), true);

        // Verify CSRF token - check multiple sources
        $token = $input['csrf_token'] ??
                 $_POST['csrf_token'] ??
                 $_SERVER['HTTP_X_CSRF_TOKEN'] ??
                 getallheaders()['X-CSRF-TOKEN'] ??
                 null;

        if (!$token || !verify_csrf_token($token)) {
            secure_log("CSRF token validation failed. Token: " . ($token ? "present" : "missing"));
            http_response_code(403);
            echo json_encode(['error' => 'CSRF token invalid or missing']);
            exit;
        }

        // Get faction name from request (input already parsed above)
        $factionName = $input['factionName'] ?? null;
        $reason = $input['reason'] ?? 'No reason provided';

        if (empty($factionName)) {
            http_response_code(400);
            echo json_encode(['error' => 'Faction name is required']);
            exit;
        }

        // Sanitize inputs
        $factionName = sanitize_input($factionName);
        $reason = sanitize_input($reason);

        // Get staff information for logging
        $staffId = $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? 'unknown';
        $staffName = $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? 'unknown';

        // Delete the faction
        $result = delete_faction_admin($factionName, $staffId, $staffName, $reason);

        if ($result['success']) {
            // Clear any cached search results that might contain this faction
            if (function_exists('apcu_clear_cache')) {
                apcu_clear_cache();
            }

            // Clear session cache as well
            if (isset($_SESSION['query_cache'])) {
                $_SESSION['query_cache'] = [];
            }

            // Clear specific faction cache entries
            if (function_exists('apcu_delete')) {
                $cacheKeys = [
                    'admin_search_' . md5($factionName . '_faction'),
                    'faction_data_' . md5($factionName),
                    'search_' . md5($factionName),
                    'api_faction_' . md5($factionName)
                ];

                foreach ($cacheKeys as $key) {
                    apcu_delete($key);
                }
            }

            // Force a fresh search by adding a cache-busting parameter
            $cacheBuster = time();

            secure_log("Faction {$factionName} deleted successfully by {$staffName}. All caches cleared with cache buster: {$cacheBuster}");

            echo json_encode([
                'success' => true,
                'message' => 'Faction deleted successfully',
                'details' => [
                    'faction_name' => $factionName,
                    'members_affected' => $result['members_affected'] ?? 0,
                    'allies_updated' => $result['allies_updated'] ?? 0,
                    'cache_cleared' => true,
                    'cache_buster' => $cacheBuster
                ]
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'error' => $result['error'] ?? 'Failed to delete faction',
                'details' => $result['details'] ?? null
            ]);
        }
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }

} catch (Exception $e) {
    secure_log("Delete Faction API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
