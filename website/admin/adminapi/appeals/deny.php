<?php
/**
 * Appeal Deny API Endpoint
 * Handles denying an appeal
 */


header('Content-Type: application/json');


require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/security.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/auth.php';


if (getenv('APP_ENV') !== 'production') {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
    secure_log("Appeal Deny API: Endpoint called");
}


if (session_status() === PHP_SESSION_NONE) {
    session_start();
    if (getenv('APP_ENV') !== 'production') {
        secure_log("Appeal Deny API: Started new session, ID: " . session_id());
    }
} else {
    if (getenv('APP_ENV') !== 'production') {
        secure_log("Appeal Deny API: Using existing session, ID: " . session_id());
    }
}


apply_rate_limit($_SERVER['REMOTE_ADDR'], 60, 60); // 60 requests per minute

try {

    // Enforce proper authentication - NO BYPASSES ALLOWED
    if (!is_authenticated()) {
        secure_log("Appeal Deny API: Authentication failed - User not authenticated", "warning", [
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'endpoint' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ]);
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        exit;
    }


    if (!has_role(ROLE_ADMIN)) {
        secure_log("Appeal Deny API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied. You do not have the required role to deny appeals.']);
        exit;
    }


    $headers = getallheaders();
    update_staff_activity(
        $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
        $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
        $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
        $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null
    );


    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        exit;
    }


    $appeal_id = null;


    if (isset($_GET['id']) && !empty($_GET['id'])) {
        $appeal_id = sanitize_input($_GET['id']);
        secure_log("Appeal Deny API: Got appeal_id=$appeal_id from query string");
    } else {

        $uri_parts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
        secure_log("Appeal Deny API: URI parts: " . json_encode($uri_parts));



        if (count($uri_parts) >= 4) {
            $appeals_pos = array_search('appeals', $uri_parts);
            if ($appeals_pos !== false && isset($uri_parts[$appeals_pos + 1]) &&
                (isset($uri_parts[$appeals_pos + 2]) && $uri_parts[$appeals_pos + 2] === 'deny')) {
                $appeal_id = sanitize_input($uri_parts[$appeals_pos + 1]);
                secure_log("Appeal Deny API: Extracted appeal_id=$appeal_id from URL path (pattern 1)");
            }
        }


        if (empty($appeal_id) && count($uri_parts) >= 3) {
            $appeals_pos = array_search('appeals.php', $uri_parts);
            if ($appeals_pos !== false && isset($uri_parts[$appeals_pos + 1])) {
                $appeal_id = sanitize_input($uri_parts[$appeals_pos + 1]);
                secure_log("Appeal Deny API: Extracted appeal_id=$appeal_id from URL path (pattern 2)");
            }
        }


        if (empty($appeal_id) && count($uri_parts) >= 2) {
            $appeal_id = sanitize_input($uri_parts[count($uri_parts) - 2]);
            secure_log("Appeal Deny API: Extracted appeal_id=$appeal_id from URL path (fallback method)");
        }
    }


    if (empty($appeal_id)) {
        secure_log("Appeal Deny API: No appeal ID found in request");
        http_response_code(400);
        echo json_encode(['error' => 'Missing appeal ID']);
        exit;
    }


    $input = json_decode(file_get_contents('php://input'), true);
    $staff_notes = '';

    if (!$input && isset($_POST['staff_notes'])) {

        $staff_notes = sanitize_input($_POST['staff_notes']);
    } elseif ($input && isset($input['staff_notes'])) {

        $staff_notes = sanitize_input($input['staff_notes']);
    }


    $staff_id = $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? 'unknown';
    $staff_name = $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? 'unknown';

    secure_log("Appeal Deny API: Processing deny action for appeal_id=$appeal_id by staff=$staff_name");

    $result = handle_appeal_action($appeal_id, 'deny', $staff_id, $staff_name, $staff_notes);
    echo json_encode($result);

} catch (Exception $e) {

    secure_log("Appeal Deny API Error: " . $e->getMessage());
    secure_log("Appeal Deny API Error Stack Trace: " . $e->getTraceAsString());


    http_response_code(500);
    echo json_encode(['error' => 'An internal server error occurred']);
}
?>
