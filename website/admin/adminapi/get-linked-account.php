<?php
/**
 * API endpoint to get linked account info by Discord ID
 * Admin only functionality
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/db_access.php';


set_security_headers();


header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


require_auth(ROLE_MODERATOR);


$user_id = $_SESSION['discord_user_id'] ?? 'unknown';
if (!check_rate_limit("linked_account_lookup:{$user_id}", 30, 60)) { // 30 requests per minute
    http_response_code(429);
    echo json_encode(['success' => false, 'message' => 'Rate limit exceeded. Please try again later.']);
    exit;
}


if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $discord_id = $_GET['discord_id'] ?? '';

    if (empty($discord_id)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Discord ID is required']);
        exit;
    }

    // Sanitize and validate Discord ID (should be numeric)
    $discord_id = trim($discord_id);

    // Discord IDs are typically 17-19 digits long, validate length to prevent abuse
    if (strlen($discord_id) > 20 || strlen($discord_id) < 17) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid Discord ID length']);
        exit;
    }

    if (!preg_match('/^\d+$/', $discord_id)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid Discord ID format']);
        exit;
    }
    
    // Use the main website's DatabaseAccess class for consistency
    require_once __DIR__ . '/../../includes/db_access.php';
    $dbAccess = new DatabaseAccess();

    if (!$dbAccess || !$dbAccess->db) {
        throw new Exception("Database connection failed");
    }

    // Search for linked account by Discord ID
    $linkedAccount = $dbAccess->db->linked_accounts->findOne([
        'discord_id' => $discord_id,
        'platform' => 'xbox',
        'status' => 'verified'
    ]);

    if ($linkedAccount) {
        // Get Discord user data from player_accounts
        $playerData = $dbAccess->db->player_accounts->findOne([
            'discord_id' => $discord_id
        ]);

        // Log this sensitive operation for audit purposes
        $staff_id = $_SESSION['discord_user_id'] ?? 'unknown';
        $staff_username = $_SESSION['discord_username'] ?? 'unknown';
        secure_log("Linked account lookup performed", "admin_action", [
            'staff_id' => $staff_id,
            'staff_username' => $staff_username,
            'target_discord_id' => $discord_id,
            'target_xbox_username' => $linkedAccount['username'],
            'action' => 'view_linked_account',
            'endpoint' => '/adminapi/get-linked-account.php'
        ]);

        echo json_encode([
            'success' => true,
            'data' => [
                'has_linked_account' => true,
                'discord_id' => $discord_id,
                'discord_username' => $playerData['discord_username'] ?? 'Unknown',
                'discord_email' => $playerData['discord_email'] ?? null,
                'xbox_username' => $linkedAccount['username'],
                'linked_at' => $linkedAccount['verified_at'] ? $linkedAccount['verified_at']->toDateTime()->format('Y-m-d H:i:s') : null
            ]
        ]);
    } else {
        // Log failed lookup attempts as well
        $staff_id = $_SESSION['discord_user_id'] ?? 'unknown';
        $staff_username = $_SESSION['discord_username'] ?? 'unknown';
        secure_log("Linked account lookup - no account found", "admin_action", [
            'staff_id' => $staff_id,
            'staff_username' => $staff_username,
            'target_discord_id' => $discord_id,
            'action' => 'view_linked_account_not_found',
            'endpoint' => '/adminapi/get-linked-account.php'
        ]);

        echo json_encode([
            'success' => true,
            'data' => [
                'has_linked_account' => false
            ]
        ]);
    }
    
} catch (Exception $e) {
    secure_log("Error in get-linked-account.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
