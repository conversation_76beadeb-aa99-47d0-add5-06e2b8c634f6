<?php
/**
 * Convert UUIDs to usernames API endpoint
 * Used for displaying faction member names instead of UUIDs
 */

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';

header('Content-Type: application/json');


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    // Check authentication
    if (!is_authenticated()) {
        http_response_code(401);
        echo json_encode(['success' => false, 'error' => 'Not authenticated']);
        exit;
    }

    // Check role permissions
    if (!has_role(ROLE_TRAINEE)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Insufficient permissions - TRAINEE role or higher required']);
        exit;
    }

    // Only accept POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        exit;
    }

    // Get UUIDs from request
    $uuids_json = $_POST['uuids'] ?? '';
    if (empty($uuids_json)) {
        echo json_encode(['success' => false, 'error' => 'No UUIDs provided']);
        exit;
    }

    $uuids = json_decode($uuids_json, true);
    if (!is_array($uuids) || empty($uuids)) {
        echo json_encode(['success' => false, 'error' => 'Invalid UUIDs format']);
        exit;
    }

    // Limit the number of UUIDs to prevent abuse
    if (count($uuids) > 100) {
        echo json_encode(['success' => false, 'error' => 'Too many UUIDs (max 100)']);
        exit;
    }

    // Validate UUIDs format
    $validUUIDs = [];
    foreach ($uuids as $uuid) {
        if (is_string($uuid) && preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $uuid)) {
            $validUUIDs[] = $uuid;
        }
    }

    if (empty($validUUIDs)) {
        echo json_encode(['success' => false, 'error' => 'No valid UUIDs provided']);
        exit;
    }

    // Convert UUIDs to usernames using database
    $db = new DatabaseAccess();
    $conversions = [];

    foreach ($validUUIDs as $uuid) {
        try {
            // Convert UUID string to binary format for database query
            $uuid_clean = str_replace('-', '', $uuid);
            $uuid_binary = hex2bin($uuid_clean);

            if ($uuid_binary === false) {
                secure_log("Failed to convert UUID to binary: {$uuid}");
                continue;
            }

            // Query database for player with this UUID
            $player = $db->db->player_data->findOne([
                'uuid' => new MongoDB\BSON\Binary($uuid_binary, MongoDB\BSON\Binary::TYPE_GENERIC)
            ]);

            if ($player) {
                // Get username from player data
                $username = null;
                
                // Try different username fields
                if (isset($player['username'])) {
                    $username = $player['username'];
                } elseif (isset($player['playernames']) && is_array($player['playernames']) && !empty($player['playernames'])) {
                    $username = $player['playernames'][0];
                }

                if ($username) {
                    $conversions[$uuid] = $username;
                    secure_log("Converted UUID {$uuid} to username {$username}");
                } else {
                    secure_log("Found player for UUID {$uuid} but no username available");
                }
            } else {
                secure_log("No player found for UUID {$uuid}");
            }
        } catch (Exception $e) {
            secure_log("Error converting UUID {$uuid}: " . $e->getMessage());
        }
    }

    secure_log("UUID conversion completed. Converted " . count($conversions) . " out of " . count($validUUIDs) . " UUIDs");

    echo json_encode([
        'success' => true,
        'conversions' => $conversions,
        'total_requested' => count($validUUIDs),
        'total_converted' => count($conversions)
    ]);

} catch (Exception $e) {
    secure_log("Error in convert-uuids.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}
?>
