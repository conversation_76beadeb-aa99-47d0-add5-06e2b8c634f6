<?php

require_once __DIR__ . '/../../../../includes/config.php';
require_once __DIR__ . '/../../../../includes/auth.php';
require_once __DIR__ . '/../../../../includes/db_access.php';


header('Content-Type: application/json');


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-TOKEN, X-Discord-ID, X-Discord-Username, X-Discord-Role, X-Discord-Avatar, X-Session-ID, X-Requested-With');
header('Access-Control-Allow-Credentials: true');


if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}



if (!is_authenticated()) {
    secure_log("Ban Add API: Authentication failed - User not authenticated", "warning", [
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'endpoint' => $_SERVER['REQUEST_URI'] ?? 'unknown'
    ]);
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


refresh_session();



if (!has_role(ROLE_MODERATOR)) {
    secure_log("Ban Add API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set') . ", Required: MODERATOR or higher", "security");
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Insufficient privileges - MODERATOR role or higher required to ban players']);
    exit;
}


$headers = getallheaders();
$staff_id = $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? 'unknown';
$staff_name = $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? 'unknown';


if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // SECURITY: Enforce CSRF protection for ban operations
    require_csrf_token(true);

    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);


    if (empty($data['player'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Player name is required']);
        exit;
    }

    if (empty($data['reason'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Reason is required']);
        exit;
    }

    try {

        $player_xuid = $data['player_xuid'] ?? '';


        $offense_type = $data['offense_type'] ?? '';


        $duration = $data['duration'] ?? 'permanent';


        $result = add_punishment(
            'ban',                  // punishment type
            $data['player'],        // player name
            $data['reason'],        // reason
            $staff_id,              // staff ID
            $staff_name,            // staff name
            $duration,              // duration
            $player_xuid,           // player XUID
            $offense_type           // offense type
        );


        echo json_encode($result);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error: ' . $e->getMessage()
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
