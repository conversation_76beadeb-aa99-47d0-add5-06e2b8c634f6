<?php

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';


header('Content-Type: application/json');



if (!is_authenticated()) {
    secure_log("Punishment List API: Authentication failed - User not authenticated", "warning", [
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'endpoint' => $_SERVER['REQUEST_URI'] ?? 'unknown'
    ]);
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit;
}


if (!has_role(ROLE_TRAINEE)) {
    secure_log("Punishment List API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'), "security");
    http_response_code(403);
    echo json_encode(['error' => 'Insufficient privileges - TRAINEE role or higher required']);
    exit;
}


$headers = getallheaders();
update_staff_activity(
    $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
    $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
    $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
    $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null
);


if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {

        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('punishments', $collections)) {

            echo json_encode([]);
            exit;
        }


        $filter = [];


        if (!empty($_GET['type']) && $_GET['type'] !== 'all') {
            $filter['punishment_type'] = $_GET['type'];
        }


        if (!empty($_GET['status'])) {
            if ($_GET['status'] === 'active') {
                $filter['active'] = true;
            } else if ($_GET['status'] === 'expired') {
                $filter['active'] = false;
            }
        }


        if (!empty($_GET['player'])) {
            $filter['player_name'] = ['$regex' => $_GET['player'], '$options' => 'i'];
        }


        if (!empty($_GET['staff'])) {
            $filter['staff_name'] = ['$regex' => $_GET['staff'], '$options' => 'i'];
        }


        $options = [
            'sort' => ['issued_at' => -1], // Sort by issued date descending (newest first)
            'limit' => 100 // Limit to 100 most recent punishments
        ];


        $cursor = $db->punishments->find($filter, $options);


        $punishments = [];
        foreach ($cursor as $document) {

            $punishment = [
                'punishment_id' => $document['punishment_id'] ?? ('PUN-' . substr((string)$document['_id'], -8)),
                'punishment_type' => $document['punishment_type'] ?? 'unknown',
                'player_name' => $document['player_name'] ?? 'Unknown Player',
                'player_xuid' => $document['player_xuid'] ?? '',
                'reason' => $document['reason'] ?? '',
                'staff_name' => $document['staff_name'] ?? 'Unknown Staff',
                'staff_id' => $document['staff_id'] ?? '',
                'active' => $document['active'] ?? false,
                'issued_at' => isset($document['issued_at']) ? $document['issued_at']->toDateTime()->format('c') : null,
                'expires_at' => isset($document['expires_at']) ? $document['expires_at']->toDateTime()->format('c') : null,
                'removed_at' => isset($document['removed_at']) ? $document['removed_at']->toDateTime()->format('c') : null,
                'removed_by' => $document['removed_by'] ?? null,
                'removed_by_name' => $document['removed_by_name'] ?? null,
                'removed_reason' => $document['removed_reason'] ?? null,
                'evidence' => $document['evidence'] ?? '',
                'staff_notes' => $document['staff_notes'] ?? '',
                'offense_type' => $document['offense_type'] ?? '',
                'offense_count' => $document['offense_count'] ?? 1,
                'public' => $document['public'] ?? true
            ];

            $punishments[] = $punishment;
        }


        echo json_encode($punishments);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}
?>
