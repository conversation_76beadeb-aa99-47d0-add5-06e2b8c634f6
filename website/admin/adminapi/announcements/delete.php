<?php
/**
 * Delete Announcement API Endpoint
 * Allows developers and owners to delete announcements
 */

require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/security.php';

header('Content-Type: application/json');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

secure_log("Delete Announcement API Request - Method: " . $_SERVER['REQUEST_METHOD']);
secure_log("Delete Announcement API Request - User ID: " . ($_SESSION['discord_user_id'] ?? 'Not set'));
secure_log("Delete Announcement API Request - User Role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));


if (!is_authenticated()) {
    secure_log("Delete Announcement API - Authentication failed");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


if (!has_role(ROLE_DEVELOPER) && !has_role(ROLE_OWNER)) {
    secure_log("Delete Announcement API - Authorization failed - User role: " . ($_SESSION['discord_user_role'] ?? 'Unknown'));
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access - Developer or Owner role required']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    try {
        // Get announcement ID from URL or request body
        $announcementId = null;
        
        // Try to get from URL path
        $pathInfo = $_SERVER['PATH_INFO'] ?? '';
        if (!empty($pathInfo)) {
            $announcementId = trim($pathInfo, '/');
        }
        
        // If not in path, try request body
        if (empty($announcementId)) {
            $json = file_get_contents('php://input');
            $data = json_decode($json, true);
            $announcementId = $data['announcement_id'] ?? null;
        }
        
        // Validate announcement ID
        if (empty($announcementId)) {
            secure_log("Delete Announcement API - Missing announcement ID");
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Missing announcement ID']);
            exit;
        }
        
        // Validate MongoDB ObjectId format
        if (!preg_match('/^[a-f\d]{24}$/i', $announcementId)) {
            secure_log("Delete Announcement API - Invalid announcement ID format: " . $announcementId);
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid announcement ID format']);
            exit;
        }
        
        // Get database connection
        $connection = get_db_connection();
        $db = $connection['db'];
        
        if (!$db) {
            secure_log("Delete Announcement API - Database connection failed");
            throw new Exception("Failed to connect to database");
        }
        
        // Check if announcements collection exists
        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }
        
        if (!in_array('announcements', $collections)) {
            secure_log("Delete Announcement API - Announcements collection does not exist");
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Announcements collection not found']);
            exit;
        }
        
        // Find the announcement first to get details for logging
        $announcement = $db->announcements->findOne(['_id' => new MongoDB\BSON\ObjectId($announcementId)]);
        
        if (!$announcement) {
            secure_log("Delete Announcement API - Announcement not found: " . $announcementId);
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Announcement not found']);
            exit;
        }
        
        // Delete the announcement
        $result = $db->announcements->deleteOne(['_id' => new MongoDB\BSON\ObjectId($announcementId)]);
        
        if ($result->getDeletedCount() === 0) {
            secure_log("Delete Announcement API - Failed to delete announcement: " . $announcementId);
            throw new Exception("Failed to delete announcement");
        }
        
        // Log the deletion activity
        $categoryText = ucfirst($announcement['category'] ?? 'unknown');
        $db->staff_activity_log->insertOne([
            'user_id' => $_SESSION['discord_user_id'],
            'username' => $_SESSION['discord_username'],
            'action' => "Deleted " . $categoryText . " announcement: " . ($announcement['title'] ?? 'Unknown Title'),
            'type' => 'announcement_deletion',
            'target' => 'Announcement ID: ' . $announcementId,
            'details' => "Category: " . $categoryText . " | Original Author: " . ($announcement['sender_name'] ?? 'Unknown') . " | Message: " . substr($announcement['message'] ?? '', 0, 100) . (strlen($announcement['message'] ?? '') > 100 ? '...' : ''),
            'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
            'description' => "Deleted " . $categoryText . " announcement created by " . ($announcement['sender_name'] ?? 'Unknown')
        ]);
        
        secure_log("Delete Announcement API - Successfully deleted announcement: " . $announcementId);
        
        echo json_encode([
            'success' => true,
            'message' => "Successfully deleted " . $categoryText . " announcement",
            'announcement_id' => $announcementId
        ]);
        
    } catch (Exception $e) {
        secure_log("Delete Announcement API Error: " . $e->getMessage());
        secure_log("Stack trace: " . $e->getTraceAsString());
        
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Server error: ' . $e->getMessage()
        ]);
    }
} else {
    secure_log("Delete Announcement API - Method not allowed: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
