<?php
/**
 * Mark Announcement as Read API Endpoint
 * Allows staff members to mark announcements as read
 */


require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';


header('Content-Type: application/json');


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


secure_log("Mark Announcement Read API Request - Method: " . $_SERVER['REQUEST_METHOD']);
secure_log("Mark Announcement Read API Request - Session ID: " . session_id());
secure_log("Mark Announcement Read API Request - User ID: " . ($_SESSION['discord_user_id'] ?? 'Not set'));


if (!is_authenticated()) {
    secure_log("Mark Announcement Read API - Authentication failed");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


if (!has_role(ROLE_TRAINEE)) {
    secure_log("Mark Announcement Read API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Permission denied. You do not have the required role to mark announcements as read.']);
    exit;
}


if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {

        $json = file_get_contents('php://input');
        $data = json_decode($json, true);


        if (empty($data['announcement_id'])) {
            secure_log("Mark Announcement Read API - Missing announcement_id");
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Missing announcement_id']);
            exit;
        }


        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            secure_log("Mark Announcement Read API - Database connection failed");
            throw new Exception("Failed to connect to database");
        }


        $user_id = $_SESSION['discord_user_id'];


        $result = $db->announcements->updateOne(
            [
                '_id' => new MongoDB\BSON\ObjectId($data['announcement_id']),
                'read_by' => ['$ne' => $user_id]
            ],
            [
                '$addToSet' => ['read_by' => $user_id]
            ]
        );


        echo json_encode([
            'success' => true,
            'message' => 'Announcement marked as read',
            'updated' => $result->getModifiedCount() > 0
        ]);

    } catch (Exception $e) {

        secure_log("Mark Announcement Read API Error: " . $e->getMessage());
        secure_log("Stack trace: " . $e->getTraceAsString());


        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Server error: ' . $e->getMessage()
        ]);
    }
} else {

    secure_log("Mark Announcement Read API - Method not allowed: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
