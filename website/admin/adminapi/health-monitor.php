<?php
/**
 * Health Monitor Background Process
 * 
 * Runs health checks every 5 minutes and sends Discord notifications
 * This script should be called by a cron job or similar scheduler
 */


if (isset($_SERVER['HTTP_HOST'])) {
    http_response_code(403);
    exit('Direct access not allowed');
}


require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/auth.php';


$HEALTH_CHECK_INTERVAL = 300; // 5 minutes in seconds
$LOCK_FILE = '/tmp/massacremc_health_monitor.lock';
$LAST_RUN_FILE = '/tmp/massacremc_last_health_run.txt';


if (file_exists($LOCK_FILE)) {
    $lockTime = filemtime($LOCK_FILE);
    if (time() - $lockTime < 600) { // 10 minutes max lock
        exit("Health monitor already running\n");
    }
    unlink($LOCK_FILE);
}


file_put_contents($LOCK_FILE, getmypid());


$lastRun = file_exists($LAST_RUN_FILE) ? intval(file_get_contents($LAST_RUN_FILE)) : 0;
$currentTime = time();

if ($currentTime - $lastRun < $HEALTH_CHECK_INTERVAL) {
    unlink($LOCK_FILE);
    exit("Too soon to run health check\n");
}

try {
    echo "Starting health monitor at " . date('Y-m-d H:i:s') . "\n";

    // Check if health monitor is enabled in database
    require_once __DIR__ . '/../../includes/db_access.php';
    $mongo = get_mongo_connection();
    $db = $mongo->selectDatabase('mmc');
    $collection = $db->selectCollection('system_settings');
    $healthMonitorSetting = $collection->findOne(['setting_name' => 'health_monitor']);
    $isEnabled = $healthMonitorSetting ? (bool)($healthMonitorSetting['enabled'] ?? false) : false;

    if (!$isEnabled) {
        echo "Health monitor is disabled in database settings\n";
        exit("Health monitor disabled\n");
    }

    echo "Health monitor is enabled, proceeding with checks\n";

    // Run health check
    $healthData = performHealthCheck();
    echo "Health check completed: " . json_encode($healthData) . "\n";

    // Check if we need to enable/disable maintenance mode based on health
    handleAutomaticMaintenanceMode($healthData);

    // Discord notifications disabled
    echo "Discord notifications are disabled for health monitor\n";

    // Update last run time
    file_put_contents($LAST_RUN_FILE, $currentTime);

    echo "Health monitor completed successfully\n";

} catch (Exception $e) {
    echo "Health monitor error: " . $e->getMessage() . "\n";
    error_log("Health monitor error: " . $e->getMessage());
} finally {
    // Remove lock file
    if (file_exists($LOCK_FILE)) {
        unlink($LOCK_FILE);
    }
}

function performHealthCheck() {
    $startTime = microtime(true);
    
    // Database check
    $dbStatus = checkDatabase();
    
    // Session check
    $sessionStatus = checkSessions();
    
    // File system check
    $fsStatus = checkFileSystem();
    
    $endTime = microtime(true);
    $responseTime = round(($endTime - $startTime) * 1000, 2);
    
    // Determine overall status
    $allChecks = [$dbStatus, $sessionStatus, $fsStatus];
    $healthyCount = count(array_filter($allChecks, function($check) {
        return $check['status'] === 'healthy';
    }));
    
    if ($healthyCount === count($allChecks)) {
        $overallStatus = 'healthy';
    } elseif ($healthyCount > 0) {
        $overallStatus = 'degraded';
    } else {
        $overallStatus = 'unhealthy';
    }
    
    return [
        'status' => $overallStatus,
        'timestamp' => date('c'),
        'response_time_ms' => $responseTime,
        'service' => 'MassacreMC Staff Portal',
        'checks' => [
            'database' => $dbStatus,
            'sessions' => $sessionStatus,
            'filesystem' => $fsStatus
        ]
    ];
}

function checkDatabase() {
    try {
        // Use the existing MongoDB connection from db_access.php
        require_once __DIR__ . '/../../includes/db_access.php';

        $db_access = new DatabaseAccess();

        // Test MongoDB connection by performing a simple operation
        $result = $db_access->testConnection();

        if ($result) {
            return [
                'status' => 'healthy',
                'message' => 'MongoDB connection successful'
            ];
        } else {
            return [
                'status' => 'unhealthy',
                'message' => 'MongoDB connection test failed'
            ];
        }
    } catch (Exception $e) {
        return [
            'status' => 'unhealthy',
            'message' => 'MongoDB connection failed',
            'error' => $e->getMessage()
        ];
    }
}

function checkSessions() {
    try {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        return [
            'status' => 'healthy',
            'message' => 'Session system operational'
        ];
    } catch (Exception $e) {
        return [
            'status' => 'unhealthy',
            'message' => 'Session system failed',
            'error' => $e->getMessage()
        ];
    }
}

function checkFileSystem() {
    try {
        $testFile = '/tmp/massacremc_health_test.txt';
        $testData = 'health_check_' . time();
        
        // Test write
        if (file_put_contents($testFile, $testData) === false) {
            throw new Exception('Cannot write to filesystem');
        }
        
        // Test read
        $readData = file_get_contents($testFile);
        if ($readData !== $testData) {
            throw new Exception('Filesystem read/write mismatch');
        }
        
        // Cleanup
        unlink($testFile);
        
        return [
            'status' => 'healthy',
            'message' => 'Filesystem read/write operational'
        ];
    } catch (Exception $e) {
        return [
            'status' => 'unhealthy',
            'message' => 'Filesystem check failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Handle automatic maintenance mode based on health status
 */
function handleAutomaticMaintenanceMode($healthData) {
    try {
        $currentStatus = $healthData['status'] ?? 'unknown';
        echo "Current health status: $currentStatus\n";

        // Get database connection
        require_once __DIR__ . '/../../includes/db_access.php';
        $mongo = get_mongo_connection();
        $db = $mongo->selectDatabase('mmc');
        $collection = $db->selectCollection('system_settings');

        // Check current maintenance mode status
        $maintenanceSetting = $collection->findOne(['setting_name' => 'maintenance_mode']);
        $isCurrentlyInMaintenance = $maintenanceSetting ? (bool)($maintenanceSetting['enabled'] ?? false) : false;
        $isAutoMaintenance = $maintenanceSetting ? (bool)($maintenanceSetting['auto_enabled'] ?? false) : false;

        echo "Current maintenance mode: " . ($isCurrentlyInMaintenance ? "enabled" : "disabled") . "\n";
        echo "Auto maintenance: " . ($isAutoMaintenance ? "yes" : "no") . "\n";

        // Determine if we should enable/disable maintenance mode
        $shouldEnableMaintenance = false;
        $maintenanceMessage = '';

        if ($currentStatus === 'unhealthy') {
            // Check which critical services are down
            $failedServices = [];
            foreach ($healthData['checks'] ?? [] as $serviceName => $serviceData) {
                if ($serviceData['status'] === 'unhealthy') {
                    $failedServices[] = $serviceName;
                }
            }

            if (!empty($failedServices)) {
                $shouldEnableMaintenance = true;
                $maintenanceMessage = "We're currently experiencing technical difficulties with our " .
                                    implode(', ', $failedServices) . " service" .
                                    (count($failedServices) > 1 ? "s" : "") .
                                    ". Our team is working to resolve this issue. Please check back shortly.";
            }
        }

        // Only make changes if needed
        if ($shouldEnableMaintenance && !$isCurrentlyInMaintenance) {
            // Enable automatic maintenance mode
            echo "Enabling automatic maintenance mode due to unhealthy status\n";

            $collection->updateOne(
                ['setting_name' => 'maintenance_mode'],
                [
                    '$set' => [
                        'enabled' => true,
                        'auto_enabled' => true,
                        'message' => $maintenanceMessage,
                        'updated_by' => 'health_monitor',
                        'updated_at' => date('c'),
                        'auto_enabled_at' => date('c')
                    ]
                ],
                ['upsert' => true]
            );

            echo "Automatic maintenance mode enabled\n";

        } elseif (!$shouldEnableMaintenance && $isCurrentlyInMaintenance && $isAutoMaintenance) {
            // Disable automatic maintenance mode (only if it was auto-enabled)
            echo "Disabling automatic maintenance mode - services are healthy\n";

            $collection->updateOne(
                ['setting_name' => 'maintenance_mode'],
                [
                    '$set' => [
                        'enabled' => false,
                        'auto_enabled' => false,
                        'message' => '',
                        'updated_by' => 'health_monitor',
                        'updated_at' => date('c'),
                        'auto_disabled_at' => date('c')
                    ]
                ]
            );

            echo "Automatic maintenance mode disabled\n";
        } else {
            echo "No maintenance mode changes needed\n";
        }

    } catch (Exception $e) {
        echo "Error handling automatic maintenance mode: " . $e->getMessage() . "\n";
        error_log("Automatic maintenance mode error: " . $e->getMessage());
    }
}

?>
