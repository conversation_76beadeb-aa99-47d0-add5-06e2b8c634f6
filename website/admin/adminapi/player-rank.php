<?php
/**
 * Player Rank Management API
 * Handles rank changes for players - <PERSON><PERSON><PERSON> and Owner access only
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/db_access.php';
require_once __DIR__ . '/../../includes/api_client.php';


header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');


require_auth(ROLE_DEVELOPER);


if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}



require_csrf_check(true); // true for API mode

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
        exit;
    }

    // Validate required fields
    $required_fields = ['uuid', 'username', 'new_rank'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => "Missing required field: $field"]);
            exit;
        }
    }

    $uuid = trim($data['uuid']);
    $username = trim($data['username']);
    $new_rank = strtolower(trim($data['new_rank']));
    $reason = isset($data['reason']) ? trim($data['reason']) : '';

    // Define valid ranks
    $valid_ranks = [
        'player', 'vip', 'mvp', 'mmp', 'mgp', 'mlp', 'c', 'builder', 
        'trainee', 'support', 'moderator', 'admin', 'owner', 'yt', 'youtuber', 'secretary'
    ];

    // Validate rank
    if (!in_array($new_rank, $valid_ranks)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid rank specified']);
        exit;
    }

    // Get database connection
    $connection = get_db_connection();
    $db = $connection['db'];

    if (!$db) {
        throw new Exception("Failed to connect to database");
    }

    // Check if player exists (using username like the Go code does with FindPlayerByName)
    $player = $db->player_data->findOne(['Username' => $username]);
    if (!$player) {
        // Try case-insensitive search as fallback
        $player = $db->player_data->findOne(['Username' => new MongoDB\BSON\Regex('^' . preg_quote($username) . '$', 'i')]);
        if (!$player) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Player not found']);
            exit;
        }
    }

    // Get the actual UUID from the found player (like Go code gets dt.UUID)
    $actual_uuid = $player['UUID'] ?? null;
    if (!$actual_uuid) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Player UUID not found']);
        exit;
    }

    // Get current rank for logging (using RankId field like the Go code)
    $current_rank = isset($player['RankId']) ? $player['RankId'] : 'player';

    // Update player rank (using RankId field like the Go code)
    $update_result = $db->player_data->updateOne(
        ['UUID' => $actual_uuid],
        ['$set' => ['RankId' => $new_rank]]
    );

    if ($update_result->getModifiedCount() === 0 && $update_result->getMatchedCount() === 0) {
        throw new Exception("Failed to update player rank");
    }

    // Update player data in Go API to sync the rank change
    try {
        $apiClient = new ApiClient();

        // Method 1: Try to update player rank via dedicated endpoint
        $rankUpdateResult = null;
        try {
            $rankData = [
                'rank_id' => $new_rank,
                'admin_action' => true,
                'updated_by' => $_SESSION['discord_username'] ?? 'Unknown'
            ];
            $endpoint = 'api/players/' . urlencode($actual_uuid) . '/rank';
            $rankUpdateResult = $apiClient->makeRequest($endpoint, 'PUT', $rankData);
            secure_log("Player Rank API: Successfully updated rank via dedicated endpoint for {$username} ({$actual_uuid})");
        } catch (Exception $e) {
            secure_log("Player Rank API: Dedicated endpoint failed for {$username}: " . $e->getMessage());
        }

        // Method 2: Try to update via general player update endpoint
        if (!$rankUpdateResult || isset($rankUpdateResult['error'])) {
            try {
                $playerData = [
                    'RankId' => $new_rank
                ];
                $endpoint = 'api/players/' . urlencode($actual_uuid);
                $rankUpdateResult = $apiClient->makeRequest($endpoint, 'PATCH', $playerData);
                secure_log("Player Rank API: Successfully updated rank via player endpoint for {$username} ({$actual_uuid})");
            } catch (Exception $e) {
                secure_log("Player Rank API: Player endpoint failed for {$username}: " . $e->getMessage());
            }
        }

        // Method 3: Try to execute rank command via admin API (mimics the Go rank command)
        if (!$rankUpdateResult || isset($rankUpdateResult['error'])) {
            try {
                $commandData = [
                    'command' => 'rank',
                    'parameters' => [
                        'player' => $username,
                        'role' => $new_rank,
                        'admin_forced' => true
                    ]
                ];
                $endpoint = 'api/admin/execute';
                $rankUpdateResult = $apiClient->makeRequest($endpoint, 'POST', $commandData);
                secure_log("Player Rank API: Successfully executed rank command for {$username} ({$actual_uuid})");
            } catch (Exception $e) {
                secure_log("Player Rank API: Command execution failed for {$username}: " . $e->getMessage());
            }
        }

        // Method 4: Try to trigger user data update (like the Go code does)
        if (!$rankUpdateResult || isset($rankUpdateResult['error'])) {
            try {
                $updateData = [
                    'action' => 'update_user_data',
                    'uuid' => $actual_uuid,
                    'force_sync' => true
                ];
                $endpoint = 'api/admin/user/update';
                $rankUpdateResult = $apiClient->makeRequest($endpoint, 'POST', $updateData);
                secure_log("Player Rank API: Successfully triggered user data update for {$username} ({$actual_uuid})");
            } catch (Exception $e) {
                secure_log("Player Rank API: User data update failed for {$username}: " . $e->getMessage());
            }
        }

        // If player is online, try to update their nametag (like the Go code does)
        try {
            $nametagData = [
                'action' => 'update_nametag',
                'uuid' => $actual_uuid,
                'force_refresh' => true
            ];
            $endpoint = 'api/admin/player/nametag';
            $nametagResult = $apiClient->makeRequest($endpoint, 'POST', $nametagData);
            secure_log("Player Rank API: Successfully updated nametag for {$username} ({$actual_uuid})");
        } catch (Exception $e) {
            secure_log("Player Rank API: Nametag update failed for {$username}: " . $e->getMessage());
            // This is not critical, so we don't fail the whole operation
        }

    } catch (Exception $e) {
        secure_log("Player Rank API: Error updating Go API for {$username}: " . $e->getMessage());
        // Don't fail the whole operation if API update fails, but log it
    }

    // Log the rank change for audit purposes
    $audit_data = [
        'action' => 'rank_change',
        'target_uuid' => $actual_uuid,
        'target_username' => $username,
        'old_rank' => $current_rank,
        'new_rank' => $new_rank,
        'reason' => $reason,
        'staff_id' => $_SESSION['discord_user_id'] ?? 'unknown',
        'staff_username' => $_SESSION['discord_username'] ?? 'Unknown',
        'staff_role' => $_SESSION['discord_user_role'] ?? 'UNKNOWN',
        'timestamp' => new MongoDB\BSON\UTCDateTime(),
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];

    // Insert audit log
    $db->audit_logs->insertOne($audit_data);

    // Log to error log as well
    secure_log("RANK CHANGE: {$_SESSION['discord_username']} changed {$username} ({$actual_uuid}) rank from {$current_rank} to {$new_rank}. Reason: {$reason}");

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Player rank updated successfully',
        'data' => [
            'uuid' => $uuid,
            'username' => $username,
            'old_rank' => $current_rank,
            'new_rank' => $new_rank,
            'updated_by' => $_SESSION['discord_username'] ?? 'Unknown'
        ]
    ]);

} catch (Exception $e) {
    secure_log("Player Rank API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
