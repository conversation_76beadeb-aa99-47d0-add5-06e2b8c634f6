<?php
/**
 * Secure Health Check Endpoint for Sentry Uptime Monitoring
 * 
 * This endpoint provides comprehensive health status for the staff portal
 * and can ONLY be accessed by authorized monitoring services (Sentry).
 */


header('Content-Type: application/json');
header('X-Robots-Tag: noindex, nofollow');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: no-referrer');
header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
header('Cache-Control: no-cache, no-store, must-revalidate, private');
header('Pragma: no-cache');
header('Expires: 0');


function getClientIP() {
    $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'REMOTE_ADDR'];
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            return trim($ips[0]);
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}


session_start();
$rate_limit_key = 'health_check_' . (getClientIP() ?? 'unknown');
$current_time = time();
$rate_limit_window = 60; // 1 minute
$max_requests = 10; // Max 10 requests per minute

if (!isset($_SESSION[$rate_limit_key])) {
    $_SESSION[$rate_limit_key] = [];
}


$_SESSION[$rate_limit_key] = array_filter($_SESSION[$rate_limit_key], function($timestamp) use ($current_time, $rate_limit_window) {
    return ($current_time - $timestamp) < $rate_limit_window;
});


if (count($_SESSION[$rate_limit_key]) >= $max_requests) {
    http_response_code(429);
    echo json_encode([
        'status' => 'error',
        'message' => 'Rate limit exceeded',
        'retry_after' => $rate_limit_window
    ]);
    exit;
}


$_SESSION[$rate_limit_key][] = $current_time;


function getMonitoringToken() {
    // Use same token file path logic as monitoring-token.php
    $possiblePaths = [
        '/tmp/massacremc_monitoring_token.txt',
        sys_get_temp_dir() . '/massacremc_monitoring_token.txt',
        __DIR__ . '/monitoring_token.txt'
    ];

    foreach ($possiblePaths as $path) {
        if (file_exists($path)) {
            $tokenFile = $path;
            break;
        }
    }

    // If no existing file found, use the first writable location
    if (!isset($tokenFile)) {
        foreach ($possiblePaths as $path) {
            $dir = dirname($path);
            if (is_writable($dir)) {
                $tokenFile = $path;
                break;
            }
        }
    }

    // Fallback
    if (!isset($tokenFile)) {
        $tokenFile = __DIR__ . '/monitoring_token.txt';
    }

    // Create data directory if it doesn't exist
    $dataDir = dirname($tokenFile);
    if (!is_dir($dataDir)) {
        mkdir($dataDir, 0755, true);
    }

    // Check if token file exists and is readable
    if (file_exists($tokenFile) && is_readable($tokenFile)) {
        $token = trim(file_get_contents($tokenFile));
        if (!empty($token) && strlen($token) >= 64) {
            return $token;
        }
    }

    // Generate new secure token
    $token = bin2hex(random_bytes(32)) . '_' . hash('sha256',
        ($_SERVER['HTTP_HOST'] ?? 'localhost') .
        microtime(true) .
        random_int(100000, 999999)
    );

    // Save token to file with error handling
    $result = file_put_contents($tokenFile, $token, LOCK_EX);
    if ($result !== false && file_exists($tokenFile)) {
        chmod($tokenFile, 0600); // Restrict file permissions
    }

    return $token;
}

$MONITORING_TOKEN = getMonitoringToken();
$ALLOWED_IPS = [
    // Sentry IP ranges (updated 2024)
    '************',
    '*************',
    '**************',
    '************',
    '**************',
    '***********',      // New Sentry IP from logs
    '*************',    // Cloudflare/Sentry IP
    '**************',   // Cloudflare/Sentry IP
    // Add your server's IP for internal monitoring
    $_SERVER['SERVER_ADDR'] ?? '127.0.0.1',
    '127.0.0.1',
    '::1'
];




function isAuthorized() {
    global $MONITORING_TOKEN, $ALLOWED_IPS;

    $clientIP = getClientIP();

    // Only accept header-based authentication for security
    $providedToken = $_SERVER['HTTP_X_MONITORING_TOKEN'] ?? '';

    // Validate token format (must be non-empty and reasonable length)
    if (empty($providedToken) || strlen($providedToken) < 32 || strlen($providedToken) > 256) {
        return false;
    }

    // Check for suspicious patterns in token
    if (preg_match('/[<>"\'\\\]/', $providedToken)) {
        return false;
    }

    // Check token authentication using timing-safe comparison
    $validToken = hash_equals($MONITORING_TOKEN, $providedToken);

    // Check IP whitelist (more restrictive)
    $validIP = in_array($clientIP, $ALLOWED_IPS);

    // Additional security: check User-Agent for monitoring services
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $referer = $_SERVER['HTTP_REFERER'] ?? '';

    // Allow browser requests from admin settings page
    $isBrowserTest = strpos($userAgent, 'Chrome') !== false ||
                     strpos($userAgent, 'Firefox') !== false ||
                     strpos($userAgent, 'Safari') !== false;
    $isFromSettings = strpos($referer, '/dashboard/settings') !== false;

    $validUserAgent = empty($userAgent) ||
                     strpos($userAgent, 'Sentry') !== false ||
                     strpos($userAgent, 'SentryUptimeBot') !== false ||
                     strpos($userAgent, 'curl') !== false ||
                     strpos($userAgent, 'wget') !== false ||
                     strpos($userAgent, 'monitoring') !== false ||
                     strpos($userAgent, 'Uptime') !== false ||
                     strpos($userAgent, 'MassacreMC-Internal') !== false ||
                     ($isBrowserTest && $isFromSettings); // Allow browser tests from settings

    // Log unauthorized access attempts
    if (!$validToken && !$validIP) {
        error_log("Unauthorized health check access attempt from IP: $clientIP, Header token provided: " . (!empty($providedToken) ? 'yes' : 'no') . ", User-Agent: $userAgent");
    }

    return $validToken && $validUserAgent;
}


function sendDiscordNotification($healthData) {
    // Load Discord webhook from environment
    $webhookUrl = $_ENV['DISCORD_WEBHOOK_URL'] ?? getenv('DISCORD_WEBHOOK_URL');

    if (empty($webhookUrl) || !filter_var($webhookUrl, FILTER_VALIDATE_URL)) {
        return; // No webhook configured or invalid URL
    }

    // Validate Discord webhook format
    if (!preg_match('/^https:\/\/discord\.com\/api\/webhooks\/\d+\/[a-zA-Z0-9_-]+$/', $webhookUrl)) {
        error_log("Invalid Discord webhook URL format");
        return;
    }

    // Check if we should send notification (every 5 minutes or status change)
    $statusFile = getStatusFilePath();
    $timestampFile = getTimestampFilePath();

    $lastStatus = file_exists($statusFile) ? trim(file_get_contents($statusFile)) : '';
    $lastWebhookTime = file_exists($timestampFile) ? intval(file_get_contents($timestampFile)) : 0;
    $currentStatus = $healthData['status'] ?? 'unknown';
    $currentTime = time();

    // Send notification if:
    // 1. Status changed, OR
    // 2. It's been 5+ minutes since last notification, OR
    // 3. System is unhealthy (more frequent updates)
    $statusChanged = $lastStatus !== $currentStatus;
    $timeSinceLastWebhook = $currentTime - $lastWebhookTime;
    $shouldSendRegularUpdate = $timeSinceLastWebhook >= 300; // 5 minutes
    $isUnhealthy = $currentStatus === 'unhealthy';

    if (!$statusChanged && !$shouldSendRegularUpdate && !$isUnhealthy) {
        return; // No need to send notification
    }

    // Save current status and timestamp
    file_put_contents($statusFile, $currentStatus, LOCK_EX);
    file_put_contents($timestampFile, $currentTime, LOCK_EX);

    // Determine embed color and urgency
    $colors = [
        'healthy' => 0x00FF00,    // Green
        'degraded' => 0xFF8C00,   // Orange
        'unhealthy' => 0xFF0000,  // Red
        'unknown' => 0x808080     // Gray
    ];

    $color = $colors[$currentStatus] ?? $colors['unknown'];

    // Determine notification type
    $notificationType = $statusChanged ? 'Status Change' : 'Regular Update';
    $titleIcon = $statusChanged ? '🚨' : '🏥';

    // Create Discord embed
    $embed = [
        'title' => "$titleIcon Staff Portal Health Status",
        'description' => "**Status:** " . strtoupper($currentStatus) . "\n**Type:** $notificationType",
        'color' => $color,
        'timestamp' => date('c'),
        'fields' => [],
        'footer' => [
            'text' => 'MassacreMC Health Monitor • Updates every 5 minutes'
        ]
    ];

    // Add response time if available
    if (isset($healthData['response_time_ms'])) {
        $embed['fields'][] = [
            'name' => '⏱️ Response Time',
            'value' => $healthData['response_time_ms'] . 'ms',
            'inline' => true
        ];
    }

    // Add uptime information
    $uptimeFile = getUptimeFilePath();
    if ($statusChanged) {
        // Record when status changed
        file_put_contents($uptimeFile, $currentTime, LOCK_EX);
        $statusDuration = 'Just changed';
    } else {
        // Calculate how long we've been in current status
        $statusChangeTime = file_exists($uptimeFile) ? intval(file_get_contents($uptimeFile)) : $currentTime;
        $duration = $currentTime - $statusChangeTime;
        $statusDuration = formatDuration($duration);
    }

    $embed['fields'][] = [
        'name' => '📊 Status Duration',
        'value' => $statusDuration,
        'inline' => true
    ];

    // Add failed checks for unhealthy status
    if ($currentStatus === 'unhealthy' && isset($healthData['checks'])) {
        $failedChecks = [];
        foreach ($healthData['checks'] as $checkName => $checkData) {
            if ($checkData['status'] === 'unhealthy') {
                $failedChecks[] = ucfirst($checkName);
            }
        }

        if (!empty($failedChecks)) {
            $embed['fields'][] = [
                'name' => '❌ Failed Checks',
                'value' => implode(', ', $failedChecks),
                'inline' => false
            ];
        }
    }

    // Add error details if present
    if (isset($healthData['error'])) {
        $embed['fields'][] = [
            'name' => '🚨 Error Details',
            'value' => substr($healthData['details'] ?? $healthData['error'], 0, 1000),
            'inline' => false
        ];
    }

    // Prepare webhook payload
    $payload = [
        'embeds' => [$embed],
        'username' => 'Health Monitor',
        'avatar_url' => 'https://cdn.discordapp.com/attachments/your-avatar-url.png'
    ];

    // Send webhook (non-blocking)
    sendWebhookAsync($webhookUrl, $payload);
}

function sendWebhookAsync($webhookUrl, $payload) {
    // Use cURL for non-blocking webhook delivery
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $webhookUrl,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($payload),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'User-Agent: MassacreMC-Health-Monitor/1.0'
        ],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 5,
        CURLOPT_CONNECTTIMEOUT => 3,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_FOLLOWLOCATION => false
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    // Log webhook delivery status
    if ($httpCode !== 204) {
        error_log("Discord webhook delivery failed: HTTP $httpCode");
    }
}

function formatDuration($seconds) {
    if ($seconds < 60) {
        return $seconds . 's';
    } elseif ($seconds < 3600) {
        $minutes = floor($seconds / 60);
        return $minutes . 'm';
    } elseif ($seconds < 86400) {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        return $hours . 'h ' . $minutes . 'm';
    } else {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        return $days . 'd ' . $hours . 'h';
    }
}

function getWebhookFilePath() {
    $possiblePaths = [
        '/tmp/massacremc_discord_webhook.txt',
        sys_get_temp_dir() . '/massacremc_discord_webhook.txt',
        __DIR__ . '/discord_webhook.txt'
    ];

    foreach ($possiblePaths as $path) {
        $dir = dirname($path);
        if (is_writable($dir)) {
            return $path;
        }
    }

    return __DIR__ . '/discord_webhook.txt';
}

function getStatusFilePath() {
    $possiblePaths = [
        '/tmp/massacremc_health_status.txt',
        sys_get_temp_dir() . '/massacremc_health_status.txt',
        __DIR__ . '/health_status.txt'
    ];

    foreach ($possiblePaths as $path) {
        $dir = dirname($path);
        if (is_writable($dir)) {
            return $path;
        }
    }

    return __DIR__ . '/health_status.txt';
}

function getTimestampFilePath() {
    $possiblePaths = [
        '/tmp/massacremc_webhook_time.txt',
        sys_get_temp_dir() . '/massacremc_webhook_time.txt',
        __DIR__ . '/webhook_time.txt'
    ];

    foreach ($possiblePaths as $path) {
        $dir = dirname($path);
        if (is_writable($dir)) {
            return $path;
        }
    }

    return __DIR__ . '/webhook_time.txt';
}

function getUptimeFilePath() {
    $possiblePaths = [
        '/tmp/massacremc_status_change.txt',
        sys_get_temp_dir() . '/massacremc_status_change.txt',
        __DIR__ . '/status_change.txt'
    ];

    foreach ($possiblePaths as $path) {
        $dir = dirname($path);
        if (is_writable($dir)) {
            return $path;
        }
    }

    return __DIR__ . '/status_change.txt';
}


if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}


if (!in_array($_SERVER['REQUEST_METHOD'], ['GET', 'HEAD'])) {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
    exit;
}


if (!isAuthorized()) {
    http_response_code(403);
    echo json_encode([
        'status' => 'error', 
        'message' => 'Access denied',
        'client_ip' => getClientIP(),
        'timestamp' => date('c'),
        'debug' => [
            'token_provided' => !empty($_GET['token'] ?? $_SERVER['HTTP_X_MONITORING_TOKEN'] ?? ''),
            'ip_check' => in_array(getClientIP(), $ALLOWED_IPS),
            'server_ip' => $_SERVER['SERVER_ADDR'] ?? 'unknown'
        ]
    ]);
    exit;
}

try {
    $startTime = microtime(true);
    $healthStatus = [
        'status' => 'healthy',
        'timestamp' => date('c'),
        'service' => 'MassacreMC Staff Portal',
        'version' => '1.0.0',
        'checks' => []
    ];

    // Check 1: Basic PHP functionality
    $healthStatus['checks']['php'] = [
        'status' => 'healthy',
        'version' => PHP_VERSION,
        'memory_usage' => memory_get_usage(true),
        'memory_peak' => memory_get_peak_usage(true)
    ];

    // Check 2: Database connectivity
    try {
        require_once __DIR__ . '/../../includes/db_access.php';
        $db = new DatabaseAccess();
        
        // Simple database ping
        $result = $db->db->command(['ping' => 1]);
        
        $healthStatus['checks']['database'] = [
            'status' => 'healthy',
            'type' => 'MongoDB',
            'response_time_ms' => round((microtime(true) - $startTime) * 1000, 2)
        ];
    } catch (Exception $e) {
        $healthStatus['checks']['database'] = [
            'status' => 'unhealthy',
            'error' => 'Database connection failed',
            'details' => $e->getMessage()
        ];
        $healthStatus['status'] = 'degraded';
    }

    // Check 3: Session functionality
    try {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $healthStatus['checks']['sessions'] = [
            'status' => 'healthy',
            'session_status' => session_status(),
            'session_id' => session_id() ? 'active' : 'none'
        ];
    } catch (Exception $e) {
        $healthStatus['checks']['sessions'] = [
            'status' => 'unhealthy',
            'error' => 'Session functionality failed',
            'details' => $e->getMessage()
        ];
        $healthStatus['status'] = 'degraded';
    }

    // Check 4: File system access
    try {
        $tempFile = sys_get_temp_dir() . '/health_check_' . uniqid();
        file_put_contents($tempFile, 'test');
        $canRead = file_get_contents($tempFile) === 'test';
        unlink($tempFile);
        
        $healthStatus['checks']['filesystem'] = [
            'status' => $canRead ? 'healthy' : 'unhealthy',
            'temp_dir' => sys_get_temp_dir(),
            'writable' => is_writable(sys_get_temp_dir())
        ];
        
        if (!$canRead) {
            $healthStatus['status'] = 'degraded';
        }
    } catch (Exception $e) {
        $healthStatus['checks']['filesystem'] = [
            'status' => 'unhealthy',
            'error' => 'File system access failed',
            'details' => $e->getMessage()
        ];
        $healthStatus['status'] = 'degraded';
    }

    // Check 5: External API connectivity (optional)
    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'method' => 'HEAD'
            ]
        ]);
        
        $headers = @get_headers('https://api.mcsrvstat.us/3/play.massacremc.net', 1, $context);
        $apiHealthy = $headers && strpos($headers[0], '200') !== false;
        
        $healthStatus['checks']['external_api'] = [
            'status' => $apiHealthy ? 'healthy' : 'degraded',
            'endpoint' => 'mcsrvstat.us',
            'reachable' => $apiHealthy
        ];
        
        if (!$apiHealthy) {
            $healthStatus['status'] = 'degraded';
        }
    } catch (Exception $e) {
        $healthStatus['checks']['external_api'] = [
            'status' => 'degraded',
            'error' => 'External API check failed',
            'details' => $e->getMessage()
        ];
    }

    // Calculate total response time
    $totalTime = microtime(true) - $startTime;
    $healthStatus['response_time_ms'] = round($totalTime * 1000, 2);
    
    // Determine overall status
    $unhealthyChecks = array_filter($healthStatus['checks'], function($check) {
        return $check['status'] === 'unhealthy';
    });
    
    if (count($unhealthyChecks) > 0) {
        $healthStatus['status'] = 'unhealthy';
        http_response_code(503); // Service Unavailable
    } elseif ($healthStatus['status'] === 'degraded') {
        http_response_code(200); // Still return 200 for degraded
    } else {
        http_response_code(200);
    }

    // Add uptime information
    $healthStatus['uptime'] = [
        'server_uptime' => function_exists('sys_getloadavg') ? sys_getloadavg() : 'unavailable',
        'php_uptime' => time() - $_SERVER['REQUEST_TIME'],
        'current_load' => function_exists('sys_getloadavg') ? sys_getloadavg()[0] : 'unavailable'
    ];

    // Send Discord webhook notification if status changed
    sendDiscordNotification($healthStatus);

    echo json_encode($healthStatus, JSON_PRETTY_PRINT);

} catch (Exception $e) {
    http_response_code(500);
    $errorStatus = [
        'status' => 'unhealthy',
        'timestamp' => date('c'),
        'service' => 'MassacreMC Staff Portal',
        'error' => 'Health check failed',
        'details' => $e->getMessage()
    ];

    // Send Discord notification for critical errors
    sendDiscordNotification($errorStatus);

    echo json_encode($errorStatus, JSON_PRETTY_PRINT);
}
?>
