<?php
require_once '../../includes/auth.php';
require_once '../../includes/config.php';

// Require authentication and developer role
require_auth(ROLE_DEVELOPER);

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Check if vulnerability audit script exists
    $audit_script = '/home/<USER>/Production/vulnerability_audit.sh';
    
    if (!file_exists($audit_script)) {
        throw new Exception('Vulnerability audit script not found');
    }
    
    // Run security scan in background
    $command = "cd /home/<USER>/Production && sudo ./vulnerability_audit.sh full > /tmp/security_scan_" . date('Y-m-d_H-i-s') . ".log 2>&1 &";
    $output = shell_exec($command);
    
    echo json_encode([
        'status' => 'success',
        'message' => 'Security scan initiated successfully. Results will be available in the security reports.',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Failed to start security scan: ' . $e->getMessage()
    ]);
}
?>
