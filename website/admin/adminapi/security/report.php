<?php
require_once '../../includes/auth.php';
require_once '../../includes/config.php';

// Require authentication and developer role
require_auth(ROLE_DEVELOPER);

// Check if this is a download request
if (isset($_GET['download']) && $_GET['download'] == '1') {
    // Generate and download security report
    $report_content = generateSecurityReport();
    
    header('Content-Type: text/plain');
    header('Content-Disposition: attachment; filename="security_report_' . date('Y-m-d_H-i-s') . '.txt"');
    echo $report_content;
    exit;
}

function generateSecurityReport() {
    $report = "MassacreMC Security Report\n";
    $report .= "Generated: " . date('Y-m-d H:i:s') . "\n";
    $report .= str_repeat("=", 50) . "\n\n";
    
    // System Information
    $report .= "SYSTEM INFORMATION\n";
    $report .= str_repeat("-", 20) . "\n";
    $report .= "Hostname: " . gethostname() . "\n";
    $report .= "OS: " . php_uname('s') . " " . php_uname('r') . "\n";
    $report .= "PHP Version: " . phpversion() . "\n";
    $report .= "Server Time: " . date('Y-m-d H:i:s T') . "\n\n";
    
    // Security Metrics
    $report .= "SECURITY METRICS\n";
    $report .= str_repeat("-", 20) . "\n";
    
    // Blocked IPs
    $blocked_files = [
        '/etc/massacremc/security/blocked_ips.txt',
        '/var/log/security/blocked_ips.txt',
        '/tmp/blocked_ips.txt'
    ];
    
    $blocked_count = 0;
    foreach ($blocked_files as $file) {
        if (file_exists($file)) {
            $lines = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $blocked_count = count($lines);
            break;
        }
    }
    $report .= "Blocked IPs: {$blocked_count}\n";
    
    // Active Connections
    $connections = shell_exec('netstat -tn 2>/dev/null | grep ESTABLISHED | wc -l');
    $report .= "Active Connections: " . trim($connections) . "\n";
    
    // System Load
    if (function_exists('sys_getloadavg')) {
        $load = sys_getloadavg();
        $report .= "System Load: " . round($load[0], 2) . "\n";
    }
    
    // Memory Usage
    if (file_exists('/proc/meminfo')) {
        $meminfo = file_get_contents('/proc/meminfo');
        preg_match('/MemTotal:\s+(\d+)/', $meminfo, $total);
        preg_match('/MemAvailable:\s+(\d+)/', $meminfo, $available);
        
        if ($total && $available) {
            $total_mb = round($total[1] / 1024, 1);
            $available_mb = round($available[1] / 1024, 1);
            $used_mb = $total_mb - $available_mb;
            $percent = round(($used_mb / $total_mb) * 100, 1);
            $report .= "Memory Usage: {$used_mb}MB / {$total_mb}MB ({$percent}%)\n";
        }
    }
    
    // Disk Usage
    $total = disk_total_space('/');
    $free = disk_free_space('/');
    if ($total && $free) {
        $used = $total - $free;
        $percent = round(($used / $total) * 100, 1);
        $used_gb = round($used / 1024 / 1024 / 1024, 1);
        $total_gb = round($total / 1024 / 1024 / 1024, 1);
        $report .= "Disk Usage: {$used_gb}GB / {$total_gb}GB ({$percent}%)\n";
    }
    
    $report .= "\n";
    
    // Recent Security Events
    $report .= "RECENT SECURITY EVENTS\n";
    $report .= str_repeat("-", 25) . "\n";
    
    $threat_files = [
        '/var/log/security/threats.log',
        '/var/log/security_monitor.log',
        '/tmp/threats.log'
    ];
    
    $found_threats = false;
    foreach ($threat_files as $file) {
        if (file_exists($file)) {
            $lines = file($file);
            $recent_lines = array_slice(array_reverse($lines), 0, 20);
            
            foreach ($recent_lines as $line) {
                $line = trim($line);
                if (!empty($line) && 
                    (strpos($line, 'THREAT') !== false || 
                     strpos($line, 'BLOCKED') !== false || 
                     strpos($line, 'WARNING') !== false)) {
                    $report .= $line . "\n";
                    $found_threats = true;
                }
            }
            break;
        }
    }
    
    if (!$found_threats) {
        $report .= "No recent security events found\n";
    }
    
    $report .= "\n";
    
    // Firewall Rules Summary
    $report .= "FIREWALL RULES SUMMARY\n";
    $report .= str_repeat("-", 25) . "\n";
    
    $firewall_output = shell_exec('iptables -L INPUT -n --line-numbers 2>/dev/null | head -20');
    if ($firewall_output) {
        $report .= $firewall_output . "\n";
    } else {
        $report .= "Unable to retrieve firewall rules\n";
    }
    
    // Service Status
    $report .= "SERVICE STATUS\n";
    $report .= str_repeat("-", 15) . "\n";
    
    $services = [
        'apache2' => 'Web Server',
        'mysql' => 'Database',
        'fail2ban' => 'Intrusion Prevention',
        'massacremc-security-monitor' => 'Security Monitor'
    ];
    
    foreach ($services as $service => $description) {
        $status = shell_exec("systemctl is-active {$service} 2>/dev/null");
        $status = trim($status) === 'active' ? 'Running' : 'Stopped';
        $report .= "{$description}: {$status}\n";
    }
    
    $report .= "\n";
    $report .= "Report generated by MassacreMC Security Dashboard\n";
    $report .= "For more information, visit the admin security dashboard\n";
    
    return $report;
}

// If not a download request, show the report page
$pageTitle = 'Security Report';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - MassacreMC Security</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .container { margin-top: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .report-content { background: #1e1e1e; color: #f8f9fa; padding: 20px; border-radius: 8px; font-family: 'Courier New', monospace; font-size: 0.9rem; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-file-alt"></i> Security Report</h1>
            <p class="mb-0">Comprehensive security status and metrics</p>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-chart-line"></i> Current Security Status</h5>
                        <div>
                            <button class="btn btn-success btn-sm me-2" onclick="window.open('?download=1', '_blank')">
                                <i class="fas fa-download"></i> Download Report
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="report-content">
                            <?= htmlspecialchars(generateSecurityReport()) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools"></i> Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-primary me-2" onclick="window.close()">
                            <i class="fas fa-arrow-left"></i> Back to Security Dashboard
                        </button>
                        <button class="btn btn-outline-info me-2" onclick="window.open('?download=1', '_blank')">
                            <i class="fas fa-download"></i> Download as Text File
                        </button>
                        <button class="btn btn-outline-success" onclick="emailReport()">
                            <i class="fas fa-envelope"></i> Email Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function emailReport() {
            const subject = encodeURIComponent('MassacreMC Security Report - ' + new Date().toLocaleDateString());
            const body = encodeURIComponent('Please find the security report attached. Generated on: ' + new Date().toLocaleString());
            window.open(`mailto:?subject=${subject}&body=${body}`);
        }
    </script>
</body>
</html>
