<?php
/**
 * Security Settings Update API Endpoint
 * Updates security settings like session timeout, login attempts, and IP whitelist
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


if (!is_authenticated()) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


if (!has_role(ROLE_DEVELOPER)) {
    secure_log("Security Settings API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['success' => false, 'message' => 'Developer privileges required']);
    exit;
}


if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('HTTP/1.1 405 Method Not Allowed');
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}


$data = json_decode(file_get_contents('php://input'), true);

// Verify CSRF token for security settings updates
$token = $data['csrf_token'] ??
         $_POST['csrf_token'] ??
         $_SERVER['HTTP_X_CSRF_TOKEN'] ??
         getallheaders()['X-CSRF-TOKEN'] ??
         null;

if (!$token || !verify_csrf_token($token)) {
    secure_log("Security Settings Update: CSRF token validation failed");
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['success' => false, 'message' => 'CSRF token invalid or missing']);
    exit;
}

if (empty($data)) {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['success' => false, 'message' => 'Missing request data']);
    exit;
}

try {
    // Get database connection
    $mongo = get_mongo_connection();
    $db = $mongo->selectDatabase('mmc');
    
    // Check if settings collection exists, create if not
    $collections = [];
    foreach ($db->listCollections() as $collectionInfo) {
        $collections[] = $collectionInfo->getName();
    }
    
    if (!in_array('system_settings', $collections)) {
        $db->createCollection('system_settings');
    }
    
    $collection = $db->selectCollection('system_settings');
    
    // Process session timeout setting
    if (isset($data['sessionTimeout'])) {
        $sessionTimeout = intval($data['sessionTimeout']);
        if ($sessionTimeout < 5 || $sessionTimeout > 1440) { // Between 5 minutes and 24 hours
            throw new Exception('Session timeout must be between 5 and 1440 minutes');
        }
        
        $collection->updateOne(
            ['setting_name' => 'session_timeout'],
            [
                '$set' => [
                    'value' => $sessionTimeout,
                    'updated_by' => $_SESSION['discord_user_id'],
                    'updated_at' => date('c')
                ]
            ],
            ['upsert' => true]
        );
        
        log_settings_activity(
            $_SESSION['discord_user_id'],
            $_SESSION['discord_username'],
            'security',
            "Updated session timeout to {$sessionTimeout} minutes"
        );
    }
    
    // Process login attempts setting
    if (isset($data['loginAttempts'])) {
        $loginAttempts = intval($data['loginAttempts']);
        if ($loginAttempts < 1 || $loginAttempts > 20) { // Between 1 and 20 attempts
            throw new Exception('Login attempts must be between 1 and 20');
        }
        
        $collection->updateOne(
            ['setting_name' => 'login_attempts'],
            [
                '$set' => [
                    'value' => $loginAttempts,
                    'updated_by' => $_SESSION['discord_user_id'],
                    'updated_at' => date('c')
                ]
            ],
            ['upsert' => true]
        );
        
        log_settings_activity(
            $_SESSION['discord_user_id'],
            $_SESSION['discord_username'],
            'security',
            "Updated maximum login attempts to {$loginAttempts}"
        );
    }
    
    // Process IP whitelist setting
    if (isset($data['enableIpWhitelist']) || isset($data['ipWhitelist'])) {
        $enableIpWhitelist = isset($data['enableIpWhitelist']) ? (bool)$data['enableIpWhitelist'] : false;
        $ipWhitelist = isset($data['ipWhitelist']) ? $data['ipWhitelist'] : '';
        
        // Parse IP addresses
        $ipAddresses = [];
        if (!empty($ipWhitelist)) {
            $lines = explode("\n", $ipWhitelist);
            foreach ($lines as $line) {
                $ip = trim($line);
                if (!empty($ip)) {
                    // Validate IP address format
                    if (filter_var($ip, FILTER_VALIDATE_IP)) {
                        $ipAddresses[] = $ip;
                    }
                }
            }
        }
        
        // Always include the current user's IP to prevent lockout
        $currentIp = $_SERVER['REMOTE_ADDR'];
        if (!in_array($currentIp, $ipAddresses)) {
            $ipAddresses[] = $currentIp;
        }
        
        $collection->updateOne(
            ['setting_name' => 'ip_whitelist'],
            [
                '$set' => [
                    'enabled' => $enableIpWhitelist,
                    'ips' => $ipAddresses,
                    'updated_by' => $_SESSION['discord_user_id'],
                    'updated_at' => date('c')
                ]
            ],
            ['upsert' => true]
        );
        
        log_settings_activity(
            $_SESSION['discord_user_id'],
            $_SESSION['discord_username'],
            'security',
            $enableIpWhitelist ? 
                "Enabled IP whitelist with " . count($ipAddresses) . " IP addresses" : 
                "Disabled IP whitelist"
        );
    }
    
    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'Security settings updated successfully'
    ]);
    
} catch (Exception $e) {
    secure_log("Security Settings API Error: " . $e->getMessage());
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
