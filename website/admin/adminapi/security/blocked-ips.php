<?php
require_once '../../includes/auth.php';
require_once '../../includes/config.php';

// Require authentication and developer role
require_auth(ROLE_DEVELOPER);

$pageTitle = 'Blocked IPs';

// Handle unblock request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['unblock_ip'])) {
    $ip = $_POST['unblock_ip'];
    
    // Validate IP address
    if (filter_var($ip, FILTER_VALIDATE_IP)) {
        // Remove from blocked IPs file
        $blocked_files = [
            '/etc/massacremc/security/blocked_ips.txt',
            '/var/log/security/blocked_ips.txt',
            '/tmp/blocked_ips.txt'
        ];
        
        foreach ($blocked_files as $file) {
            if (file_exists($file)) {
                $lines = file($file, FILE_IGNORE_NEW_LINES);
                $lines = array_filter($lines, function($line) use ($ip) {
                    return trim($line) !== $ip;
                });
                file_put_contents($file, implode("\n", $lines) . "\n");
                break;
            }
        }
        
        // Remove from iptables
        shell_exec("iptables -D INPUT -s {$ip} -j DROP 2>/dev/null");
        
        $success_message = "IP {$ip} has been unblocked";
    } else {
        $error_message = "Invalid IP address";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - MassacreMC Security</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .container { margin-top: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .ip-list { max-height: 400px; overflow-y: auto; }
        .ip-item { display: flex; justify-content: between; align-items: center; padding: 10px; border-bottom: 1px solid #dee2e6; }
        .ip-item:last-child { border-bottom: none; }
        .ip-address { font-family: 'Courier New', monospace; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-ban"></i> Blocked IP Addresses</h1>
            <p class="mb-0">Manage blocked IP addresses and security threats</p>
        </div>
        
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($success_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list"></i> Currently Blocked IPs</h5>
                        <button class="btn btn-primary btn-sm" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="ip-list">
                            <?php
                            $blocked_files = [
                                '/etc/massacremc/security/blocked_ips.txt',
                                '/var/log/security/blocked_ips.txt',
                                '/tmp/blocked_ips.txt'
                            ];
                            
                            $blocked_ips = [];
                            
                            foreach ($blocked_files as $file) {
                                if (file_exists($file)) {
                                    $lines = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                                    $blocked_ips = array_merge($blocked_ips, $lines);
                                    break;
                                }
                            }
                            
                            $blocked_ips = array_unique(array_filter($blocked_ips));
                            
                            if (empty($blocked_ips)): ?>
                                <div class="text-center p-4 text-muted">
                                    <i class="fas fa-shield-alt fa-3x mb-3"></i>
                                    <p>No IP addresses are currently blocked</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($blocked_ips as $ip): ?>
                                    <div class="ip-item">
                                        <div class="flex-grow-1">
                                            <span class="ip-address"><?= htmlspecialchars($ip) ?></span>
                                        </div>
                                        <div>
                                            <form method="post" style="display: inline;">
                                                <input type="hidden" name="unblock_ip" value="<?= htmlspecialchars($ip) ?>">
                                                <button type="submit" class="btn btn-outline-danger btn-sm" 
                                                        onclick="return confirm('Are you sure you want to unblock <?= htmlspecialchars($ip) ?>?')">
                                                    <i class="fas fa-unlock"></i> Unblock
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php if (!empty($blocked_ips)): ?>
                        <div class="card-footer text-muted">
                            Total blocked IPs: <?= count($blocked_ips) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> Information</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Automatic Blocking:</strong> IPs are automatically blocked when malicious activity is detected.</p>
                        <p><strong>Manual Unblocking:</strong> Use the unblock button to remove an IP from the blocklist.</p>
                        <p><strong>Whitelist:</strong> Add trusted IPs to the whitelist to prevent accidental blocking.</p>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="fas fa-tools"></i> Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-primary btn-sm mb-2 w-100" onclick="window.close()">
                            <i class="fas fa-arrow-left"></i> Back to Security Dashboard
                        </button>
                        <button class="btn btn-outline-info btn-sm mb-2 w-100" onclick="downloadBlockedIPs()">
                            <i class="fas fa-download"></i> Download List
                        </button>
                        <button class="btn btn-outline-warning btn-sm mb-2 w-100" onclick="clearAllBlocked()">
                            <i class="fas fa-trash"></i> Clear All (Dangerous)
                        </button>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <span>Blocked Today:</span>
                            <span class="badge bg-danger"><?= count(array_filter($blocked_ips, function($ip) {
                                // This is a simplified check - in production you'd track when IPs were blocked
                                return true;
                            })) ?></span>
                        </div>
                        <div class="d-flex justify-content-between mt-2">
                            <span>Total Blocked:</span>
                            <span class="badge bg-secondary"><?= count($blocked_ips) ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function downloadBlockedIPs() {
            const ips = <?= json_encode($blocked_ips) ?>;
            const content = ips.join('\n');
            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'blocked_ips_' + new Date().toISOString().slice(0, 10) + '.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
        
        function clearAllBlocked() {
            if (confirm('WARNING: This will unblock ALL IP addresses. Are you absolutely sure?')) {
                if (confirm('This action cannot be undone. Continue?')) {
                    // Create a form to clear all blocked IPs
                    const form = document.createElement('form');
                    form.method = 'post';
                    form.innerHTML = '<input type="hidden" name="clear_all" value="1">';
                    document.body.appendChild(form);
                    form.submit();
                }
            }
        }
    </script>
</body>
</html>
