<?php
require_once '../../includes/auth.php';
require_once '../../includes/config.php';

// Require authentication and developer role
require_auth(ROLE_DEVELOPER);

$pageTitle = 'Firewall Rules';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - MassacreMC Security</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .container { margin-top: 20px; }
        .firewall-rules { background: #1e1e1e; color: #f8f9fa; padding: 20px; border-radius: 8px; font-family: 'Courier New', monospace; font-size: 0.9rem; }
        .rule-line { margin-bottom: 5px; }
        .rule-accept { color: #28a745; }
        .rule-drop { color: #dc3545; }
        .rule-reject { color: #ffc107; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> Firewall Rules</h1>
            <p class="mb-0">Current iptables configuration</p>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list"></i> Active Rules</h5>
                        <button class="btn btn-primary btn-sm" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="firewall-rules">
                            <?php
                            try {
                                $output = shell_exec('iptables -L -n -v --line-numbers 2>/dev/null');
                                if ($output) {
                                    $lines = explode("\n", $output);
                                    foreach ($lines as $line) {
                                        $line = htmlspecialchars($line);
                                        $class = '';
                                        
                                        if (strpos($line, 'ACCEPT') !== false) {
                                            $class = 'rule-accept';
                                        } elseif (strpos($line, 'DROP') !== false) {
                                            $class = 'rule-drop';
                                        } elseif (strpos($line, 'REJECT') !== false) {
                                            $class = 'rule-reject';
                                        }
                                        
                                        echo "<div class='rule-line {$class}'>{$line}</div>";
                                    }
                                } else {
                                    echo "<div class='text-warning'>Unable to retrieve firewall rules. Check permissions.</div>";
                                }
                            } catch (Exception $e) {
                                echo "<div class='text-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> Legend</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-2"><span class="badge bg-success">ACCEPT</span> - Traffic allowed</div>
                        <div class="mb-2"><span class="badge bg-danger">DROP</span> - Traffic silently dropped</div>
                        <div class="mb-2"><span class="badge bg-warning">REJECT</span> - Traffic rejected with response</div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools"></i> Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-primary btn-sm mb-2 w-100" onclick="window.close()">
                            <i class="fas fa-arrow-left"></i> Back to Security Dashboard
                        </button>
                        <button class="btn btn-outline-info btn-sm mb-2 w-100" onclick="downloadRules()">
                            <i class="fas fa-download"></i> Download Rules
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function downloadRules() {
            // Create a downloadable text file with the firewall rules
            const rules = document.querySelector('.firewall-rules').innerText;
            const blob = new Blob([rules], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'firewall_rules_' + new Date().toISOString().slice(0, 10) + '.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
