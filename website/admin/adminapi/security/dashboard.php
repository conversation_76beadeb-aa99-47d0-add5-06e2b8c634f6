<?php
require_once '../../includes/auth.php';
require_once '../../includes/config.php';

// Require authentication and developer role
require_auth(ROLE_DEVELOPER);

header('Content-Type: application/json');

// Helper functions to get security metrics
function getSystemLoad() {
    if (function_exists('sys_getloadavg')) {
        $load = sys_getloadavg();
        return round($load[0], 2);
    }
    
    // Fallback: try to read from /proc/loadavg
    if (file_exists('/proc/loadavg')) {
        $loadavg = file_get_contents('/proc/loadavg');
        $load = explode(' ', $loadavg);
        return round(floatval($load[0]), 2);
    }
    
    return 'N/A';
}

function getMemoryUsage() {
    if (file_exists('/proc/meminfo')) {
        $meminfo = file_get_contents('/proc/meminfo');
        preg_match('/MemTotal:\s+(\d+)/', $meminfo, $total);
        preg_match('/MemAvailable:\s+(\d+)/', $meminfo, $available);
        
        if ($total && $available) {
            $total_mb = round($total[1] / 1024, 1);
            $available_mb = round($available[1] / 1024, 1);
            $used_mb = $total_mb - $available_mb;
            $percent = round(($used_mb / $total_mb) * 100, 1);
            return "{$used_mb}MB ({$percent}%)";
        }
    }
    
    // Fallback: PHP memory usage
    $mem = memory_get_usage(true);
    return round($mem / 1024 / 1024, 1) . ' MB';
}

function getDiskUsage() {
    $total = disk_total_space('/');
    $free = disk_free_space('/');
    
    if ($total && $free) {
        $used = $total - $free;
        $percent = round(($used / $total) * 100, 1);
        $used_gb = round($used / 1024 / 1024 / 1024, 1);
        $total_gb = round($total / 1024 / 1024 / 1024, 1);
        return "{$used_gb}GB / {$total_gb}GB ({$percent}%)";
    }
    
    return 'N/A';
}

function getUptime() {
    if (file_exists('/proc/uptime')) {
        $uptime = file_get_contents('/proc/uptime');
        $uptime_seconds = floatval(explode(' ', $uptime)[0]);
        
        $days = floor($uptime_seconds / 86400);
        $hours = floor(($uptime_seconds % 86400) / 3600);
        $minutes = floor(($uptime_seconds % 3600) / 60);
        
        if ($days > 0) {
            return "{$days}d {$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h {$minutes}m";
        } else {
            return "{$minutes}m";
        }
    }
    
    return 'N/A';
}

function getBlockedIPs() {
    $blocked_files = [
        '/etc/massacremc/security/blocked_ips.txt',
        '/var/log/security/blocked_ips.txt',
        '/tmp/blocked_ips.txt',
        '../../blocked_ips.txt'
    ];
    
    foreach ($blocked_files as $file) {
        if (file_exists($file)) {
            $lines = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            return count($lines);
        }
    }
    
    return 0;
}

function getActiveConnections() {
    $output = shell_exec('netstat -tn 2>/dev/null | grep ESTABLISHED | wc -l');
    return $output ? intval(trim($output)) : 0;
}

function getThreatsToday() {
    $threat_files = [
        '/var/log/security/threats.log',
        '/var/log/security_monitor.log',
        '/tmp/threats.log'
    ];
    
    $today = date('Y-m-d');
    $count = 0;
    
    foreach ($threat_files as $file) {
        if (file_exists($file)) {
            $lines = file($file);
            foreach ($lines as $line) {
                if (strpos($line, $today) !== false && 
                    (strpos($line, 'THREAT') !== false || strpos($line, 'BLOCKED') !== false)) {
                    $count++;
                }
            }
            break; // Use first available file
        }
    }
    
    return $count;
}

function getRecentThreats($limit = 10) {
    $threat_files = [
        '/var/log/security/threats.log',
        '/var/log/security_monitor.log',
        '/tmp/threats.log'
    ];
    
    foreach ($threat_files as $file) {
        if (file_exists($file)) {
            $lines = file($file);
            $threats = [];
            
            // Get recent threat lines
            $recent_lines = array_slice(array_reverse($lines), 0, $limit * 2);
            
            foreach ($recent_lines as $line) {
                $line = trim($line);
                if (!empty($line) && 
                    (strpos($line, 'THREAT') !== false || 
                     strpos($line, 'BLOCKED') !== false || 
                     strpos($line, 'WARNING') !== false)) {
                    $threats[] = $line;
                    if (count($threats) >= $limit) break;
                }
            }
            
            return $threats;
        }
    }
    
    return ['No recent security events found'];
}

function isSecurityMonitorActive() {
    // Check if security monitoring service is running
    $output = shell_exec('systemctl is-active massacremc-security-monitor 2>/dev/null');
    if (trim($output) === 'active') {
        return true;
    }
    
    // Check if monitoring script is running
    $output = shell_exec('pgrep -f "security_monitoring_system.sh" 2>/dev/null');
    if (!empty(trim($output))) {
        return true;
    }
    
    // Check if monitoring process exists
    $output = shell_exec('pgrep -f "security_monitor" 2>/dev/null');
    return !empty(trim($output));
}

// Collect all security data
try {
    $security_data = [
        'timestamp' => date('Y-m-d H:i:s'),
        'blocked_ips' => getBlockedIPs(),
        'active_connections' => getActiveConnections(),
        'threats_today' => getThreatsToday(),
        'cpu_load' => getSystemLoad(),
        'memory_usage' => getMemoryUsage(),
        'disk_usage' => getDiskUsage(),
        'uptime' => getUptime(),
        'security_monitor_active' => isSecurityMonitorActive(),
        'recent_threats' => getRecentThreats(10),
        'status' => 'success'
    ];
    
    echo json_encode($security_data);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Failed to collect security data',
        'message' => $e->getMessage(),
        'status' => 'error'
    ]);
}
?>
