<?php
/**
 * Maintenance Mode Reset API Endpoint
 * Forcibly removes maintenance mode setting from the database
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


if (!isset($_SESSION['discord_user_id'])) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


if (!has_role(ROLE_ADMIN)) {
    secure_log("Maintenance Reset API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['success' => false, 'message' => 'Admin privileges required']);
    exit;
}

try {
    // Get database connection
    $mongo = get_mongo_connection();
    $db = $mongo->selectDatabase('mmc');
    
    // Check if settings collection exists
    $collections = [];
    foreach ($db->listCollections() as $collectionInfo) {
        $collections[] = $collectionInfo->getName();
    }
    
    if (!in_array('system_settings', $collections)) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Maintenance mode was already disabled (collection does not exist)'
        ]);
        exit;
    }
    
    $collection = $db->selectCollection('system_settings');
    
    // Delete the maintenance mode setting
    $result = $collection->deleteOne(['setting_name' => 'maintenance_mode']);
    
    // Log the action
    log_settings_activity(
        $_SESSION['discord_user_id'],
        $_SESSION['discord_username'],
        'maintenance_mode',
        'Forcibly reset maintenance mode'
    );
    
    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'deleted_count' => $result->getDeletedCount(),
        'message' => 'Maintenance mode has been forcibly reset'
    ]);
    
} catch (Exception $e) {
    secure_log("Maintenance Reset API Error: " . $e->getMessage());
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
