<?php
/**
 * Maintenance Mode Toggle API Endpoint
 * Enables or disables maintenance mode for the admin panel
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/notifications.php';
require_once __DIR__ . '/../../includes/db_access.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


if (!isset($_SESSION['discord_user_id'])) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


if (!has_role(ROLE_ADMIN)) {
    secure_log("Maintenance Toggle API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['success' => false, 'message' => 'Admin privileges required']);
    exit;
}


if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // Get database connection
        $mongo = get_mongo_connection();
        $db = $mongo->selectDatabase('mmc');

        // Check if settings collection exists
        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('system_settings', $collections)) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'enabled' => false,
                'message' => ''
            ]);
            exit;
        }

        $collection = $db->selectCollection('system_settings');

        // Get maintenance mode setting with explicit type mapping
        $maintenance = $collection->findOne(
            ['setting_name' => 'maintenance_mode'],
            ['typeMap' => ['root' => 'array', 'document' => 'array']]
        );

        // Determine the enabled status with explicit boolean casting
        $is_enabled = false;
        if ($maintenance && isset($maintenance['enabled'])) {
            $is_enabled = (bool)$maintenance['enabled'];
        }

        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'enabled' => $is_enabled,
            'message' => $maintenance && isset($maintenance['message']) ? $maintenance['message'] : ''
        ]);
        exit;
    } catch (Exception $e) {
        secure_log("Maintenance Toggle API Error (GET): " . $e->getMessage());
        header('HTTP/1.1 500 Internal Server Error');
        echo json_encode([
            'success' => false,
            'message' => 'Server error: ' . $e->getMessage()
        ]);
        exit;
    }
}


if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('HTTP/1.1 405 Method Not Allowed');
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}


$data = json_decode(file_get_contents('php://input'), true);


if (!isset($data['enabled'])) {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['success' => false, 'message' => 'Missing required field: enabled']);
    exit;
}

$enabled = (bool)$data['enabled'];
$message = $data['message'] ?? 'The system is currently undergoing maintenance. Please try again later.';
$notifyUsers = $data['notify_users'] ?? false;

try {
    // Get database connection
    $mongo = get_mongo_connection();
    $db = $mongo->selectDatabase('mmc');

    // Check if settings collection exists, create if not
    $collections = [];
    foreach ($db->listCollections() as $collectionInfo) {
        $collections[] = $collectionInfo->getName();
    }

    if (!in_array('system_settings', $collections)) {
        $db->createCollection('system_settings');
    }

    $collection = $db->selectCollection('system_settings');

    // First, delete any existing maintenance mode setting to ensure a clean update
    $collection->deleteOne(['setting_name' => 'maintenance_mode']);

    // Now insert the new setting
    $result = $collection->insertOne([
        'setting_name' => 'maintenance_mode',
        'enabled' => $enabled,
        'message' => $message,
        'updated_by' => $_SESSION['discord_user_id'],
        'updated_at' => date('c')
    ]);

    // Consider the operation successful if we get here without errors
    $success = $result->getInsertedCount() > 0;

    // Log the actual MongoDB result for debugging
    secure_log("Maintenance toggle result - Inserted: " . $result->getInsertedCount() .
              ", Setting to: " . ($enabled ? 'enabled' : 'disabled'));

    // Double-check that the setting was correctly saved
    $verification = $collection->findOne(['setting_name' => 'maintenance_mode']);
    secure_log("Verification - Enabled value in DB: " . ($verification['enabled'] ? 'true' : 'false'));

    // Log the action
    log_settings_activity(
        $_SESSION['discord_user_id'],
        $_SESSION['discord_username'],
        'maintenance_mode',
        $enabled ? 'Enabled maintenance mode' : 'Disabled maintenance mode'
    );

    // Send notification if requested and maintenance mode is enabled
    if ($success && $enabled && $notifyUsers) {
        create_notification(
            'all',
            'maintenance',
            'Maintenance Mode Enabled',
            $message
        );
    }

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'enabled' => $enabled,
        'message' => $success ?
            ($enabled ? 'Maintenance mode enabled successfully.' : 'Maintenance mode disabled successfully.') :
            'Failed to update maintenance mode setting.'
    ]);

} catch (Exception $e) {
    secure_log("Maintenance Toggle API Error: " . $e->getMessage());
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
