<?php
/**
 * Update Staff Role API Endpoint
 * Allows admins to update a staff member's role
 */


require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/security.php';


header('Content-Type: application/json');


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


secure_log("Update Staff Role API Request - Method: " . $_SERVER['REQUEST_METHOD']);
secure_log("Update Staff Role API Request - Session ID: " . session_id());
secure_log("Update Staff Role API Request - User ID: " . ($_SESSION['discord_user_id'] ?? 'Not set'));
secure_log("Update Staff Role API Request - User Role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));


if (!is_authenticated()) {
    secure_log("Update Staff Role API - Authentication failed");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


if (!has_role(ROLE_ADMIN)) {
    secure_log("Update Staff Role API - Authorization failed - User role: " . ($_SESSION['discord_user_role'] ?? 'Unknown'));
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access - Admin role required']);
    exit;
}


if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // SECURITY: Enforce CSRF protection for role updates
    require_csrf_token(true);

    try {

        $json = file_get_contents('php://input');
        $data = json_decode($json, true);


        if (empty($data['user_id']) || empty($data['role'])) {
            secure_log("Update Staff Role API - Missing required fields");
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Missing required fields']);
            exit;
        }

        // SECURITY: Prevent self-privilege escalation (users modifying their own role)
        if ($data['user_id'] === $_SESSION['discord_user_id']) {
            log_security_event('self_privilege_escalation', "User attempted to modify their own role", [
                'attempted_role' => $data['role'],
                'current_role' => $_SESSION['discord_user_role']
            ]);
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Cannot modify your own role']);
            exit;
        }


        // SECURITY: Prevent privilege escalation - users cannot grant roles higher than their own
        $current_user_role = $_SESSION['discord_user_role'] ?? '';
        $target_role = $data['role'];

        // Define role hierarchy (higher number = higher privilege)
        $role_hierarchy = [
            ROLE_TRAINEE => 1,
            ROLE_MODERATOR => 2,
            ROLE_ADMIN => 3,
            ROLE_SUPERVISOR => 4,
            ROLE_DEVELOPER => 5,
            ROLE_OWNER => 6,
            'UNAUTHORIZED' => 0
        ];

        $current_user_level = $role_hierarchy[$current_user_role] ?? 0;
        $target_role_level = $role_hierarchy[$target_role] ?? 0;

        // Only allow granting roles equal to or lower than current user's role
        // Exception: OWNER can grant any role, but others cannot grant OWNER
        if ($current_user_role !== ROLE_OWNER && $target_role === ROLE_OWNER) {
            log_security_event('privilege_escalation', "User attempted to grant OWNER role without sufficient privileges", [
                'target_user_id' => $data['user_id'],
                'attempted_role' => $target_role,
                'current_user_role' => $current_user_role
            ]);
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Cannot grant OWNER role - insufficient privileges']);
            exit;
        }

        if ($target_role_level > $current_user_level) {
            log_security_event('privilege_escalation', "User attempted to grant role higher than their own privilege level", [
                'target_user_id' => $data['user_id'],
                'attempted_role' => $target_role,
                'attempted_level' => $target_role_level,
                'current_user_role' => $current_user_role,
                'current_user_level' => $current_user_level
            ]);
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Cannot grant role higher than your own privilege level']);
            exit;
        }

        $valid_roles = [ROLE_ADMIN, ROLE_MODERATOR, ROLE_TRAINEE, ROLE_SUPERVISOR, ROLE_DEVELOPER, ROLE_OWNER, 'UNAUTHORIZED'];
        if (!in_array($target_role, $valid_roles)) {
            secure_log("Update Staff Role API - Invalid role: " . $target_role);
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid role']);
            exit;
        }


        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            secure_log("Update Staff Role API - Database connection failed");
            throw new Exception("Failed to connect to database");
        }


        $result = $db->staff_activity->updateOne(
            ['user_id' => $data['user_id']],
            ['$set' => ['role' => $data['role']]]
        );

        if ($result->getModifiedCount() === 0 && $result->getMatchedCount() === 0) {
            secure_log("Update Staff Role API - Staff member not found: " . $data['user_id']);
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Staff member not found']);
            exit;
        }


        $staff_member = $db->staff_activity->findOne(['user_id' => $data['user_id']]);
        $staff_username = $staff_member['username'] ?? 'Unknown';


        $db->staff_activity_log->insertOne([
            'user_id' => $_SESSION['discord_user_id'],
            'username' => $_SESSION['discord_username'],
            'action' => "Updated role for $staff_username",
            'type' => 'role_update',
            'target' => $staff_username,
            'details' => "Changed role to " . $data['role'],
            'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
            'description' => "Updated staff role"
        ]);


        echo json_encode([
            'success' => true,
            'message' => "Successfully updated role for $staff_username to " . $data['role']
        ]);

    } catch (Exception $e) {

        secure_log("Update Staff Role API Error: " . $e->getMessage());
        secure_log("Stack trace: " . $e->getTraceAsString());


        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Server error: ' . $e->getMessage()
        ]);
    }
} else {

    secure_log("Update Staff Role API - Method not allowed: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
