<?php

require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';


header('Content-Type: application/json');


header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-TOKEN, X-Discord-ID, X-Discord-Username, X-Discord-Role, X-Discord-Avatar, X-Session-ID, X-Requested-With');
header('Access-Control-Allow-Credentials: true');


if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


$headers = getallheaders();


secure_log("Staff List API: Session state - User ID: " . ($_SESSION['discord_user_id'] ?? 'not set') .
          ", Username: " . ($_SESSION['discord_username'] ?? 'not set') .
          ", Role: " . ($_SESSION['discord_user_role'] ?? 'not set') .
          ", Auth time: " . ($_SESSION['auth_time'] ?? 'not set'));



if (!is_authenticated()) {
    secure_log("Staff List API: Authentication failed - User not authenticated", "warning", [
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'endpoint' => $_SERVER['REQUEST_URI'] ?? 'unknown'
    ]);
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


$userRole = get_user_role();
secure_log("Staff List API: User role check - Current role: {$userRole}, Required role: " . ROLE_ADMIN);

if (!has_role(ROLE_ADMIN)) {
    secure_log("Staff List API: User does not have admin role");
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access - Admin role required']);
    exit;
}

secure_log("Staff List API: User has admin role, proceeding with request");


refresh_session();


$headers = getallheaders();
update_staff_activity(
    $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
    $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
    $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
    $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null
);


if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {

        $connection = get_db_connection();
        $db = $connection['db'];


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('staff_activity', $collections)) {

            $db->createCollection('staff_activity');


            $now = new MongoDB\BSON\UTCDateTime(time() * 1000);
            $db->staff_activity->insertOne([
                'user_id' => $_SESSION['discord_user_id'] ?? 'unknown',
                'username' => $_SESSION['discord_username'] ?? 'Unknown User',
                'role' => $_SESSION['discord_user_role'] ?? 'STAFF',
                'avatar_hash' => $_SESSION['discord_avatar'] ?? '',
                'last_active' => $now
            ]);
        }


        $collection = $db->staff_activity;


        $cursor = $collection->find(
            [],
            [
                'sort' => ['username' => 1], // Sort by username ascending
                'projection' => [
                    'user_id' => 1,
                    'username' => 1,
                    'role' => 1,
                    'avatar_hash' => 1,
                    'last_active' => 1
                ]
            ]
        );


        $staff = [];
        foreach ($cursor as $document) {

            $staffMember = [
                'user_id' => $document['user_id'] ?? '',
                'username' => $document['username'] ?? 'Unknown',
                'role' => $document['role'] ?? 'STAFF',
                'avatar_hash' => $document['avatar_hash'] ?? '',
                'last_active' => isset($document['last_active']) ? $document['last_active']->toDateTime()->format('c') : null
            ];


            if (!empty($staffMember['avatar_hash'])) {
                $staffMember['avatar_url'] = "https://cdn.discordapp.com/avatars/{$staffMember['user_id']}/{$staffMember['avatar_hash']}.png";
            }

            $staff[] = $staffMember;
        }


        echo json_encode([
            'success' => true,
            'staff' => $staff,
            'count' => count($staff)
        ]);

    } catch (Exception $e) {

        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error: ' . $e->getMessage()
        ]);
    }
} else {

    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
