<?php
/**
 * Edit Player Note API Endpoint
 * Dedicated endpoint for editing player notes
 */


header('Content-Type: application/json');


require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/security.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/auth.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


$requestPath = $_SERVER['REQUEST_URI'];
$requestMethod = $_SERVER['REQUEST_METHOD'];
secure_log("Edit Note API Request: $requestMethod $requestPath");

try {

    if (!is_authenticated()) {
        http_response_code(401);
        echo json_encode(['error' => 'Not authenticated']);
        exit;
    }


    if (!has_role(ROLE_TRAINEE)) {
        secure_log("Edit Note API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied. You do not have the required role to edit notes.']);
        exit;
    }


    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        exit;
    }

    // CSRF token validation
    require_csrf_check(true); // true for API mode

    $json = file_get_contents('php://input');
    $data = json_decode($json, true);


    secure_log("Edit Note POST request data: " . json_encode($data));


    if (!$data || !is_array($data)) {
        secure_log("No valid JSON data in request body");
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON data']);
        exit;
    }


    if (!isset($data['note_id']) || !isset($data['content']) || !isset($data['type'])) {
        secure_log("Missing required fields in request data");
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        exit;
    }


    $note_id = sanitize_input($data['note_id']);
    $content = sanitize_input($data['content']);
    $type = sanitize_input($data['type']);
    $important = isset($data['important']) ? (bool)$data['important'] : false;


    $staff_id = $_SESSION['user_id'] ?? $_SESSION['discord_user_id'] ?? 'unknown';
    $staff_name = $_SESSION['username'] ?? $_SESSION['discord_username'] ?? 'Unknown Staff';
    $user_role = $_SESSION['discord_user_role'] ?? ROLE_UNAUTHORIZED;

    secure_log("Editing note: $note_id by staff: $staff_name ($staff_id) with role: $user_role");

    try {

        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $collections = [];
        foreach ($db->listCollections() as $collection) {
            $collections[] = $collection->getName();
        }

        if (!in_array('player_notes', $collections)) {
            secure_log("player_notes collection does not exist");
            http_response_code(404);
            echo json_encode(['error' => 'Notes collection not found']);
            exit;
        }


        $note = $db->player_notes->findOne(['note_id' => $note_id]);

        if (!$note) {
            secure_log("Note not found: $note_id");
            http_response_code(404);
            echo json_encode(['error' => 'Note not found']);
            exit;
        }



        $is_creator = ($note['staff_id'] === $staff_id);
        $is_developer_or_higher = has_role(ROLE_DEVELOPER) || has_role(ROLE_SUPERVISOR) || has_role(ROLE_OWNER);

        if (!$is_creator && !$is_developer_or_higher) {
            secure_log("Permission denied: User $staff_id (role: $user_role) cannot edit note $note_id created by {$note['staff_id']}");
            http_response_code(403);
            echo json_encode(['error' => 'You do not have permission to edit this note. Only the creator or developers+ can edit notes.']);
            exit;
        }


        $result = $db->player_notes->updateOne(
            ['note_id' => $note_id],
            ['$set' => [
                'content' => $content,
                'type' => $type,
                'important' => (bool)$important,
                'updated_at' => new MongoDB\BSON\UTCDateTime(time() * 1000),
                'updated_by' => $staff_name,
                'updated_by_id' => $staff_id
            ]]
        );

        secure_log("Update result: " . json_encode([
            'matchedCount' => $result->getMatchedCount(),
            'modifiedCount' => $result->getModifiedCount(),
            'acknowledged' => $result->isAcknowledged()
        ]));

        if ($result->getMatchedCount() === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Note not found']);
            exit;
        }

        if ($result->getModifiedCount() === 0) {

            echo json_encode([
                'success' => true,
                'message' => 'No changes made to note',
                'note_id' => $note_id
            ]);
            exit;
        }


        echo json_encode([
            'success' => true,
            'message' => 'Note updated successfully',
            'note_id' => $note_id
        ]);

    } catch (Exception $e) {
        secure_log("Error editing note: " . $e->getMessage());
        secure_log("Stack trace: " . $e->getTraceAsString());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to edit note: ' . $e->getMessage()]);
    }

} catch (Exception $e) {

    secure_log("Edit Note API Error: " . $e->getMessage());
    secure_log("Stack trace: " . $e->getTraceAsString());


    http_response_code(500);
    echo json_encode(['error' => 'An internal server error occurred']);
}
?>
