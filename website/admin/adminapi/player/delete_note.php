<?php
/**
 * Delete Player Note API Endpoint
 * Dedicated endpoint for deleting player notes
 */


header('Content-Type: application/json');


require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/security.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/auth.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


$requestPath = $_SERVER['REQUEST_URI'];
$requestMethod = $_SERVER['REQUEST_METHOD'];
secure_log("Delete Note API Request: $requestMethod $requestPath");

try {

    if (!is_authenticated()) {
        http_response_code(401);
        echo json_encode(['error' => 'Not authenticated']);
        exit;
    }


    if (!has_role(ROLE_TRAINEE)) {
        secure_log("Delete Note API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied. You do not have the required role to delete notes.']);
        exit;
    }


    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        exit;
    }

    // CSRF token validation with debugging
    $headers = getallheaders();
    $csrf_token = get_csrf_token_from_request();

    require_csrf_check(true); // true for API mode

    $json = file_get_contents('php://input');
    $data = json_decode($json, true);


    secure_log("Delete Note POST request data: " . json_encode($data));


    if (!$data || !is_array($data)) {
        secure_log("No valid JSON data in request body");
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON data']);
        exit;
    }


    if (!isset($data['note_id'])) {
        secure_log("Missing note_id in request data");
        http_response_code(400);
        echo json_encode(['error' => 'Missing note_id']);
        exit;
    }


    $note_id = sanitize_input($data['note_id']);


    $staff_id = $_SESSION['user_id'] ?? $_SESSION['discord_user_id'] ?? 'unknown';
    $staff_name = $_SESSION['username'] ?? $_SESSION['discord_username'] ?? 'Unknown Staff';
    $user_role = $_SESSION['discord_user_role'] ?? ROLE_UNAUTHORIZED;

    try {

        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }



        $filter = ['note_id' => $note_id];

        // Only allow users with DEVELOPER role or higher to delete any note
        // Lower roles can only delete their own notes
        if (!has_role(ROLE_DEVELOPER) && !has_role(ROLE_SUPERVISOR) && !has_role(ROLE_OWNER)) {
            $filter['staff_id'] = $staff_id;
        }


        $result = $db->player_notes->findOneAndDelete($filter);


        if (!$result) {

            $note = $db->player_notes->findOne(['note_id' => $note_id]);

            if ($note) {

                http_response_code(403);
                echo json_encode(['error' => 'You do not have permission to delete this note']);
            } else {

                http_response_code(404);
                echo json_encode(['error' => 'Note not found']);
            }
            exit;
        }


        $player_name = $result['username'] ?? 'Unknown Player';
        $note_content = $result['content'] ?? '';
        $is_important = $result['important'] ?? false;


        try {

            log_player_note_activity(
                $staff_id,
                $staff_name,
                $player_name,
                'delete',
                $note_content,
                $is_important
            );

            secure_log("Successfully logged note deletion activity for staff: $staff_name ($staff_id)");
        } catch (Exception $e) {
            secure_log("Error logging note deletion activity: " . $e->getMessage());

        }


        echo json_encode([
            'success' => true,
            'message' => 'Note deleted successfully',
            'note_id' => $note_id
        ]);

    } catch (Exception $e) {
        secure_log("Error deleting note: " . $e->getMessage());
        secure_log("Stack trace: " . $e->getTraceAsString());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete note: ' . $e->getMessage()]);
    }

} catch (Exception $e) {

    secure_log("Delete Note API Error: " . $e->getMessage());
    secure_log("Stack trace: " . $e->getTraceAsString());


    http_response_code(500);
    echo json_encode(['error' => 'An internal server error occurred']);
}
?>
