<?php
/**
 * Player Notes API Endpoint
 * Handles saving and retrieving player notes
 *
 * Endpoints:
 * - GET /adminapi/player/notes.php - Get notes for a player
 * - POST /adminapi/player/notes.php - Add a new note
 */


header('Content-Type: application/json');


require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/security.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/auth.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


apply_rate_limit($_SERVER['REMOTE_ADDR'], 60, 60); // 60 requests per minute


$requestPath = $_SERVER['REQUEST_URI'];
$requestMethod = $_SERVER['REQUEST_METHOD'];
secure_log("Player Notes API Request: $requestMethod $requestPath");

try {

    // Enforce proper authentication - NO BYPASSES ALLOWED
    if (!is_authenticated()) {
        secure_log("Player Notes API: Authentication failed - User not authenticated", "warning", [
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'endpoint' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ]);
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        exit;
    }


    if (!has_role(ROLE_TRAINEE)) {
        secure_log("Player Notes API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied. You do not have the required role to access this resource.']);
        exit;
    }


    $headers = getallheaders();
    update_staff_activity(
        $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
        $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
        $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
        $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null
    );


    $connection = get_db_connection();
    $db = $connection['db'];

    if (!$db) {
        throw new Exception("Database connection failed");
    }


    if ($_SERVER['REQUEST_METHOD'] === 'GET') {

        $username = isset($_GET['username']) ? sanitize_input($_GET['username']) : null;
        $xuid = isset($_GET['xuid']) ? sanitize_input($_GET['xuid']) : null;

        secure_log("GET request parameters: username=" . ($username ?? 'null') . ", xuid=" . ($xuid ?? 'null'));


        if (empty($username) && empty($xuid)) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing username or xuid parameter']);
            exit;
        }


        $notes = get_player_notes($username, $xuid);
        secure_log("Retrieved " . count($notes) . " notes for player");


        echo json_encode($notes);
    }

    else if ($_SERVER['REQUEST_METHOD'] === 'POST') {

        $json = file_get_contents('php://input');
        $data = json_decode($json, true);


        secure_log("POST request data: " . json_encode($data));
        secure_log("POST request URL parameters: " . json_encode($_GET));


        if (!$data || !is_array($data)) {
            secure_log("No valid JSON data in request body");
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON data']);
            exit;
        }


        if (!isset($data['username']) && isset($_GET['username'])) {
            $data['username'] = $_GET['username'];
            secure_log("Using username from URL parameter: " . $data['username']);
        }

        if (!isset($data['xuid']) && isset($_GET['xuid'])) {
            $data['xuid'] = $_GET['xuid'];
            secure_log("Using xuid from URL parameter: " . $data['xuid']);
        }


        if (!isset($data['xuid']) || !isset($data['username']) || !isset($data['content']) || !isset($data['type'])) {
            secure_log("Missing required fields in request data");
            http_response_code(400);
            echo json_encode(['error' => 'Missing required fields']);
            exit;
        }


        $xuid = sanitize_input($data['xuid']);
        $username = sanitize_input($data['username']);
        $content = sanitize_input($data['content']);
        $type = sanitize_input($data['type']);
        $important = isset($data['important']) ? (bool)$data['important'] : false;


        $staff_id = $_SESSION['user_id'] ?? $headers['X-Discord-ID'] ?? 'unknown';
        $staff_name = $_SESSION['username'] ?? $headers['X-Discord-Username'] ?? 'Unknown Staff';

        secure_log("Adding note for player: $username ($xuid) by staff: $staff_name ($staff_id)");


        $result = add_player_note($xuid, $username, $content, $type, $important, $staff_id, $staff_name);

        if ($result['success']) {
            secure_log("Successfully added note: " . json_encode($result));
            echo json_encode([
                'success' => true,
                'message' => $result['message'],
                'note_id' => $result['note_id'] ?? null
            ]);
        } else {
            secure_log("Failed to add note: " . json_encode($result));
            http_response_code(500);
            echo json_encode(['error' => $result['message']]);
        }
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {

    secure_log("Player Notes API Error: " . $e->getMessage());
    secure_log("Stack trace: " . $e->getTraceAsString());


    http_response_code(500);
    echo json_encode(['error' => 'An internal server error occurred']);
}
?>
