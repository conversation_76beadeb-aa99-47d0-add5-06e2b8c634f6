<?php
/**
 * Search API Endpoint
 * Handles player and faction searches using the modern search API
 */


header('Content-Type: application/json');


require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../../includes/core.php';
require_once __DIR__ . '/../../includes/api_client.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


apply_rate_limit($_SERVER['REMOTE_ADDR'], 60, 60); // 60 requests per minute

try {



    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? null;
        if (!$token || !verify_csrf_token($token)) {
            secure_log("Search API: CSRF token validation failed");
            http_response_code(403);
            echo json_encode(['error' => 'CSRF token invalid or missing']);
            exit;
        }
    }


    // Enforce proper authentication - NO BYPASSES ALLOWED
    if (!is_authenticated()) {
        secure_log("Search API: Authentication failed - User not authenticated", "warning", [
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'endpoint' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ]);
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        exit;
    }


    if (!has_role(ROLE_TRAINEE)) {
        secure_log("Search API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied. You do not have the required role to access search functionality.']);
        exit;
    }


    refresh_session();


    $headers = getallheaders();
    update_staff_activity(
        $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
        $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
        $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
        $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null
    );


    if ($_SERVER['REQUEST_METHOD'] === 'GET') {

        $query = isset($_GET['query']) ? sanitize_input($_GET['query']) : null;
        $type = isset($_GET['type']) ? sanitize_input($_GET['type']) : 'player';
        // Partial search removed - only use full username search
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;


        if (empty($query)) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing search query']);
            exit;
        }

        if (!in_array($type, ['player', 'faction'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid search type']);
            exit;
        }


        // Check if we should bypass cache (for fresh data after faction deletions)
        $bypassCache = isset($_GET['fresh']) && $_GET['fresh'] === '1';
        $cacheKey = 'admin_search_' . md5($query . '_' . $type . '_full_' . $limit);

        if ($bypassCache) {
            // Clear this specific cache entry
            if (function_exists('apcu_delete')) {
                apcu_delete($cacheKey);
            }
            secure_log("Admin Search: Cache bypassed for query: {$query}");
        }

        // Force bypass cache for faction searches temporarily to debug
        if ($type === 'faction') {
            if (function_exists('apcu_delete')) {
                apcu_delete($cacheKey);
            }
            secure_log("Admin Search: Forcing cache bypass for faction search: {$query}");
        }

        // Simple, working search system
        secure_log("Admin Search: Starting {$type} search for '{$query}'");

        if ($type === 'player') {
            // Only use full username search - no partial search

            // PLAYER SEARCH - Only use API with full username (no partial search)
            try {
                $apiClient = new ApiClient();
                $player_data = $apiClient->searchPlayer($query);

                if (!isset($player_data['error']) && !empty($player_data)) {
                    secure_log("Admin Search: Found player '{$query}' via API");
                    echo json_encode([
                        'player_data' => $player_data,
                        'player_name' => $player_data['Username'] ?? $query,
                        'player_uuid' => $player_data['UUID'] ?? '',
                        'source' => 'api'
                    ]);
                    exit;
                }
            } catch (Exception $e) {
                secure_log("Admin Search: API error for player '{$query}': " . $e->getMessage());
            }

            // Player not found
            secure_log("Admin Search: Player '{$query}' not found");
            http_response_code(404);
            echo json_encode(['error' => 'Player not found']);
            exit;

        } elseif ($type === 'faction') {
            // Only use full faction name search - no partial search

            // FACTION SEARCH - Only use API with full faction name (no partial search)
            try {
                $apiClient = new ApiClient();
                $faction_data = $apiClient->getFactionData($query);

                if (!isset($faction_data['error']) && !empty($faction_data)) {
                    secure_log("Admin Search: Found faction '{$query}' via API");
                    echo json_encode([
                        'faction_data' => $faction_data,
                        'faction_name' => $faction_data['Name'] ?? $query,
                        'source' => 'api'
                    ]);
                    exit;
                }
            } catch (Exception $e) {
                secure_log("Admin Search: API error for faction '{$query}': " . $e->getMessage());
            }

            // Faction not found
            secure_log("Admin Search: Faction '{$query}' not found");
            http_response_code(404);
            echo json_encode(['error' => 'Faction not found']);
            exit;
        }

        // Invalid search type
        http_response_code(400);
        echo json_encode(['error' => 'Invalid search type']);
        exit;


    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    // Log the error securely without exposing sensitive information
    secure_log("Search API Error", "error", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);

    // Always return a generic error message in production
    http_response_code(500);
    echo json_encode([
        'error' => 'An internal server error occurred',
        'status' => 'error'
    ]);
}
?>