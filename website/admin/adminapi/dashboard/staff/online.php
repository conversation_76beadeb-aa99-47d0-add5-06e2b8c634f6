<?php

header('Content-Type: application/json');
require_once __DIR__ . '/../../../includes/db_access.php';
require_once __DIR__ . '/../../../includes/auth.php';


if (!is_authenticated()) {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}


if (!has_role(ROLE_TRAINEE)) {
    secure_log("Online Staff API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    http_response_code(403);
    echo json_encode(['error' => 'Permission denied. You do not have the required role to access online staff information.']);
    exit;
}


$headers = getallheaders();
update_staff_activity(
    $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
    $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
    $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
    $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null
);

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $online_staff = get_online_staff();
    echo json_encode($online_staff);
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}
?>