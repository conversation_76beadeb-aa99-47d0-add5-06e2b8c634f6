<?php
/**
 * Clear cache endpoint for admin operations
 * Used after faction deletions or other operations that require fresh data
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../../includes/db_optimization.php';

header('Content-Type: application/json');

try {
    // Check authentication
    if (!is_authenticated()) {
        http_response_code(401);
        echo json_encode(['error' => 'Not authenticated']);
        exit;
    }

    // Check admin level
    if (!isset($_SESSION['admin_level']) || $_SESSION['admin_level'] < 1) {
        http_response_code(403);
        echo json_encode(['error' => 'Insufficient permissions']);
        exit;
    }

    // Verify CSRF token for cache clearing operations
    $token = $_POST['csrf_token'] ??
             $_SERVER['HTTP_X_CSRF_TOKEN'] ??
             getallheaders()['X-CSRF-TOKEN'] ??
             null;

    if (!$token || !verify_csrf_token($token)) {
        secure_log("Clear Cache: CSRF token validation failed");
        http_response_code(403);
        echo json_encode(['error' => 'CSRF token invalid or missing']);
        exit;
    }

    // Get parameters
    $type = $_GET['type'] ?? 'all';
    $query = $_GET['query'] ?? '';

    $cleared = [];

    switch ($type) {
        case 'search':
            if (!empty($query)) {
                // Clear specific search cache
                $cacheKey = 'admin_search_' . md5($query . '_player');
                if (function_exists('apcu_delete')) {
                    apcu_delete($cacheKey);
                    $cleared[] = "search_player_{$query}";
                }
                
                $cacheKey = 'admin_search_' . md5($query . '_faction');
                if (function_exists('apcu_delete')) {
                    apcu_delete($cacheKey);
                    $cleared[] = "search_faction_{$query}";
                }
            }
            break;

        case 'all':
            // Clear all cache
            if (function_exists('apcu_clear_cache')) {
                apcu_clear_cache();
                $cleared[] = 'apcu_cache';
            }
            
            // Clear session cache
            if (isset($_SESSION['query_cache'])) {
                $_SESSION['query_cache'] = [];
                $cleared[] = 'session_cache';
            }
            break;

        case 'faction':
            // Clear faction-related caches
            if (function_exists('apcu_clear_cache')) {
                // For now, clear all cache since we don't have faction-specific keys
                apcu_clear_cache();
                $cleared[] = 'faction_cache';
            }
            break;

        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid cache type']);
            exit;
    }

    secure_log("Admin Cache: Cache cleared by " . ($_SESSION['discord_username'] ?? 'Unknown') . " - Type: {$type}, Query: {$query}");

    echo json_encode([
        'success' => true,
        'message' => 'Cache cleared successfully',
        'cleared' => $cleared,
        'type' => $type,
        'query' => $query
    ]);

} catch (Exception $e) {
    secure_log("Admin Cache: Error clearing cache - " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Failed to clear cache: ' . $e->getMessage()]);
}
?>
