<?php
/**
 * Health Monitor Setup Script
 * 
 * Sets up automatic health monitoring with Discord notifications
 * Run this once to configure the system
 */

require_once __DIR__ . '/../includes/auth.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!is_authenticated()) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => 'Authentication required']);
    exit;
}

if (!has_role(ROLE_DEVELOPER)) {
    http_response_code(403);
    echo json_encode(['status' => 'error', 'message' => 'Developer role required']);
    exit;
}

header('Content-Type: application/json');

$action = $_GET['action'] ?? 'status';

switch ($action) {
    case 'status':
        checkMonitorStatus();
        break;

    case 'start':
        startHealthMonitor();
        break;

    case 'stop':
        stopHealthMonitor();
        break;

    case 'test':
        testHealthMonitor();
        break;

    case 'run':
        runHealthMonitorCheck();
        break;

    default:
        http_response_code(400);
        echo json_encode(['status' => 'error', 'message' => 'Invalid action']);
        break;
}

function checkMonitorStatus() {
    try {
        // Get database connection
        require_once __DIR__ . '/../includes/db_access.php';
        $mongo = get_mongo_connection();
        $db = $mongo->selectDatabase('mmc');

        // Check if health monitor is enabled in database
        $collection = $db->selectCollection('system_settings');
        $healthMonitorSetting = $collection->findOne(['setting_name' => 'health_monitor']);
        $isEnabled = $healthMonitorSetting ? (bool)($healthMonitorSetting['enabled'] ?? false) : false;

        // Discord webhook functionality has been removed
        $webhookConfigured = false;

        // Check file-based status for compatibility
        $lockFile = '/tmp/massacremc_health_monitor.lock';
        $lastRunFile = '/tmp/massacremc_last_health_run.txt';

        $isRunning = file_exists($lockFile);
        $lastRun = file_exists($lastRunFile) ? intval(file_get_contents($lastRunFile)) : 0;

        $status = [
            'monitor_enabled' => $isEnabled,
            'monitor_running' => $isRunning,
            'webhook_configured' => $webhookConfigured,
            'last_run' => $lastRun > 0 ? date('Y-m-d H:i:s', $lastRun) : 'Never',
            'next_run' => $lastRun > 0 ? date('Y-m-d H:i:s', $lastRun + 300) : 'Unknown',
            'time_until_next' => $lastRun > 0 ? max(0, ($lastRun + 300) - time()) : 0
        ];

        echo json_encode([
            'status' => 'success',
            'monitor_status' => $status
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Failed to check monitor status: ' . $e->getMessage()
        ]);
    }
}

function startHealthMonitor() {
    try {
        // Get database connection
        require_once __DIR__ . '/../includes/db_access.php';
        $mongo = get_mongo_connection();
        $db = $mongo->selectDatabase('mmc');

        // Save health monitor enabled state to database
        $collection = $db->selectCollection('system_settings');
        $collection->updateOne(
            ['setting_name' => 'health_monitor'],
            [
                '$set' => [
                    'enabled' => true,
                    'updated_by' => $_SESSION['discord_user_id'] ?? 'system',
                    'updated_at' => date('c')
                ]
            ],
            ['upsert' => true]
        );

        // Log the action
        if (function_exists('log_settings_activity')) {
            log_settings_activity(
                $_SESSION['discord_user_id'] ?? 'system',
                $_SESSION['discord_username'] ?? 'System',
                'health_monitor',
                'Enabled automatic health monitoring'
            );
        }

        echo json_encode([
            'status' => 'success',
            'message' => 'Health monitor enabled and will run automatically every 5 minutes',
            'instructions' => 'The monitor state has been saved and will persist across page refreshes'
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Failed to start health monitor: ' . $e->getMessage()
        ]);
    }
}

function stopHealthMonitor() {
    try {
        // Get database connection
        require_once __DIR__ . '/../includes/db_access.php';
        $mongo = get_mongo_connection();
        $db = $mongo->selectDatabase('mmc');

        // Save health monitor disabled state to database
        $collection = $db->selectCollection('system_settings');
        $collection->updateOne(
            ['setting_name' => 'health_monitor'],
            [
                '$set' => [
                    'enabled' => false,
                    'updated_by' => $_SESSION['discord_user_id'] ?? 'system',
                    'updated_at' => date('c')
                ]
            ],
            ['upsert' => true]
        );

        // Clean up lock file
        $lockFile = '/tmp/massacremc_health_monitor.lock';
        if (file_exists($lockFile)) {
            unlink($lockFile);
        }

        // Log the action
        if (function_exists('log_settings_activity')) {
            log_settings_activity(
                $_SESSION['discord_user_id'] ?? 'system',
                $_SESSION['discord_username'] ?? 'System',
                'health_monitor',
                'Disabled automatic health monitoring'
            );
        }

        echo json_encode([
            'status' => 'success',
            'message' => 'Health monitor disabled and state saved'
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Failed to stop health monitor: ' . $e->getMessage()
        ]);
    }
}

function testHealthMonitor() {
    try {
        // Run the health monitor script directly
        $output = [];
        $returnCode = 0;
        
        $command = 'php ' . escapeshellarg(__DIR__ . '/health-monitor.php') . ' 2>&1';
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0) {
            echo json_encode([
                'status' => 'success',
                'message' => 'Health monitor test completed successfully',
                'output' => implode("\n", $output)
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'Health monitor test failed',
                'output' => implode("\n", $output),
                'return_code' => $returnCode
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Error running health monitor test: ' . $e->getMessage()
        ]);
    }
}

function runHealthMonitorCheck() {
    try {
        // Check if health monitor is enabled
        require_once __DIR__ . '/../includes/db_access.php';
        $mongo = get_mongo_connection();
        $db = $mongo->selectDatabase('mmc');
        $collection = $db->selectCollection('system_settings');
        $healthMonitorSetting = $collection->findOne(['setting_name' => 'health_monitor']);
        $isEnabled = $healthMonitorSetting ? (bool)($healthMonitorSetting['enabled'] ?? false) : false;

        if (!$isEnabled) {
            echo json_encode([
                'status' => 'skipped',
                'message' => 'Health monitor is disabled'
            ]);
            return;
        }

        // Check if enough time has passed since last run
        $lastRunFile = '/tmp/massacremc_last_health_run.txt';
        $lastRun = file_exists($lastRunFile) ? intval(file_get_contents($lastRunFile)) : 0;
        $currentTime = time();

        if ($currentTime - $lastRun < 300) { // 5 minutes
            echo json_encode([
                'status' => 'skipped',
                'message' => 'Too soon since last run',
                'time_until_next' => 300 - ($currentTime - $lastRun)
            ]);
            return;
        }

        // Run the health monitor script
        $output = [];
        $returnCode = 0;

        $command = 'php ' . escapeshellarg(__DIR__ . '/health-monitor.php') . ' 2>&1';
        exec($command, $output, $returnCode);

        if ($returnCode === 0) {
            echo json_encode([
                'status' => 'success',
                'message' => 'Health monitor check completed successfully',
                'output' => implode("\n", $output),
                'next_run' => date('Y-m-d H:i:s', $currentTime + 300)
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'Health monitor check failed',
                'output' => implode("\n", $output),
                'return_code' => $returnCode
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Error running health monitor check: ' . $e->getMessage()
        ]);
    }
}

function getWebhookFilePath() {
    $possiblePaths = [
        '/tmp/massacremc_discord_webhook.txt',
        sys_get_temp_dir() . '/massacremc_discord_webhook.txt',
        __DIR__ . '/discord_webhook.txt'
    ];

    foreach ($possiblePaths as $path) {
        $dir = dirname($path);
        if (is_writable($dir)) {
            return $path;
        }
    }

    return __DIR__ . '/discord_webhook.txt';
}

?>
