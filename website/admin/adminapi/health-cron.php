<?php
/**
 * Health Monitor Cron Endpoint
 * 
 * This endpoint can be called by a cron job or external monitoring service
 * to ensure health checks run every 5 minutes even when the admin panel is closed.
 * Note: Discord notifications have been removed from the health monitor system.
 * 
 * Usage: curl -X GET https://staff.massacremc.net/adminapi/health-cron.php
 */


if (!isset($_SERVER['HTTP_USER_AGENT']) || 
    (strpos($_SERVER['HTTP_USER_AGENT'], 'curl') === false && 
     strpos($_SERVER['HTTP_USER_AGENT'], 'wget') === false &&
     strpos($_SERVER['HTTP_USER_AGENT'], 'cron') === false &&
     !isset($_SERVER['HTTP_X_REQUESTED_WITH']))) {
    http_response_code(403);
    die('Access denied. This endpoint is for automated systems only.');
}


header('Content-Type: application/json');

try {
    // Load configuration
    require_once __DIR__ . '/../includes/config.php';
    require_once __DIR__ . '/../includes/db_access.php';
    
    // Check if health monitor is enabled in database
    $mongo = get_mongo_connection();
    $db = $mongo->selectDatabase('mmc');
    $collection = $db->selectCollection('system_settings');
    $healthMonitorSetting = $collection->findOne(['setting_name' => 'health_monitor']);
    $isEnabled = $healthMonitorSetting ? (bool)($healthMonitorSetting['enabled'] ?? false) : false;
    
    if (!$isEnabled) {
        echo json_encode([
            'status' => 'disabled',
            'message' => 'Health monitor is disabled in database settings',
            'timestamp' => date('c')
        ]);
        exit;
    }
    
    // Check if enough time has passed since last run
    $lastRunFile = '/tmp/massacremc_last_health_run.txt';
    $lastRun = file_exists($lastRunFile) ? intval(file_get_contents($lastRunFile)) : 0;
    $currentTime = time();
    $timeSinceLastRun = $currentTime - $lastRun;
    
    if ($timeSinceLastRun < 300) { // 5 minutes
        echo json_encode([
            'status' => 'skipped',
            'message' => 'Too soon since last run',
            'time_since_last_run' => $timeSinceLastRun,
            'time_until_next' => 300 - $timeSinceLastRun,
            'timestamp' => date('c')
        ]);
        exit;
    }
    
    // Run the health monitor
    $output = [];
    $returnCode = 0;
    
    $command = 'php ' . escapeshellarg(__DIR__ . '/health-monitor.php') . ' 2>&1';
    exec($command, $output, $returnCode);
    
    if ($returnCode === 0) {
        echo json_encode([
            'status' => 'success',
            'message' => 'Health monitor executed successfully',
            'output' => implode("\n", $output),
            'next_run' => date('Y-m-d H:i:s', $currentTime + 300),
            'timestamp' => date('c')
        ]);
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => 'Health monitor execution failed',
            'output' => implode("\n", $output),
            'return_code' => $returnCode,
            'timestamp' => date('c')
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Health monitor cron error: ' . $e->getMessage(),
        'timestamp' => date('c')
    ]);
}
?>
