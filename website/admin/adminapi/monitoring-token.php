<?php
/**
 * Monitoring Token Management API
 * 
 * Allows developers and owners to view and regenerate monitoring tokens
 * Restricted access to high-level staff only
 */

require_once __DIR__ . '/../includes/auth.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: no-referrer');
header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
header('Cache-Control: no-cache, no-store, must-revalidate, private');
header('Pragma: no-cache');
header('Expires: 0');


if (!is_authenticated()) {
    http_response_code(401);
    echo json_encode([
        'status' => 'error',
        'message' => 'Authentication required',
        'debug' => [
            'session_status' => session_status(),
            'session_id' => session_id(),
            'has_discord_user_id' => isset($_SESSION['discord_user_id']),
            'auth_time' => $_SESSION['auth_time'] ?? 'not_set'
        ]
    ]);
    exit;
}


if (!has_role(ROLE_DEVELOPER)) {
    $user_role = $_SESSION['discord_user_role'] ?? 'UNKNOWN';
    http_response_code(403);
    echo json_encode([
        'status' => 'error',
        'message' => 'Access denied. Developer or Owner role required.',
        'user_role' => $user_role,
        'required_role' => 'DEVELOPER or higher'
    ]);
    exit;
}

$user_role = $_SESSION['discord_user_role'] ?? 'UNKNOWN';


function getTokenFilePath() {
    // Use a writable directory - try multiple options
    $possiblePaths = [
        '/tmp/massacremc_monitoring_token.txt',
        sys_get_temp_dir() . '/massacremc_monitoring_token.txt',
        __DIR__ . '/monitoring_token.txt'
    ];

    foreach ($possiblePaths as $path) {
        $dir = dirname($path);
        if (is_writable($dir)) {
            return $path;
        }
    }

    // Fallback to current directory
    return __DIR__ . '/monitoring_token.txt';
}

function getCurrentToken() {
    $tokenFile = getTokenFilePath();
    
    if (file_exists($tokenFile) && is_readable($tokenFile)) {
        $token = trim(file_get_contents($tokenFile));
        if (!empty($token) && strlen($token) >= 64) {
            return $token;
        }
    }
    
    return null;
}

function generateNewToken() {
    $tokenFile = getTokenFilePath();

    // Verify we can write to the token file location
    $dataDir = dirname($tokenFile);
    if (!is_writable($dataDir)) {
        error_log("Cannot write to monitoring token directory: $dataDir");
        throw new Exception("Token storage location is not writable");
    }
    
    // Generate cryptographically secure token
    $token = bin2hex(random_bytes(32)) . '_' . hash('sha256', 
        ($_SERVER['HTTP_HOST'] ?? 'localhost') . 
        microtime(true) . 
        random_int(100000, 999999) .
        ($_SESSION['username'] ?? 'system')
    );
    
    // Save token to file with restricted permissions
    $result = file_put_contents($tokenFile, $token, LOCK_EX);
    if ($result === false) {
        error_log("Failed to write monitoring token to file: $tokenFile");
        throw new Exception("Failed to save monitoring token");
    }

    if (file_exists($tokenFile)) {
        chmod($tokenFile, 0600);
    } else {
        error_log("Monitoring token file does not exist after write: $tokenFile");
        throw new Exception("Token file creation failed");
    }

    // Log token regeneration
    $currentUserRole = $_SESSION['discord_user_role'] ?? 'unknown';
    error_log("Monitoring token regenerated by user: " . ($_SESSION['discord_username'] ?? 'unknown') . " (Role: $currentUserRole)");
    
    return $token;
}

function getTokenInfo($token) {
    if (empty($token)) {
        return null;
    }
    
    $parts = explode('_', $token);
    $created = filemtime(getTokenFilePath());
    
    return [
        'length' => strlen($token),
        'created' => $created ? date('Y-m-d H:i:s', $created) : 'Unknown',
        'created_timestamp' => $created,
        'prefix' => substr($token, 0, 16) . '...',
        'suffix' => '...' . substr($token, -8),
        'is_secure' => strlen($token) >= 64
    ];
}


$action = $_GET['action'] ?? $_POST['action'] ?? 'get';

switch ($action) {
    case 'get':
        // Get current token information
        $currentToken = getCurrentToken();
        
        if ($currentToken) {
            echo json_encode([
                'status' => 'success',
                'token' => $currentToken,
                'token_info' => getTokenInfo($currentToken),
                'endpoint_url' => 'https://' . $_SERVER['HTTP_HOST'] . '/adminapi/health-check.php'
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'No token found. Generate a new one.',
                'endpoint_url' => 'https://' . $_SERVER['HTTP_HOST'] . '/adminapi/health-check.php'
            ]);
        }
        break;
        
    case 'regenerate':
        // Only allow POST for regeneration
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['status' => 'error', 'message' => 'POST method required for regeneration']);
            exit;
        }
        
        // CSRF protection
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'] ?? '', $_POST['csrf_token'])) {
            http_response_code(403);
            echo json_encode(['status' => 'error', 'message' => 'Invalid CSRF token']);
            exit;
        }
        
        $newToken = generateNewToken();
        
        echo json_encode([
            'status' => 'success',
            'message' => 'New monitoring token generated successfully',
            'token' => $newToken,
            'token_info' => getTokenInfo($newToken),
            'endpoint_url' => 'https://' . $_SERVER['HTTP_HOST'] . '/adminapi/health-check.php',
            'full_url' => 'https://' . $_SERVER['HTTP_HOST'] . '/adminapi/health-check.php?token=' . urlencode($newToken)
        ]);
        break;
        
    case 'test':
        // Test the current token
        $currentToken = getCurrentToken();
        
        if (!$currentToken) {
            echo json_encode([
                'status' => 'error',
                'message' => 'No token available to test'
            ]);
            exit;
        }
        
        // Make internal request to health check
        $testUrl = 'https://' . $_SERVER['HTTP_HOST'] . '/adminapi/health-check.php?token=' . urlencode($currentToken);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10,
                'user_agent' => 'MassacreMC-Internal-Test/1.0'
            ]
        ]);
        
        $response = @file_get_contents($testUrl, false, $context);
        $httpCode = 200;
        
        if ($response === false) {
            $httpCode = 500;
            $response = json_encode(['status' => 'error', 'message' => 'Request failed']);
        }
        
        echo json_encode([
            'status' => 'success',
            'test_result' => [
                'http_code' => $httpCode,
                'response' => json_decode($response, true),
                'test_url' => $testUrl,
                'timestamp' => date('c')
            ]
        ]);
        break;

    case 'webhook':
        // Webhook management moved to environment configuration
        echo json_encode([
            'status' => 'error',
            'message' => 'Webhook management is now handled via environment variables'
        ]);
        break;

    default:
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => 'Invalid action. Supported: get, regenerate, test'
        ]);
        break;
}




?>
