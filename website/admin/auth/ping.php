<?php
require_once __DIR__ . '/../includes/security.php';

/**
 * Server Ping Endpoint
 * Lightweight endpoint to check if server is online
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
apply_rate_limit('admin_ping', 60, 60);



echo json_encode([
    'status' => 'online',
    'timestamp' => time()
]);
?>
