<?php

function get_dark_mode_class() {
    return 'dark-mode';
}


function output_dark_mode_init() {
    $nonce = $GLOBALS['admin_csp_nonce'] ?? '';
    $nonceAttr = $nonce ? ' nonce="' . htmlspecialchars($nonce, ENT_QUOTES, 'UTF-8') . '"' : '';
    echo <<<HTML
    <!-- Critical dark mode initialization -->
    <style$nonceAttr>
        /* Force dark mode immediately to prevent white flash */
        html, body {
            background-color: #1a202c !important;
            color: #f1f5f9 !important;
            transition: none !important;
        }

        /* Ensure all elements start with dark theme */
        * {
            background-color: inherit;
            color: inherit;
        }

        /* Override any potential light mode styles */
        .bg-white, .bg-light {
            background-color: #2d3748 !important;
        }

        .text-dark {
            color: #f1f5f9 !important;
        }

        .card, .modal-content {
            background-color: #2d3748 !important;
            color: #f1f5f9 !important;
        }
    </style>
    <script$nonceAttr>
        // Force dark mode immediately
        (function() {
            document.documentElement.classList.add('dark-mode');
            document.body.classList.add('dark-mode');
            localStorage.setItem("darkMode", "enabled");
            document.cookie = "darkMode=enabled; path=/; max-age=31536000; SameSite=Lax";
        })();
    </script>
HTML;
}
?>