<?php

require_once __DIR__ . '/version.php';


$releaseInfo = [
    'releaseName' => get_release_name(),
    'environment' => get_app_environment()
];
$nonce = $GLOBALS['admin_csp_nonce'] ?? '';
?>

<!-- Common JavaScript Libraries -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js" data-cfasync="false"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" data-cfasync="false"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11" data-cfasync="false"></script>

<!-- Common Admin Scripts -->
<script src="/js/session-security.min.js" data-cfasync="false"></script>
<script src="/js/admin-common.min.js" data-cfasync="false"></script>
<script src="/js/sidebar-fix.min.js" data-cfasync="false"></script>

<!-- Sentry Release Health Configuration -->
<script<?php if ($nonce) echo ' nonce="' . htmlspecialchars($nonce, ENT_QUOTES, 'UTF-8') . '"'; ?>>
    window.RELEASE_INFO = <?php echo json_encode($releaseInfo); ?>;
</script>
<script defer src="https://cloud.umami.is/script.js" data-website-id="d7d1f96a-2af6-49a7-af19-3168f54f4944"></script>

<!-- Force Dark Mode Only -->
<script<?php if ($nonce) echo ' nonce="' . htmlspecialchars($nonce, ENT_QUOTES, 'UTF-8') . '"'; ?>>
    $(document).ready(function() {
        // Always force dark mode - remove any toggle functionality
        document.documentElement.classList.add('dark-mode');
        document.body.classList.add('dark-mode');
        localStorage.setItem('darkMode', 'enabled');
        document.cookie = 'darkMode=enabled; path=/; max-age=31536000; SameSite=Lax';

        // Hide any dark mode toggle elements
        const darkModeMenuItem = document.querySelector('.dropdown-item[href="#"][data-action="toggle-dark-mode"]');
        if (darkModeMenuItem) {
            darkModeMenuItem.style.display = 'none';
        }

        const darkModeToggle = document.querySelector('.dark-mode-toggle');
        if (darkModeToggle) {
            darkModeToggle.style.display = 'none';
        }
    });
</script>
