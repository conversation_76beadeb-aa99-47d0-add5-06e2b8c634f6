<?php
/**
 * Authentication Module
 * Handles user authentication, authorization, and session management
 */


require_once __DIR__ . '/config.php';

require_once __DIR__ . '/../../includes/logging.php';

require_once __DIR__ . '/db_access.php';


// Minimal session helpers for admin portal (avoid pulling in public core.php)
if (!function_exists('generate_session_token')) {
    function generate_session_token() {
        return bin2hex(random_bytes(32));
    }
}

if (!function_exists('generate_session_fingerprint')) {
    function generate_session_fingerprint() {
        $fingerprint_data = [
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'accept_language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
            'accept_encoding' => $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '',
            'http_accept' => $_SERVER['HTTP_ACCEPT'] ?? '',
            'http_dnt' => $_SERVER['HTTP_DNT'] ?? '',
            'http_upgrade_insecure_requests' => $_SERVER['HTTP_UPGRADE_INSECURE_REQUESTS'] ?? '',
        ];
        return hash('sha256', serialize($fingerprint_data));
    }
}

if (!function_exists('create_secure_session')) {
    function create_secure_session($user_id, $user_data = []) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        session_regenerate_id(true);

        $session_token = generate_session_token();

        $_SESSION['discord_user_id'] = $user_id;
        $_SESSION['session_token'] = $session_token;
        $_SESSION['session_token_expires_at'] = time() + 1800; // 30 minutes server-side TTL
        $_SESSION['auth_time'] = time();
        $_SESSION['last_activity'] = time();
        $_SESSION['fingerprint'] = generate_session_fingerprint();
        $_SESSION['original_ip'] = $_SERVER['REMOTE_ADDR'] ?? '';

        foreach ($user_data as $key => $value) {
            $_SESSION[$key] = $value;
        }

        secure_log('Admin secure session created', 'info', [
            'user_id' => $user_id,
            'session_id' => session_id(),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
        ]);

        return $session_token;
    }
}
// Securely store Discord access token in session (encrypted)
if (!function_exists('store_discord_token')) {
    function store_discord_token($access_token) {
        if (session_status() === PHP_SESSION_NONE) { session_start(); }
        if (empty($access_token)) { return false; }
        if (!function_exists('openssl_encrypt')) {
            // As a last resort, avoid storing token if encryption is unavailable
            secure_log('OpenSSL not available; cannot encrypt Discord token', 'error');
            return false;
        }
        try {
            // Derive a 32-byte key from ENCRYPTION_KEY
            $base_key = defined('ENCRYPTION_KEY') && ENCRYPTION_KEY ? ENCRYPTION_KEY : '';
            $key = hash('sha256', $base_key, true);
            // Use configured IV (16 bytes) from env; validated in config.php
            $iv_source = defined('ENCRYPTION_IV') ? ENCRYPTION_IV : '';
            $iv = substr($iv_source, 0, 16);
            if (strlen($iv) < 16) {
                secure_log('Encryption IV not properly configured; refusing to store token', 'error');
                return false;
            }
            $cipher = 'aes-256-cbc';
            $ciphertext = openssl_encrypt($access_token, $cipher, $key, OPENSSL_RAW_DATA, $iv);
            if ($ciphertext === false) { return false; }
            $_SESSION['discord_access_token_enc'] = base64_encode($ciphertext);
            // Do not persist plaintext token anywhere
            return true;
        } catch (Throwable $e) {
            secure_log('Failed to encrypt Discord token: ' . $e->getMessage(), 'error');
            return false;
        }
    }
}

// Retrieve decrypted Discord access token from session
if (!function_exists('get_discord_token')) {
    function get_discord_token() {
        if (session_status() === PHP_SESSION_NONE) { session_start(); }
        $enc = $_SESSION['discord_access_token_enc'] ?? null;
        if (empty($enc) || !function_exists('openssl_decrypt')) { return null; }
        try {
            $base_key = defined('ENCRYPTION_KEY') && ENCRYPTION_KEY ? ENCRYPTION_KEY : '';
            $key = hash('sha256', $base_key, true);
            $iv_source = defined('ENCRYPTION_IV') ? ENCRYPTION_IV : '';
            $iv = substr($iv_source, 0, 16);
            if (strlen($iv) < 16) { return null; }
            $ciphertext = base64_decode($enc, true);
            if ($ciphertext === false) { return null; }
            $cipher = 'aes-256-cbc';
            $plaintext = openssl_decrypt($ciphertext, $cipher, $key, OPENSSL_RAW_DATA, $iv);
            return $plaintext !== false ? $plaintext : null;
        } catch (Throwable $e) {
            secure_log('Failed to decrypt Discord token: ' . $e->getMessage(), 'error');
            return null;
        }
    }
}


if (!empty($OWNER_ROLE_IDS) && !empty($DEVELOPER_ROLE_IDS) && !empty($SUPERVISOR_ROLE_IDS) &&
    !empty($ADMIN_ROLE_IDS) && !empty($MODERATOR_ROLE_IDS) && !empty($TRAINEE_ROLE_IDS) && !empty($GUILD_ID)) {
    if (getenv('APP_ENV') !== 'production') {


    }
} else {
    secure_log("ERROR: Missing required Discord role configuration");
    if (getenv('APP_ENV') !== 'production') {
        die("Authentication disabled: Incomplete configuration. Please check your environment variables.");
    }
}

/**
 * Check if Discord API is accessible
 *
 * Note: This function has been updated to always return true since we don't
 * want to rely on Discord API availability for basic site functionality.
 * The Discord bot is no longer used.
 *
 * @return bool Always returns true
 */
function is_bot_online() {
    // Always return true to ensure users aren't logged out due to Discord API issues
    return true;
}

/**
 * Verify user's Discord roles via API and log out if no staff role
 *
 * @return bool Whether user still has valid staff roles
 */
function verify_roles() {
    if (!is_authenticated()) {
        return false;
    }

    // Always verify roles regardless of bot status
    // The Discord bot is no longer used, but we still use the bot token for API access

    global $ENV, $GUILD_ID;
    $bot_token = $ENV['DISCORD_BOT_TOKEN'] ?? '';
    $user_id = $_SESSION['discord_user_id'] ?? '';
    $discord_api_endpoint = 'https://discord.com/api/v10';

    if (!$user_id || !$bot_token) {
        secure_log("ERROR: Missing user_id or bot_token for role verification");
        return false;
    }

    // We no longer check if Discord API is accessible
    // Instead, we'll try to verify roles and handle errors gracefully


    $ch = curl_init();

    curl_setopt_array($ch, [
        CURLOPT_URL => "{$discord_api_endpoint}/guilds/{$GUILD_ID}/members/{$user_id}",
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => ["Authorization: Bot {$bot_token}"],
        CURLOPT_SSL_VERIFYPEER => false,  // Disable SSL verification for Discord API
        CURLOPT_SSL_VERIFYHOST => false,  // Disable hostname verification
        CURLOPT_USERAGENT => 'MassacreMC Staff Portal',
        CURLOPT_CONNECTTIMEOUT => 5,
        CURLOPT_TIMEOUT => 10
    ]);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    $curl_errno = curl_errno($ch);

    // Handle curl errors
    if ($curl_error) {
        secure_log("cURL Error during role verification: " . $curl_error);
        secure_log("cURL Error code: " . $curl_errno);

        // Don't invalidate the session due to API connectivity issues
        // This prevents users from being logged out when Discord API is unavailable
        curl_close($ch);
        return true; // Allow session to continue
    }

    $member_info = json_decode($response, true);
    curl_close($ch);

    if ($http_code !== 200 || !isset($member_info['roles'])) {
        secure_log("Role verification failed - HTTP Code: $http_code - Response: $response");

        // If we can't get the roles from Discord, don't invalidate the session
        // This prevents users from being logged out when Discord API is unavailable
        if ($http_code === 0 || $http_code >= 500) {
            // Server error or connection issue - allow session to continue
            secure_log("Discord API server error or connection issue - allowing session to continue");
            return true;
        }

        // For other errors (like 401, 403, 404), we should invalidate the session
        // as these likely indicate an actual problem with the user's access
        return false;
    }

    $current_roles = $member_info['roles'];



    if (!has_staff_role($current_roles)) {
        secure_log("User {$user_id} no longer has staff roles. Logging out.");
        logout_user();
        return false;
    }


    if ($current_roles !== ($_SESSION['discord_roles'] ?? [])) {

        $_SESSION['discord_roles'] = $current_roles;


        $user_data = [
            'id' => $_SESSION['discord_user_id'],
            'username' => $_SESSION['discord_username'],
            'discriminator' => $_SESSION['discord_discriminator'],
            'avatar' => $_SESSION['discord_avatar'],
            'email' => $_SESSION['discord_email'] ?? ''
        ];
        $login_result = login_user($user_data, $current_roles);
        if (!$login_result['success']) {
            secure_log("Failed to update roles: {$login_result['message']}");
            logout_user();
            return false;
        }
    }

    return true;
}

/**
 * Check if session has expired
 *
 * @return bool Whether session has expired
 */
function session_expired() {
    if (!isset($_SESSION['auth_time'])) {
        return true;
    }

    $expired = (time() - $_SESSION['auth_time']) > SESSION_LIFETIME;
    return $expired;
}

/**
 * Check if user is authenticated
 *
 * @return bool Whether user is authenticated
 */
function is_authenticated() {
    // Basic session validation for admin panel
    if (!isset($_SESSION['discord_user_id']) || !isset($_SESSION['auth_time'])) {
        return false;
    }

    // Check if session has expired
    if (session_expired()) {
        return false;
    }

    return true;
}

/**
 * Refresh session timestamp
 */
function refresh_session() {
    $_SESSION['auth_time'] = time();
}



/**
 * Get user role
 *
 * @return string User's role
 */
function get_user_role() {
    if (!is_authenticated()) {
        return ROLE_UNAUTHORIZED;
    }

    return $_SESSION['discord_user_role'] ?? ROLE_UNAUTHORIZED;
}

/**
 * Check if user has required role or higher
 *
 * @param string $required_role Minimum required role
 * @return bool Whether user has required role
 */
function has_role($required_role) {
    $role = get_user_role();


    if ($role === ROLE_UNAUTHORIZED) {
        secure_log("ACCESS DENIED: User with UNAUTHORIZED role attempted to access a protected resource");
        return false;
    }





    $role_levels = [
        ROLE_OWNER => 6,
        ROLE_DEVELOPER => 5,
        ROLE_SUPERVISOR => 4,
        ROLE_ADMIN => 3,
        ROLE_MODERATOR => 2,
        ROLE_TRAINEE => 1,
        ROLE_UNAUTHORIZED => 0
    ];


    $user_level = $role_levels[$role] ?? 0;
    $required_level = $role_levels[$required_role] ?? 0;


    $has_access = $user_level >= $required_level;

    if (!$has_access) {
        secure_log("ACCESS DENIED: User with role {$role} (level {$user_level}) attempted to access resource requiring {$required_role} (level {$required_level})");
    }

    return $has_access;
}

/**
 * Check if maintenance mode is enabled
 *
 * @param bool $force_refresh Force a fresh check from the database
 * @return array Maintenance mode status and message
 */
function is_maintenance_mode($force_refresh = true) {
    // Default to maintenance mode disabled
    $default_result = ['enabled' => false, 'message' => ''];

    try {
        // Always get a fresh connection to ensure we're not using cached data
        $mongo = get_mongo_connection();
        if (!$mongo) {
            secure_log("Maintenance mode check: Failed to get MongoDB connection");
            return $default_result;
        }

        $db = $mongo->selectDatabase('mmc');

        // Check if the collection exists
        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('system_settings', $collections)) {
            secure_log("Maintenance mode check: system_settings collection does not exist");
            return $default_result;
        }

        $collection = $db->selectCollection('system_settings');

        // Use a timestamp to prevent caching
        $maintenance = $collection->findOne(
            ['setting_name' => 'maintenance_mode'],
            ['typeMap' => ['root' => 'array', 'document' => 'array']]
        );

        if (!$maintenance) {
            secure_log("Maintenance mode check: No maintenance_mode document found");
            return $default_result;
        }

        if (!isset($maintenance['enabled'])) {
            secure_log("Maintenance mode check: 'enabled' field not found in maintenance_mode document");
            return $default_result;
        }

        // Explicitly cast to boolean and ensure it's actually true
        $is_enabled = (bool)$maintenance['enabled'];

        // Log the maintenance mode status for debugging
        secure_log("Maintenance mode check: " . ($is_enabled ? 'ENABLED' : 'DISABLED') .
                 " (raw value: " . var_export($maintenance['enabled'], true) . ")");

        if ($is_enabled === true) {
            return [
                'enabled' => true,
                'message' => $maintenance['message'] ?? 'The system is currently undergoing maintenance. Please try again later.'
            ];
        }

        return $default_result;
    } catch (Exception $e) {
        secure_log("Error checking maintenance mode: " . $e->getMessage());
        return $default_result;
    }
}

/**
 * Require authentication and specific role to access a page
 *
 * @param string $required_role Required role to access the page
 */
function require_auth($required_role = ROLE_TRAINEE) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    if (!is_authenticated()) {
        $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
        header('Location: /login');
        exit;
    }

    if (session_expired()) {
        session_destroy();
        header('Location: /login?expired=1');
        exit;
    }

    try {
        $role_verification = verify_roles();
        if (!$role_verification) {
            header('Location: /login?error=no_staff_role');
            exit;
        }
    } catch (Exception $e) {
        // If role verification throws an exception, log it but don't log the user out
        secure_log("Exception during role verification in require_auth: " . $e->getMessage());
        // Continue with the session
    }

    if (get_user_role() === ROLE_UNAUTHORIZED) {
        secure_log("ACCESS DENIED: User with UNAUTHORIZED role attempted to access a protected page");
        logout_user(); // Force logout for unauthorized users
        header('Location: /login?error=unauthorized');
        exit;
    }

    // Check maintenance mode - only allow ADMIN or higher when maintenance mode is active
    $maintenance = is_maintenance_mode();
    if ($maintenance['enabled'] && !has_role(ROLE_ADMIN)) {
        if (is_ajax_request()) {
            header('Content-Type: application/json');
            http_response_code(503);
            echo json_encode([
                'error' => 'Maintenance Mode',
                'message' => $maintenance['message']
            ]);
            exit;
        } else {
            $_SESSION['maintenance_message'] = $maintenance['message'];
            header('Location: /maintenance.php');
            exit;
        }
    }

    if (!has_role($required_role)) {
        if (is_ajax_request()) {
            header('Content-Type: application/json');
            http_response_code(403);
            echo json_encode(['error' => 'You do not have permission to access this resource']);
            exit;
        } else {
            header('Location: /unauthorized.php');
            exit;
        }
    }

    refresh_session();

    if (!is_ajax_request() && function_exists('update_staff_activity')) {
        update_staff_activity(
            $_SESSION['discord_user_id'] ?? null,
            $_SESSION['discord_username'] ?? null,
            $_SESSION['discord_user_role'] ?? null,
            $_SESSION['discord_avatar'] ?? null
        );
    }
}

/**
 * Check if user has any staff role
 *
 * @param array $user_roles User's Discord role IDs
 * @return bool Whether user has staff role
 */
function has_staff_role($user_roles) {
    global $OWNER_ROLE_IDS, $DEVELOPER_ROLE_IDS, $SUPERVISOR_ROLE_IDS,
           $ADMIN_ROLE_IDS, $MODERATOR_ROLE_IDS, $TRAINEE_ROLE_IDS;

    foreach ($user_roles as $role_id) {
        if (in_array($role_id, $OWNER_ROLE_IDS) ||
            in_array($role_id, $DEVELOPER_ROLE_IDS) ||
            in_array($role_id, $SUPERVISOR_ROLE_IDS) ||
            in_array($role_id, $ADMIN_ROLE_IDS) ||
            in_array($role_id, $MODERATOR_ROLE_IDS) ||
            in_array($role_id, $TRAINEE_ROLE_IDS)) {
            return true;
        }
    }

    return false;
}

/**
 * Log user in
 *
 * @param array $discord_user Discord user data
 * @param array $discord_roles Discord role IDs
 * @return array Login result
 */
function login_user($discord_user, $discord_roles = []) {
    global $OWNER_ROLE_IDS, $DEVELOPER_ROLE_IDS, $SUPERVISOR_ROLE_IDS,
           $ADMIN_ROLE_IDS, $MODERATOR_ROLE_IDS, $TRAINEE_ROLE_IDS;


    if (!isset($discord_user['id']) || !isset($discord_user['username'])) {
        secure_log("LOGIN ERROR: Invalid Discord user data - ID or username missing");
        return ['success' => false, 'message' => 'Invalid Discord user data'];
    }

    if (empty($discord_roles)) {
        secure_log("LOGIN ERROR: No Discord roles provided for user {$discord_user['id']}");
        return ['success' => false, 'message' => 'No Discord roles available'];
    }





    $role = ROLE_UNAUTHORIZED;


    $has_owner = false;
    foreach ($discord_roles as $discord_role) {
        foreach ($OWNER_ROLE_IDS as $owner_role) {
            if ($discord_role === $owner_role) {
                $has_owner = true;
                break 2;
            }
        }
    }

    if ($has_owner) {
        $role = ROLE_OWNER;
    } else {

        $has_developer = false;
        foreach ($discord_roles as $discord_role) {
            foreach ($DEVELOPER_ROLE_IDS as $developer_role) {
                if ($discord_role === $developer_role) {
                    $has_developer = true;
                    break 2;
                }
            }
        }

        if ($has_developer) {
            $role = ROLE_DEVELOPER;
        } else {

            $has_supervisor = false;
            foreach ($discord_roles as $discord_role) {
                foreach ($SUPERVISOR_ROLE_IDS as $supervisor_role) {
                    if ($discord_role === $supervisor_role) {
                        $has_supervisor = true;
                        break 2;
                    }
                }
            }

            if ($has_supervisor) {
                $role = ROLE_SUPERVISOR;
            } else {

                $has_admin = false;
                foreach ($discord_roles as $discord_role) {
                    foreach ($ADMIN_ROLE_IDS as $admin_role) {
                        if ($discord_role === $admin_role) {
                            $has_admin = true;
                            break 2;
                        }
                    }
                }

                if ($has_admin) {
                    $role = ROLE_ADMIN;
                } else {

                    $has_mod = false;
                    foreach ($discord_roles as $discord_role) {
                        foreach ($MODERATOR_ROLE_IDS as $mod_role) {
                            if ($discord_role === $mod_role) {
                                $has_mod = true;
                                break 2;
                            }
                        }
                    }

                    if ($has_mod) {
                        $role = ROLE_MODERATOR;
                    } else {

                        $has_trainee = false;
                        foreach ($discord_roles as $discord_role) {
                            foreach ($TRAINEE_ROLE_IDS as $trainee_role) {
                                if ($discord_role === $trainee_role) {
                                    $has_trainee = true;
                                    break 2;
                                }
                            }
                        }

                        if ($has_trainee) {
                            $role = ROLE_TRAINEE;
                        } else {
                            secure_log("LOGIN: User does not have a valid staff role. Marking as UNAUTHORIZED.");
                            $role = ROLE_UNAUTHORIZED;
                        }
                    }
                }
            }
        }
    }


    // Create secure session with token for admin
    $session_token = create_secure_session($discord_user['id'], [
        'discord_username' => $discord_user['username'],
        'discord_discriminator' => isset($discord_user['discriminator']) ? $discord_user['discriminator'] : null,
        'discord_avatar' => isset($_SESSION['discord_avatar']) ? $_SESSION['discord_avatar'] : '',
        'discord_user_role' => $role,
        'discord_roles' => $discord_roles
    ]);


    if (isset($discord_user['email'])) {
        $_SESSION['discord_email'] = $discord_user['email'];
    } elseif (!isset($_SESSION['discord_email'])) {
        $_SESSION['discord_email'] = '';
    }




    if (function_exists('update_staff_activity')) {
        update_staff_activity(
            $discord_user['id'],
            $discord_user['username'],
            $role,
            isset($_SESSION['discord_avatar']) ? $_SESSION['discord_avatar'] : null,
            'login',  // Explicitly mark this as a login activity
            'User logged in via Discord OAuth'
        );
    }

    return ['success' => true, 'message' => 'Login successful', 'role' => $role, 'session_token' => $session_token];
}

/**
 * Log user out
 */
function logout_user() {
    $_SESSION = [];

    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(
            session_name(),
            '',
            time() - 42000,
            $params["path"],
            $params["domain"],
            $params["secure"],
            $params["httponly"]
        );
    }

    session_destroy();

    $cookie_expire = time() - 3600;


    setcookie('discord_user_id', '', [
        'expires' => $cookie_expire,
        'path' => COOKIE_PATH,
        'domain' => COOKIE_DOMAIN,
        'secure' => COOKIE_SECURE,
        'httponly' => COOKIE_HTTPONLY,
        'samesite' => COOKIE_SAMESITE
    ]);

    setcookie('discord_access_token', '', [
        'expires' => $cookie_expire,
        'path' => COOKIE_PATH,
        'domain' => COOKIE_DOMAIN,
        'secure' => COOKIE_SECURE,
        'httponly' => COOKIE_HTTPONLY,
        'samesite' => COOKIE_SAMESITE
    ]);

    // Clear encrypted Discord access token from session
    unset($_SESSION['discord_access_token_encrypted']);
    unset($_SESSION['discord_access_token']); // Clear any legacy plain text token
}



/**
 * Get user data
 *
 * @return array User data
 */
function get_user_data() {
    if (!is_authenticated()) {
        return null;
    }

    return [
        'user_id' => $_SESSION['discord_user_id'],
        'username' => $_SESSION['discord_username'],
        'discriminator' => $_SESSION['discord_discriminator'] ?? null,
        'avatar' => $_SESSION['discord_avatar'] ?? null,
        'email' => $_SESSION['discord_email'] ?? '',
        'role' => $_SESSION['discord_user_role']
    ];
}

/**
 * Check if request is AJAX
 *
 * @return bool Whether request is AJAX
 */
if (!function_exists('is_ajax_request')) {
function is_ajax_request() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
}
}

/**
 * Handle check status request for AJAX
 */
function handle_check_status() {
    header('Content-Type: application/json');

    if (!is_authenticated() || session_expired()) {
        http_response_code(401);
        echo json_encode([
            'authenticated' => false,
            'message' => 'Session expired or not authenticated',
            'reason' => session_expired() ? 'session_expired' : 'not_authenticated'
        ]);
        exit;
    }

    // Verify roles on each request, but handle API errors gracefully
    try {
        $role_verification = verify_roles();
        if (!$role_verification) {
            secure_log("User " . ($_SESSION['discord_user_id'] ?? 'unknown') . " failed role verification - logging out");
            logout_user(); // Ensure user is logged out
            http_response_code(401);
            echo json_encode([
                'authenticated' => false,
                'message' => 'Your Discord roles have changed. You no longer have access to this system.'
            ]);
            exit;
        }
    } catch (Exception $e) {
        // If role verification throws an exception, log it but don't log the user out
        secure_log("Exception during role verification: " . $e->getMessage());
        // Continue with the session
    }

    refresh_session();

    if (function_exists('update_staff_activity')) {
        $user_data = get_user_data();
        update_staff_activity(
            $user_data['user_id'],
            $user_data['username'],
            $user_data['role'],
            $user_data['avatar']
        );
    }

    echo json_encode([
        'authenticated' => true,
        'hasRequiredRole' => true,
        'username' => $_SESSION['discord_username'],
        'userId' => $_SESSION['discord_user_id'],
        'role' => $_SESSION['discord_user_role'],
        'avatar' => $_SESSION['discord_avatar'] ?? null
    ]);
    exit;
}


if (isset($_SERVER['REQUEST_URI']) && $_SERVER['REQUEST_URI'] === '/auth/check_status') {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    handle_check_status();
}

function start_page_buffer() {
    ob_start(function($buffer) {
        if (strpos($buffer, '<html') !== false && strpos($buffer, 'class="') === false) {
            // Add dark mode class to html tag if not already present
            require_once __DIR__ . '/theme-handler.php';
            $darkModeClass = get_dark_mode_class();
            $buffer = preg_replace('/<html/', '<html class="'.$darkModeClass.'"', $buffer, 1);

            // Add critical dark mode styles if needed
            if (strpos($buffer, '<!-- Critical dark mode initialization -->') === false) {
                $pos = strpos($buffer, '<head>') + 6;
                ob_start();
                output_dark_mode_init();
                $darkModeInit = ob_get_clean();
                $buffer = substr_replace($buffer, $darkModeInit, $pos, 0);
            }
        }
        return $buffer;
    });
}
start_page_buffer();
?>