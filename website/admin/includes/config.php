<?php
/**
 * Central Configuration File
 * Contains all configuration settings and environment variable handling
 */



$is_production = getenv('APP_ENV') === 'production' || getenv('APP_ENV') === null;


ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL & ~E_DEPRECATED & ~E_STRICT);


ini_set('log_errors', 1);
ini_set('error_log', '/var/log/php/massacremc-error.log');


ini_set('memory_limit', '512M');


if (getenv('APP_ENV') === 'development') {
    // Only enable these settings in a controlled development environment
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
}

if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_secure', 1);
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_path', '/');
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Lax');
    ini_set('session.gc_maxlifetime', 86400);
    ini_set('session.gc_probability', 1);
    ini_set('session.gc_divisor', 100);
}


require_once __DIR__ . '/../../includes/config_parser.php';

try {
    $ENV = load_env_config([
        '/etc/massacremc/config/admin.env',
        __DIR__ . '/../.env'
    ]);
} catch (Exception $e) {
    secure_log("Admin configuration error: " . $e->getMessage());
    $ENV = [];
}

define('SESSION_LIFETIME', 86400);
define('COOKIE_DOMAIN', $ENV['COOKIE_DOMAIN'] ?? $_SERVER['HTTP_HOST'] ?? 'localhost');
define('COOKIE_PATH', '/');
define('COOKIE_SECURE', true);
define('COOKIE_HTTPONLY', true);
define('COOKIE_SAMESITE', 'Lax');

define('ROLE_UNAUTHORIZED', 'UNAUTHORIZED');
define('ROLE_TRAINEE', 'TRAINEE');
define('ROLE_MODERATOR', 'MODERATOR');
define('ROLE_ADMIN', 'ADMIN');
define('ROLE_SUPERVISOR', 'SUPERVISOR');
define('ROLE_DEVELOPER', 'DEVELOPER');
define('ROLE_OWNER', 'OWNER');

$OWNER_ROLE_IDS = explode(',', $ENV['DISCORD_OWNER_ROLE_IDS'] ?? '');
$DEVELOPER_ROLE_IDS = explode(',', $ENV['DISCORD_DEVELOPER_ROLE_IDS'] ?? '');
$SUPERVISOR_ROLE_IDS = explode(',', $ENV['DISCORD_SUPERVISOR_ROLE_IDS'] ?? '');
$ADMIN_ROLE_IDS = explode(',', $ENV['DISCORD_ADMIN_ROLE_IDS'] ?? '');
$MODERATOR_ROLE_IDS = explode(',', $ENV['DISCORD_MODERATOR_ROLE_IDS'] ?? '');
$TRAINEE_ROLE_IDS = explode(',', $ENV['DISCORD_TRAINEE_ROLE_IDS'] ?? '');
$GUILD_ID = $ENV['DISCORD_GUILD_ID'] ?? '';

define('ENCRYPTION_KEY', $ENV['COOKIE_ENCRYPTION_KEY'] ?? null);
define('ENCRYPTION_IV', $ENV['COOKIE_ENCRYPTION_IV'] ?? null);

if (!ENCRYPTION_KEY || !ENCRYPTION_IV || strlen(ENCRYPTION_IV) < 16) {
    if (!$is_production) {
        die("Security configuration error: Missing encryption keys. Check your environment variables.");
    }
}

define('API_RATE_LIMIT', 60);
define('API_RATE_WINDOW', 60);

define('CSP_ENABLED', true);
define('CSP_REPORT_ONLY', false);

function env($key, $default = null) {
    global $ENV;
    return $ENV[$key] ?? $default;
}
