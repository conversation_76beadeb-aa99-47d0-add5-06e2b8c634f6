/**
 * Verify the AWS IAM ECS hosted auth works
 */

(function() {
"use strict";


const AWS_ACCOUNT_ARN = "arn:aws:sts::************:assumed-role/ecsTaskExecutionRole/*";

const conn = MongoRunner.runMongod({
    setParameter: {
        "authenticationMechanisms": "MONGODB-AWS,SCRAM-SHA-256",
    },
    auth: "",
});

const external = conn.getDB("$external");
const admin = conn.getDB("admin");

assert.commandWorked(admin.runCommand({createUser: "admin", pwd: "pwd", roles: ['root']}));
assert(admin.auth("admin", "pwd"));

assert.commandWorked(external.runCommand({createUser: AWS_ACCOUNT_ARN, roles:[{role: 'read', db: "aws"}]}));

const uri = "mongodb://127.0.0.1:20000/aws?authMechanism=MONGODB-AWS";
const program = "/root/src/.evergreen/run-mongodb-aws-ecs-test.sh";


const smoke = runMongoProgram(program, uri);
assert.eq(smoke, 0, "Could not auth with smoke user");


assert(external.auth({mechanism: 'MONGODB-AWS'}));

MongoRunner.stopMongod(conn);
}());
