<?php
require_once __DIR__ . '/../includes/security_headers.php';
set_security_headers();
/**
 * Player Account Portal - Staff Applications
 * View and manage staff applications
 */

require_once __DIR__ . '/../includes/core.php';
require_once __DIR__ . '/../includes/player-auth.php';
require_once __DIR__ . '/../includes/layout-unified.php';
require_once __DIR__ . '/../includes/db_access.php';


require_player_auth();

$player_data = get_player_data();


$applications = [];
try {
    $dbAccess = new DatabaseAccess();
    if ($dbAccess && $dbAccess->db) {
        $applicationsData = $dbAccess->db->staff_applications->find([
            'discord_id' => $player_data['discord_id']
        ], [
            'sort' => ['submitted_at' => -1]
        ]);
        
        foreach ($applicationsData as $app) {
            $applications[] = $app;
        }
    }
} catch (Exception $e) {
    secure_log("Error fetching staff applications", "error", [
        'discord_id' => $player_data['discord_id'],
        'error' => $e->getMessage()
    ]);
}


$scenario_fields = [
    'scenario_response_1',
    'scenario_response_2',
    'scenario_response_3',
    'scenario_response_4'
];

$pageTitle = "My Staff Applications - MassacreMC";
renderHeader($pageTitle, ['/css/services.css', '/css/account-portal.css']);
renderNavbar();
?>

<style>
/* Modern Status Alert Styling */
.status-alert-modern {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    margin-top: 1.5rem;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.status-alert-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    opacity: 0.8;
}

.status-alert-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.status-alert-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.status-alert-icon i {
    position: relative;
    z-index: 1;
}

.status-alert-content {
    flex: 1;
    min-width: 0;
}

.status-alert-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: white;
}

.status-alert-message {
    font-size: 0.95rem;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
}

.status-alert-notes {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
}

.status-alert-notes strong {
    color: white;
}

/* Status-specific styling */
.status-success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(22, 163, 74, 0.15));
    border-color: rgba(34, 197, 94, 0.3);
}

.status-success::before {
    background: linear-gradient(90deg, #22c55e, #16a34a);
}

.status-success .status-alert-icon {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.status-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(217, 119, 6, 0.15));
    border-color: rgba(245, 158, 11, 0.3);
}

.status-warning::before {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.status-warning .status-alert-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.status-danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(220, 38, 38, 0.15));
    border-color: rgba(239, 68, 68, 0.3);
}

.status-danger::before {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.status-danger .status-alert-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.status-info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(29, 78, 216, 0.15));
    border-color: rgba(59, 130, 246, 0.3);
}

.status-info::before {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.status-info .status-alert-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.status-secondary {
    background: linear-gradient(135deg, rgba(107, 114, 128, 0.15), rgba(75, 85, 99, 0.15));
    border-color: rgba(107, 114, 128, 0.3);
}

.status-secondary::before {
    background: linear-gradient(90deg, #6b7280, #4b5563);
}

.status-secondary .status-alert-icon {
    background: linear-gradient(135deg, #6b7280, #4b5563);
}

/* Mobile responsive */
@media (max-width: 768px) {
    .status-alert-modern {
        padding: 1.25rem;
        gap: 0.75rem;
    }

    .status-alert-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .status-alert-title {
        font-size: 1rem;
    }

    .status-alert-message {
        font-size: 0.9rem;
    }

    .status-alert-notes {
        font-size: 0.85rem;
        margin-top: 0.75rem;
        padding-top: 0.75rem;
    }
}
</style>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="display-5 fw-bold">
                        <i class="fas fa-user-tie me-3 text-success"></i>My Staff Applications
                    </h1>
                    <p class="lead text-muted">View and track your staff application submissions</p>
                </div>
                <a href="/staff-application" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>New Application
                </a>
            </div>

            <?php if (empty($applications)): ?>
            <!-- No Applications -->
            <div class="text-center py-5">
                <i class="fas fa-user-tie fa-5x text-muted mb-4"></i>
                <h3>No Applications Submitted</h3>
                <p class="text-muted mb-4">You haven't submitted any staff applications yet.</p>
                <a href="/staff-application" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>Submit an Application
                </a>
            </div>
            <?php else: ?>

            <!-- Applications List -->
            <div class="row">
                <?php foreach ($applications as $application): ?>
                <div class="col-12 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-1">
                                    <?php echo htmlspecialchars(ucfirst($application['position'])); ?> Application
                                </h5>
                                <small class="text-muted">
                                    Application ID: <?php echo htmlspecialchars($application['application_id']); ?>
                                </small>
                            </div>
                            <div class="text-end">
                                <?php
                                $status = $application['status'];
                                $statusClass = '';
                                $statusIcon = '';
                                
                                switch ($status) {
                                    case 'pending':
                                        $statusClass = 'bg-warning text-dark';
                                        $statusIcon = 'fas fa-clock';
                                        break;
                                    case 'approved':
                                        $statusClass = 'bg-success text-white';
                                        $statusIcon = 'fas fa-check-circle';
                                        break;
                                    case 'denied':
                                        $statusClass = 'bg-danger text-white';
                                        $statusIcon = 'fas fa-times-circle';
                                        break;
                                    case 'under_review':
                                        $statusClass = 'bg-info text-white';
                                        $statusIcon = 'fas fa-eye';
                                        break;
                                    default:
                                        $statusClass = 'bg-secondary text-white';
                                        $statusIcon = 'fas fa-question-circle';
                                }
                                ?>
                                <span class="badge <?php echo $statusClass; ?> px-3 py-2">
                                    <i class="<?php echo $statusIcon; ?> me-1"></i>
                                    <?php echo htmlspecialchars(ucfirst(str_replace('_', ' ', $status))); ?>
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-calendar-alt me-2 text-primary"></i>Submitted</h6>
                                    <p class="text-white">
                                        <?php echo $application['submitted_at']->toDateTime()->format('M j, Y \a\t g:i A'); ?>
                                    </p>

                                    <h6><i class="fas fa-user me-2 text-primary"></i>Position</h6>
                                    <p class="text-white"><?php echo htmlspecialchars(ucfirst($application['position'])); ?></p>

                                    <h6><i class="fas fa-clock me-2 text-primary"></i>Timezone</h6>
                                    <p class="text-white"><?php echo htmlspecialchars($application['timezone']); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-birthday-cake me-2 text-primary"></i>Age</h6>
                                    <p class="text-white"><?php echo htmlspecialchars($application['age']); ?> years old</p>

                                    <?php if (isset($application['reviewed_at'])): ?>
                                    <h6><i class="fas fa-eye me-2 text-primary"></i>Last Reviewed</h6>
                                    <p class="text-white">
                                        <?php echo $application['reviewed_at']->toDateTime()->format('M j, Y \a\t g:i A'); ?>
                                    </p>
                                    <?php endif; ?>

                                    <!-- Status Alert -->
                                    <?php
                                    $status = $application['status'];
                                    $alertClass = '';
                                    $alertIcon = '';
                                    $alertTitle = '';
                                    $alertMessage = '';

                                    switch ($status) {
                                        case 'pending':
                                            $alertClass = 'alert-warning';
                                            $alertIcon = 'fas fa-clock';
                                            $alertTitle = 'Application Pending';
                                            $alertMessage = 'Your application is awaiting review by our staff team. We\'ll notify you once a decision has been made.';
                                            break;
                                        case 'approved':
                                            $alertClass = 'alert-success';
                                            $alertIcon = 'fas fa-check-circle';
                                            $alertTitle = 'Application Approved!';
                                            $alertMessage = 'Congratulations! Your staff application has been approved. You will receive a notification with further instructions on Discord.';
                                            break;
                                        case 'denied':
                                            $alertClass = 'alert-danger';
                                            $alertIcon = 'fas fa-times-circle';
                                            $alertTitle = 'Application Denied';
                                            $alertMessage = 'Unfortunately, your application was not approved at this time. You may reapply 30 days from now.';
                                            break;
                                        case 'under_review':
                                            $alertClass = 'alert-info';
                                            $alertIcon = 'fas fa-eye';
                                            $alertTitle = 'Application Under Review';
                                            $alertMessage = 'Your application is currently being reviewed by our staff team. This process may take some time.';
                                            break;
                                        default:
                                            $alertClass = 'alert-secondary';
                                            $alertIcon = 'fas fa-question-circle';
                                            $alertTitle = 'Application Status Unknown';
                                            $alertMessage = 'The status of your application is unclear. Please contact an administrator.';
                                    }
                                    ?>

                                    <div class="status-alert-modern <?php echo str_replace('alert-', 'status-', $alertClass); ?>">
                                        <div class="status-alert-icon">
                                            <i class="<?php echo $alertIcon; ?>"></i>
                                        </div>
                                        <div class="status-alert-content">
                                            <div class="status-alert-title"><?php echo $alertTitle; ?></div>
                                            <div class="status-alert-message"><?php echo $alertMessage; ?></div>
                                            <?php if (isset($application['reviewer_notes']) && !empty($application['reviewer_notes'])): ?>
                                            <div class="status-alert-notes">
                                                <strong>Staff Notes:</strong> <?php echo htmlspecialchars($application['reviewer_notes']); ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Application Details (Collapsible) -->
                            <div class="mt-3">
                                <button class="btn btn-outline-primary btn-sm" type="button" data-bs-toggle="collapse" 
                                        data-bs-target="#details-<?php echo $application['application_id']; ?>" 
                                        aria-expanded="false">
                                    <i class="fas fa-eye me-1"></i>View Application Details
                                </button>
                            </div>
                            
                            <div class="collapse mt-3" id="details-<?php echo $application['application_id']; ?>">
                                <div class="card card-body bg-dark border-secondary">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Previous Experience</h6>
                                            <p class="small"><?php echo nl2br(htmlspecialchars($application['experience'])); ?></p>
                                            
                                            <h6>Why do you want to be staff?</h6>
                                            <p class="small"><?php echo nl2br(htmlspecialchars($application['why_staff'])); ?></p>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Scenario Responses</h6>

                                            <?php if (!empty($scenario_fields)): ?>
                                                <?php foreach ($scenario_fields as $index => $field): ?>
                                                    <div class="mb-3">
                                                        <strong>Scenario <?php echo $index + 1; ?>:</strong>
                                                        <p class="small mb-1"><?php echo nl2br(htmlspecialchars($application[$field] ?? 'No response provided')); ?></p>
                                                    </div>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <div class="alert alert-warning small">
                                                    <strong>No scenario responses found.</strong> This might be an older application format.
                                                </div>
                                            <?php endif; ?>
                                            
                                            <h6>Availability</h6>
                                            <p class="small"><?php echo nl2br(htmlspecialchars($application['availability'])); ?></p>
                                            
                                            <?php if (!empty($application['additional_info'])): ?>
                                            <h6>Additional Information</h6>
                                            <p class="small"><?php echo nl2br(htmlspecialchars($application['additional_info'])); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
renderFooter(['/js/account-portal.min.js', '/js/account-portal-mobile.min.js']);
?>
