<?php
require_once __DIR__ . '/../includes/security_headers.php';
set_security_headers();
/**
 * Player Account Portal - Appeals
 * View and manage punishment appeals
 */

require_once __DIR__ . '/../includes/core.php';
require_once __DIR__ . '/../includes/player-auth.php';
require_once __DIR__ . '/../includes/layout-unified.php';
require_once __DIR__ . '/../includes/db_access.php';


require_player_auth();

$player_data = get_player_data();


$xbox_username = null;
$has_xbox_linked = false;
try {
    $dbAccess = new DatabaseAccess();
    if ($dbAccess && $dbAccess->db) {
        $xbox_link = $dbAccess->db->linked_accounts->findOne([
            'discord_id' => $player_data['discord_id'],
            'platform' => 'xbox',
            'status' => 'verified'
        ]);
        if ($xbox_link) {
            $xbox_username = $xbox_link['username'];
            $has_xbox_linked = true;
        }
    }
} catch (Exception $e) {
    secure_log("Error checking Xbox link for appeals", "error", [
        'discord_id' => $player_data['discord_id'],
        'error' => $e->getMessage()
    ]);
}


if (!$has_xbox_linked) {
    $pageTitle = "My Appeals - MassacreMC";
    renderHeader($pageTitle, ['/css/services.css', '/css/account-portal.css']);
    renderNavbar();
    ?>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="text-center">
                    <i class="fab fa-xbox fa-5x text-warning mb-4"></i>
                    <h1 class="display-5 fw-bold mb-3">Xbox Account Required</h1>
                    <p class="lead text-muted mb-4">You must link your Xbox account to view and submit appeals.</p>
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Why Xbox linking is required:</strong> This ensures appeals are tied to your actual game account and prevents abuse.
                    </div>
                    <a href="/account/settings" class="btn btn-warning btn-lg">
                        <i class="fab fa-xbox me-2"></i>Link Xbox Account
                    </a>
                    <a href="/account/" class="btn btn-secondary ms-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
    <?php
    renderFooter(['/js/account-portal.min.js']);
    exit;
}

$submissions = get_player_submissions($player_data['discord_id']);
$appeals = $submissions['appeals'];

$pageTitle = "My Appeals - MassacreMC";
renderHeader($pageTitle, ['/css/services.css', '/css/account-portal.css']);
renderNavbar();
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="display-5 fw-bold">
                        <i class="fas fa-gavel me-3 text-warning"></i>My Appeals
                    </h1>
                    <p class="lead text-muted">View and track your punishment appeals</p>
                </div>
                <a href="/appeal" class="btn btn-warning">
                    <i class="fas fa-plus me-2"></i>New Appeal
                </a>
            </div>

            <?php if (empty($appeals)): ?>
            <!-- No Appeals -->
            <div class="text-center py-5">
                <i class="fas fa-gavel fa-5x text-muted mb-4"></i>
                <h3>No Appeals Submitted</h3>
                <p class="text-muted mb-4">You haven't submitted any punishment appeals yet.</p>
                <a href="/appeal" class="btn btn-warning">
                    <i class="fas fa-plus me-2"></i>Submit an Appeal
                </a>
            </div>
            <?php else: ?>

            <!-- Appeals List -->
            <div class="row">
                <?php foreach ($appeals as $appeal): ?>
                <div class="col-12 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-1">
                                    <?php
                                    // Format punishment type (capitalize first letter)
                                    $punishment_type = ucfirst(strtolower($appeal['punishment_type']));
                                    echo htmlspecialchars($punishment_type);
                                    ?> Appeal
                                    <?php if (isset($appeal['offense_type']) && !empty($appeal['offense_type'])): ?>
                                        - <?php
                                        // Format offense type (replace underscores with spaces and capitalize)
                                        $offense_type = str_replace('_', ' ', $appeal['offense_type']);
                                        $offense_type = ucwords(strtolower($offense_type));
                                        echo htmlspecialchars($offense_type);
                                        ?>
                                    <?php endif; ?>
                                </h5>
                                <small class="text-muted">
                                    Appeal ID: <?php echo htmlspecialchars($appeal['id']); ?> •
                                    Submitted: <?php
                                        $date = $appeal['created_at']->toDateTime();
                                        $date->setTimezone(new DateTimeZone('America/New_York'));
                                        echo $date->format('M j, Y g:i A') . ' ET';
                                    ?>
                                </small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php
                                    echo $appeal['status'] === 'Pending' ? 'secondary' :
                                        ($appeal['status'] === 'Approved' ? 'success' :
                                        ($appeal['status'] === 'Denied' ? 'danger' : 'secondary'));
                                ?> fs-6">
                                    <?php echo $appeal['status']; ?>
                                </span>
                                <?php if (isset($appeal['offense_count']) && $appeal['offense_count'] > 1): ?>
                                <br>
                                <small class="text-muted">
                                    Offense #<?php echo $appeal['offense_count']; ?>
                                </small>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <p class="mb-2"><strong>Player:</strong> <?php echo htmlspecialchars($appeal['player_name']); ?></p>
                                    <p class="mb-2"><strong>Punishment ID:</strong>
                                        <code class="fw-bold text-primary"><?php echo htmlspecialchars($appeal['punishment_id']); ?></code>
                                    </p>
                                    <p class="mb-3"><strong>Appeal Reason:</strong></p>
                                    <div class="border p-3 rounded" style="background-color: #f8f9fa !important; color: #333 !important; border: 1px solid #dee2e6 !important;">
                                        <?php echo nl2br(htmlspecialchars($appeal['reason'])); ?>
                                    </div>
                                </div>
                                <div class="col-md-4">

                                    <?php if (isset($appeal['history']) && !empty($appeal['history'])): ?>
                                    <div class="mt-3">
                                        <strong>Appeal History:</strong>
                                        <div class="mt-2">
                                            <?php foreach ($appeal['history'] as $history_item): ?>
                                            <div class="small text-muted mb-1">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php
                                                    $historyDate = $history_item['timestamp']->toDateTime();
                                                    $historyDate->setTimezone(new DateTimeZone('America/New_York'));
                                                    echo $historyDate->format('M j, Y g:i A') . ' ET';
                                                ?>:
                                                <?php echo htmlspecialchars($history_item['details']); ?>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Status-specific information -->
                            <?php if ($appeal['status'] === 'Approved'): ?>
                            <div class="alert alert-success mt-3" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Appeal Approved!</strong> Your punishment has been reviewed and the appeal was accepted.
                            </div>
                            <?php elseif ($appeal['status'] === 'Denied'): ?>
                            <div class="alert alert-danger mt-3" role="alert">
                                <i class="fas fa-times-circle me-2"></i>
                                <strong>Appeal Denied.</strong> Your appeal was reviewed but the original punishment stands.
                            </div>
                            <?php elseif ($appeal['status'] === 'Pending'): ?>
                            <div class="alert alert-warning mt-3" role="alert">
                                <i class="fas fa-clock me-2"></i>
                                <strong>Under Review.</strong> Your appeal is being reviewed by our staff team. Please be patient.
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <?php endif; ?>

            <!-- Help Section -->
            <div class="row mt-5">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-book fa-3x text-info mb-3"></i>
                            <h5>Appeal Guidelines</h5>
                            <p class="text-muted">Learn about our appeal process and what makes a successful appeal.</p>
                            <a href="/rules" class="btn btn-outline-info">View Guidelines</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-ban fa-3x text-primary mb-3"></i>
                            <h5>View Your Punishments</h5>
                            <p class="text-muted">View your punishment history</p>
                            <a href="/account/punishments" class="btn btn-outline-primary">View Punishments</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Back to Dashboard -->
            <div class="text-center mt-4">
                <a href="/account/" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<?php
renderFooter(['/js/account-portal.min.js', '/js/account-portal-mobile.min.js']);
?>
