<?php

require_once __DIR__ . '/includes/core.php';
require_once __DIR__ . '/includes/layout-unified.php';
require_once __DIR__ . '/includes/db_access.php';

$pageTitle = "Server Updates & Changelog - MassacreMC";


header("Cache-Control: public, max-age=300");
header("Expires: " . gmdate("D, d M Y H:i:s", time() + 300) . " GMT");

renderHeader('Server Updates', []);
?>

<style>
.featured-update {
    position: relative;
}

.featured-update::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
    border-radius: 12px;
    z-index: -1;
    opacity: 0.3;
}



.update-item {
    transition: transform 0.3s ease;
}

.update-item:hover {
    transform: translateY(-5px);
}

.update-meta span {
    font-size: 0.9rem;
    opacity: 0.8;
}

.update-description {
    line-height: 1.7;
}

@media (max-width: 768px) {
    .update-meta {
        flex-direction: column !important;
        gap: 0.5rem !important;
    }
}
</style>

<?php

$allUpdates = [];
try {
    $db = new DatabaseAccess();
    
    $cursor = $db->db->announcements->find(
        [
            'category' => 'public',
            'is_global' => true,
            'timestamp' => ['$gte' => new MongoDB\BSON\UTCDateTime((time() - 90 * 24 * 3600) * 1000)]
        ],
        [
            'sort' => ['timestamp' => -1],
            'limit' => 20,
            'projection' => ['title' => 1, 'message' => 1, 'timestamp' => 1, 'sender_name' => 1]
        ]
    );
    
    foreach ($cursor as $announcement) {
        $allUpdates[] = [
            'title' => $announcement['title'],
            'description' => $announcement['message'],
            'date' => $announcement['timestamp']->toDateTime()->format('Y-m-d'),
            'datetime' => $announcement['timestamp']->toDateTime(),
            'author' => $announcement['sender_name'] ?? 'Staff Team',
            'icon' => 'bullhorn',
            'type' => 'announcement'
        ];
    }
} catch (Exception $e) {
    $allUpdates = [
        [
            'title' => 'Server Performance Improvements',
            'description' => 'We\'ve implemented significant server optimizations to reduce lag during peak hours. Our new anti-cheat system is now more efficient and accurate, providing a better gaming experience for all players.',
            'date' => date('Y-m-d', strtotime('-3 days')),
            'datetime' => new DateTime('-3 days'),
            'author' => 'Development Team',
            'icon' => 'cogs',
            'type' => 'update'
        ],
        [
            'title' => 'New Custom Enchantments Released',
            'description' => 'We\'ve added 5 exciting new custom enchantments to enhance your gameplay:\n\n• Lightning Strike - Chance to strike enemies with lightning\n• Vampire - Heal yourself when dealing damage\n• Explosive - Arrows explode on impact\n• Freeze - Slow enemies on hit\n• Lifesteal - Steal health from enemies\n\nUse /enchants in-game to learn more!',
            'date' => date('Y-m-d', strtotime('-7 days')),
            'datetime' => new DateTime('-7 days'),
            'author' => 'Staff Team',
            'icon' => 'magic',
            'type' => 'feature'
        ],
        [
            'title' => 'Faction Upgrade System Launch',
            'description' => 'Introducing our brand new faction upgrade system! Factions can now invest in various upgrades to improve their bases:\n\n• Defensive Upgrades: Increased explosion resistance, better walls\n• Economic Upgrades: Faster resource generation, better trading\n• Military Upgrades: Enhanced weapons, better armor\n• Utility Upgrades: Faster travel, improved storage\n\nVisit your faction base and use /f upgrades to get started!',
            'date' => date('Y-m-d', strtotime('-14 days')),
            'datetime' => new DateTime('-14 days'),
            'author' => 'Development Team',
            'icon' => 'arrow-up',
            'type' => 'feature'
        ]
    ];
}

renderNavbar();
?>

<div class="container py-5">
    <div class="section-header text-center mb-5">
        <h1 class="display-3 fw-bold mb-4">Recent <span class="text-gradient">Updates</span></h1>
        <div class="divider mx-auto mb-4"></div>
        <p class="lead text-muted fs-5 mx-auto" style="max-width: 600px;">Stay up to date with the latest server improvements and features</p>
    </div>

    <?php if (empty($allUpdates)): ?>
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="feature-card text-center py-5">
                    <div class="feature-icon mb-4">
                        <i class="fas fa-newspaper text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                    </div>
                    <h3 class="text-muted mb-3">No Updates Available</h3>
                    <p class="text-muted mb-4 fs-6">Check back soon for the latest server news and updates!</p>
                    <a href="/discord" class="btn btn-primary btn-lg">
                        <i class="fab fa-discord me-2"></i>Join Discord for Updates
                    </a>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <?php foreach ($allUpdates as $index => $update): ?>
                    <div class="update-item mb-5 <?php echo $index === 0 ? 'featured-update' : ''; ?>">
                        <div class="feature-card position-relative h-100">
                            <div class="row align-items-start">
                                <div class="col-md-2 text-center mb-3 mb-md-0">
                                    <span class="badge bg-<?= $update['type'] === 'announcement' ? 'info' : ($update['type'] === 'feature' ? 'success' : 'primary') ?> px-3 py-2">
                                        <?= ucfirst(htmlspecialchars($update['type'])) ?>
                                    </span>
                                </div>
                                <div class="col-md-10">
                                    <div class="update-content">
                                        <h3 class="mb-3 fw-bold"><?= htmlspecialchars($update['title']) ?></h3>
                                        <div class="update-meta text-muted mb-4 d-flex flex-wrap gap-3">
                                            <span class="d-flex align-items-center">
                                                <i class="far fa-calendar-alt me-2"></i>
                                                <?= date('F j, Y', strtotime($update['date'])) ?>
                                            </span>
                                            <span class="d-flex align-items-center">
                                                <i class="fas fa-user me-2"></i>
                                                <?= htmlspecialchars($update['author']) ?>
                                            </span>
                                            <span class="d-flex align-items-center">
                                                <i class="fas fa-clock me-2"></i>
                                                <?php
                                                $days = floor((time() - strtotime($update['date'])) / 86400);
                                                echo $days === 0 ? 'Today' : ($days === 1 ? 'Yesterday' : $days . ' days ago');
                                                ?>
                                            </span>
                                        </div>
                                        <div class="update-description fs-6 lh-lg">
                                            <?= nl2br(htmlspecialchars($update['description'])) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="text-center mt-5 pt-4">
            <div class="feature-card d-inline-block px-5 py-4">
                <h4 class="mb-3">Stay Connected</h4>
                <p class="text-muted mb-4">Want to stay updated? Join our Discord for real-time announcements and community discussions!</p>
                <a href="/discord" class="btn btn-primary btn-lg px-4">
                    <i class="fab fa-discord me-2"></i>Join Discord Community
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
renderFooter([]);
?>
