/**
 * Report Form JavaScript
 * Handles form validation, rule population, and form reset functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('=== REPORT FORM SCRIPT LOADED ===');
    
    // Define rule sets
    const playerRules = [
        "Hacking/Cheating",
        "Kill Farming", 
        "Punishment Evading",
        "Harassment/Bullying",
        "Inappropriate Content",
        "Inappropriate Language",
        "Hate Speech",
        "Scamming",
        "Exploiting Bugs",
        "Advertising",
        "Other"
    ];

    const factionRules = [
        "Inappropriate Faction Name",
        "Faction Griefing",
        "Faction Scamming", 
        "Faction Harassment",
        "Faction Exploiting",
        "Other"
    ];

    // Get form elements
    const reportTypeSelect = document.getElementById("reportType");
    const ruleBrokenSelect = document.getElementById("ruleBroken");
    const offenderNameInput = document.getElementById("offenderName");
    const offenderNameLabel = document.getElementById("offenderNameLabel");
    const offenderNameFeedback = document.getElementById("offenderNameFeedback");
    const offenderNameHelp = document.getElementById("offenderNameHelp");
    const evidenceTextarea = document.getElementById("evidence");

    console.log('Form elements found:', {
        reportTypeSelect: !!reportTypeSelect,
        ruleBrokenSelect: !!ruleBrokenSelect,
        offenderNameInput: !!offenderNameInput
    });

    if (!reportTypeSelect || !ruleBrokenSelect) {
        console.error('Required form elements not found');
        return;
    }

    function updateFormFields() {
        console.log('=== updateFormFields() called ===');
        
        const reportType = reportTypeSelect.value;
        console.log('Report type:', reportType);

        // Reset form data
        if (offenderNameInput) offenderNameInput.value = "";
        if (evidenceTextarea) evidenceTextarea.value = "";

        // Clear validation states
        if (offenderNameInput) {
            offenderNameInput.classList.remove("is-valid", "is-invalid");
            offenderNameInput.dataset.valid = "false";
        }
        if (evidenceTextarea) evidenceTextarea.classList.remove("is-valid", "is-invalid");
        if (ruleBrokenSelect) ruleBrokenSelect.classList.remove("is-valid", "is-invalid");

        // Clear existing options and rebuild dropdown
        console.log('Clearing ruleBrokenSelect options...');
        ruleBrokenSelect.innerHTML = '<option value="" selected disabled>Select a rule violation</option>';
        ruleBrokenSelect.selectedIndex = 0;

        // Hide any existing suggestions
        if (offenderNameInput && offenderNameInput.parentNode) {
            const suggestions = offenderNameInput.parentNode.querySelector(".player-suggestions");
            if (suggestions) {
                suggestions.style.display = "none";
                suggestions.innerHTML = "";
            }
        }

        // Remove form validation state
        const formEl = document.getElementById("reportForm");
        if (formEl) formEl.classList.remove("was-validated");

        // Update based on report type
        if (reportType === "faction") {
            console.log('Setting up faction mode...');
            
            // Update labels and placeholders
            if (offenderNameLabel) offenderNameLabel.textContent = "Faction Name";
            if (offenderNameInput) {
                offenderNameInput.placeholder = "Enter the name of the faction you are reporting";
                offenderNameInput.setAttribute("data-search-type", "faction");
                offenderNameInput.setAttribute("data-player-search", "false");
            }
            if (offenderNameFeedback) offenderNameFeedback.textContent = "Please provide the name of the faction you are reporting.";
            if (offenderNameHelp) offenderNameHelp.innerHTML = "<small>Please enter a valid faction name that exists on our server</small>";

            // Populate faction rules
            console.log('Populating faction rules:', factionRules);
            factionRules.forEach((rule, index) => {
                console.log(`Adding faction rule ${index + 1}:`, rule);
                const option = document.createElement("option");
                option.value = rule;
                option.textContent = rule;
                ruleBrokenSelect.appendChild(option);
            });

            console.log('Faction mode complete - total options:', ruleBrokenSelect.options.length);
        } else {
            console.log('Setting up player mode...');
            
            // Update labels and placeholders
            if (offenderNameLabel) offenderNameLabel.textContent = "Player Username";
            if (offenderNameInput) {
                offenderNameInput.placeholder = "Enter the username of the player you are reporting";
                offenderNameInput.setAttribute("data-player-search", "true");
                offenderNameInput.removeAttribute("data-search-type");
            }
            if (offenderNameFeedback) offenderNameFeedback.textContent = "Please provide the username of the player you are reporting.";
            if (offenderNameHelp) offenderNameHelp.innerHTML = "<small>Please enter a valid player name that exists on our server</small>";

            // Populate player rules
            console.log('Populating player rules:', playerRules);
            playerRules.forEach((rule, index) => {
                console.log(`Adding player rule ${index + 1}:`, rule);
                const option = document.createElement("option");
                option.value = rule;
                option.textContent = rule;
                ruleBrokenSelect.appendChild(option);
            });

            console.log('Player mode complete - total options:', ruleBrokenSelect.options.length);
        }

        // Refresh evidence character counter
        if (evidenceTextarea) {
            try {
                evidenceTextarea.dispatchEvent(new Event("input"));
            } catch (e) {
                console.warn("Could not dispatch input event:", e);
            }
        }

        console.log('Form reset complete for', reportType, 'mode');
    }

    // Initialize form on page load
    console.log('Initializing form...');
    updateFormFields();

    // Bind change handler
    reportTypeSelect.addEventListener("change", updateFormFields);
    console.log('Event listener attached to report type select');

    // Set up form validation
    const form = document.getElementById("reportForm");
    if (form) {
        Array.from(form.elements).forEach(input => {
            input.addEventListener("input", function() {
                this.classList.remove("is-invalid");
            });
        });
    }

    // Make function globally available for debugging
    window.updateFormFields = updateFormFields;
    window.reportFormDebug = {
        playerRules: playerRules,
        factionRules: factionRules,
        elements: {
            reportTypeSelect,
            ruleBrokenSelect,
            offenderNameInput
        }
    };

    console.log('=== REPORT FORM INITIALIZATION COMPLETE ===');
});
