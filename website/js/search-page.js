(function() {
  function initSearchPage() {
    try {
      console.debug("[search] initSearchPage running");
      const searchInput = document.getElementById("searchInput");
      const searchButton = document.getElementById("searchButton");
      const loadingElement = document.getElementById("loading");
      const errorElement = document.getElementById("error-message");
      const errorText = document.getElementById("error-text");
      const searchNameElement = document.getElementById("searchName");
      const lookupPlayerButton = document.getElementById("lookupPlayer");
      const lookupFactionButton = document.getElementById("lookupFaction");
      const selectionModal = document.getElementById("selectionModal");
      const searchResultsContainer = document.getElementById("search-results");

      if (!searchInput || !searchButton) {
        console.warn("[search] Missing input/button elements; aborting init");
        return;
      }

      function debounce(func, wait) {
        let timeout;
        return function(...args) {
          clearTimeout(timeout);
          timeout = setTimeout(() => func.apply(this, args), wait);
        };
      }

      // Bridge detection: support constructor or pre-instantiated instance
      let searchBridge = null;
      if (window.SearchApiBridge) {
        try {
          if (typeof window.SearchApiBridge === "function") {
            searchBridge = new window.SearchApiBridge();
          } else {
            searchBridge = window.SearchApiBridge; // instance already
          }
          console.debug("[search] SearchApiBridge ready", { type: typeof window.SearchApiBridge });
        } catch (e) {
          console.warn("[search] Failed to init SearchApiBridge; falling back", e);
          searchBridge = null;
        }
      } else {
        console.warn("[search] SearchApiBridge not found; using legacy search");
      }

      function sanitizeText(text) {
        const div = document.createElement("div");
        div.textContent = text;
        return div.innerHTML;
      }

      function performSearch() {
        const query = searchInput.value.trim();
        if (!query) {
          if (window.Swal) {
            Swal.fire({ icon: "warning", title: "Empty Search", text: "Please enter a username or faction name.", confirmButtonColor: "#3085d6" });
          }
          return;
        }

        // Show loading
        if (loadingElement) loadingElement.style.display = "block";
        if (errorElement) errorElement.style.display = "none";
        if (searchResultsContainer) searchResultsContainer.style.display = "none";

        if (searchBridge && typeof searchBridge.search === "function") {
          searchBridge.search(query, "both").then(data => {
            if (loadingElement) loadingElement.style.display = "none";
            if (!data || !data.success) {
              if (errorElement && errorText) {
                errorElement.style.display = "block";
                errorText.textContent = (data && data.error) || "An error occurred while searching.";
              }
              return;
            }

            if (searchResultsContainer && typeof searchBridge.displaySearchResults === "function") {
              searchResultsContainer.style.display = "block";
              searchBridge.displaySearchResults("#search-results", data);
            }

            if (data.results && data.results.length === 1 && data.results[0].local_endpoint) {
              setTimeout(() => { window.location.href = data.results[0].local_endpoint; }, 1200);
            }
          }).catch(err => {
            console.error("[search] Bridge search error", err);
            if (loadingElement) loadingElement.style.display = "none";
            if (errorElement && errorText) {
              errorElement.style.display = "block";
              errorText.textContent = "An error occurred while searching. Please contact an administrator if this persists.";
            }
          });
        } else {
          // Legacy fallback
          fetch(`/search?ajax_search=1&query=${encodeURIComponent(query)}`, { headers: { "X-Requested-With": "XMLHttpRequest" } })
            .then(r => { if (!r.ok) throw new Error(`HTTP ${r.status}`); return r.json(); })
            .then(data => {
              if (loadingElement) loadingElement.style.display = "none";
              if (!data || data.error) {
                if (errorElement && errorText) {
                  errorElement.style.display = "block";
                  errorText.textContent = (data && data.error) || "An error occurred while searching.";
                }
                return;
              }

              const playerName = data.player_name || query;
              const factionName = data.faction_name || query;

              if (data.player && data.faction) {
                if (searchNameElement) searchNameElement.textContent = sanitizeText(query);
                if (selectionModal && window.bootstrap && bootstrap.Modal) {
                  const modal = new bootstrap.Modal(selectionModal);
                  modal.show();
                  if (lookupPlayerButton) lookupPlayerButton.onclick = () => { window.location.href = `/player/${encodeURIComponent(playerName)}`; };
                  if (lookupFactionButton) lookupFactionButton.onclick = () => { window.location.href = `/faction/${encodeURIComponent(factionName)}`; };
                }
              } else if (data.player) {
                window.location.href = `/player/${encodeURIComponent(playerName)}`;
              } else if (data.faction) {
                window.location.href = `/faction/${encodeURIComponent(factionName)}`;
              } else {
                if (errorElement && errorText) {
                  errorElement.style.display = "block";
                  errorText.innerHTML = `<strong>No results found for "${sanitizeText(query)}"</strong><br><span class="text-muted mt-2 d-block">This player or faction does not exist.</span>`;
                }
              }
            })
            .catch(err => {
              console.error("[search] Legacy search error", err);
              if (loadingElement) loadingElement.style.display = "none";
              if (errorElement && errorText) {
                errorElement.style.display = "block";
                errorText.textContent = "An error occurred while searching. Please contact an administrator if this persists.";
              }
            });
        }
      }

      // Wire events
      searchButton.addEventListener("click", performSearch);
      const debouncedSearch = debounce(performSearch, 300);
      searchInput.addEventListener("keydown", function(event) {
        if (event.key === "Enter") {
          event.preventDefault();
          debouncedSearch();
        }
      });

      // Optional: simple autocomplete hook (no-op)
      searchInput.addEventListener("input", debounce(function() {
        const q = searchInput.value.trim();
        if (q.length >= 2) {
          fetch(`/api/search-players.php?q=${encodeURIComponent(q)}`).then(r => r.json()).catch(() => {});
        }
      }, 500));
    } catch (e) {
      console.error("[search] init error", e);
    }
  }

  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initSearchPage);
  } else {
    initSearchPage();
  }
})();

