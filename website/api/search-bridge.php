<?php
/**
 * Search Bridge API
 * 
 * Bridges the gap between username/faction name search and Go API endpoints
 * This endpoint searches for players/factions and returns the necessary data
 * to make calls to the Go API endpoints
 * 
 * Usage:
 * GET /api/search-bridge.php?query=username&type=player
 * GET /api/search-bridge.php?query=factionname&type=faction
 * GET /api/search-bridge.php?query=searchterm (searches both)
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://portal.massacremc.net');
header('Vary: Origin');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');


if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../includes/core.php';
require_once __DIR__ . '/../includes/db_access.php';
require_once __DIR__ . '/../includes/mongo_security.php';

apply_rate_limit('search-bridge', 20, 60, true);

try {
    // Get parameters
    $query = isset($_GET['query']) ? trim($_GET['query']) : '';
    $type = isset($_GET['type']) ? trim($_GET['type']) : 'both'; // player, faction, or both
    $go_api_url = isset($_GET['go_api_url']) ? trim($_GET['go_api_url']) : 'http://localhost:8080';
    
    if (empty($query)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Query parameter is required'
        ]);
        exit;
    }
    
    // Validate query length
    if (strlen($query) < 2 || strlen($query) > 50) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Query must be between 2 and 50 characters'
        ]);
        exit;
    }
    
    // Initialize database connection
    $db = new DatabaseAccess();
    
    if (!$db || !$db->db) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Database connection failed'
        ]);
        exit;
    }
    
    $response = [
        'success' => true,
        'query' => $query,
        'results' => []
    ];
    
    // Search based on type
    if ($type === 'player' || $type === 'both') {
        // Search for player
        $uuid_result = $db->get_player_uuid($query);

        if (isset($uuid_result['success']) && $uuid_result['success']) {
            // Get up-to-date player data from API
            require_once __DIR__ . '/../includes/api_client.php';
            $apiClient = new ApiClient();
            $api_player_data = $apiClient->getPlayerData($uuid_result['uuid']);

            $response['results'][] = [
                'type' => 'player',
                'name' => $uuid_result['username'],
                'uuid' => $uuid_result['uuid'],
                'go_api_endpoint' => $go_api_url . '/api/players/' . $uuid_result['uuid'],
                'local_endpoint' => '/player.php?username=' . urlencode($uuid_result['username']),
                'api_data' => !isset($api_player_data['error']) ? $api_player_data : null
            ];
        }
    }
    
    if ($type === 'faction' || $type === 'both') {
        // Search for faction using database first to get name, then use API for data
        try {
            $factionQuery = create_mongo_regex_query('name', $query, false, 'exact');
            $faction = $db->db->faction_data->findOne($factionQuery, [
                'projection' => ['name' => 1]
            ]);

            if (!$faction) {
                $factionQuery = create_mongo_regex_query('name', $query, false, 'contains');
                $faction = $db->db->faction_data->findOne($factionQuery, [
                    'projection' => ['name' => 1]
                ]);
            }

            if ($faction && isset($faction['name'])) {
                // Get up-to-date faction data from API
                require_once __DIR__ . '/../includes/api_client.php';
                $apiClient = new ApiClient();
                $api_faction_data = $apiClient->getFactionData($faction['name']);

                $response['results'][] = [
                    'type' => 'faction',
                    'name' => $faction['name'],
                    'go_api_endpoint' => $go_api_url . '/api/factions/' . urlencode($faction['name']),
                    'local_endpoint' => '/faction.php?name=' . urlencode($faction['name']),
                    'api_data' => !isset($api_faction_data['error']) ? $api_faction_data : null
                ];
            }
        } catch (Exception $e) {
            secure_log("Error searching factions: " . $e->getMessage());
        }
    }
    
    // If no results found
    if (empty($response['results'])) {
        $response['success'] = false;
        $response['error'] = 'No players or factions found matching your search';
        http_response_code(404);
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    secure_log("Error in search-bridge.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error'
    ]);
}
