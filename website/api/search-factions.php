<?php
/**
 * Faction Search API for Report Form
 *
 * This API searches for factions in the database.
 * It returns a list of faction names that match the search query.
 */

require_once __DIR__ . '/../includes/security_headers.php';
set_security_headers();

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://portal.massacremc.net');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../includes/db_access.php';

try {
    if (!isset($_GET['q']) || empty(trim($_GET['q']))) {
        echo json_encode(['success' => false, 'error' => 'Query parameter is required']);
        exit;
    }

    $query = trim($_GET['q']);
    
    // Validate query length
    if (strlen($query) < 2) {
        echo json_encode(['success' => false, 'error' => 'Query must be at least 2 characters']);
        exit;
    }

    if (strlen($query) > 50) {
        echo json_encode(['success' => false, 'error' => 'Query too long']);
        exit;
    }

    // Sanitize query
    $query = preg_replace('/[^a-zA-Z0-9_\-\s]/', '', $query);
    
    if (empty($query)) {
        echo json_encode(['success' => false, 'error' => 'Invalid query']);
        exit;
    }

    // Use the same approach as admin portal - search via API client and database
    require_once __DIR__ . '/../includes/api_client.php';

    $factions = [];

    // First try to get exact match from Go API (like admin portal does)
    try {
        $apiClient = new ApiClient();
        $faction_data = $apiClient->getFactionData($query);

        if (!isset($faction_data['error']) && !empty($faction_data)) {
            // Found exact match via API
            $factions[] = [
                'name' => $faction_data['Name'] ?? $query,
                'description' => isset($faction_data['Description']) ? substr($faction_data['Description'], 0, 100) : '',
                'power' => $faction_data['Power'] ?? 0,
                'member_count' => isset($faction_data['Members']) ? count($faction_data['Members']) : 0,
                'source' => 'api'
            ];
        }
    } catch (Exception $e) {
        error_log("Faction search API error: " . $e->getMessage());
    }

    // If no exact match found via API, search database for partial matches
    if (empty($factions)) {
        try {
            $db = new DatabaseAccess();

            // Search faction_data collection first (same as admin portal)
            $searchRegex = new MongoDB\BSON\Regex(preg_quote($query, '/'), 'i');

            // Try faction_data collection first
            $cursor = $db->db->faction_data->find(
                ['name' => $searchRegex],
                [
                    'projection' => [
                        'name' => 1,
                        'description' => 1,
                        'power' => 1,
                        'members' => 1,
                        'strength' => 1
                    ],
                    'limit' => 10,
                    'sort' => ['name' => 1]
                ]
            );

            foreach ($cursor as $faction) {
                $memberCount = 0;
                if (isset($faction['members']) && is_array($faction['members'])) {
                    $memberCount = count($faction['members']);
                }

                $factions[] = [
                    'name' => $faction['name'] ?? 'Unknown',
                    'description' => isset($faction['description']) ? substr($faction['description'], 0, 100) : '',
                    'power' => $faction['power'] ?? $faction['strength'] ?? 0,
                    'member_count' => $memberCount,
                    'source' => 'faction_data'
                ];
            }

            // If still no results, try factions collection as fallback
            if (empty($factions)) {
                $cursor = $db->db->factions->find(
                    ['name' => $searchRegex],
                    [
                        'projection' => [
                            'name' => 1,
                            'description' => 1,
                            'power' => 1,
                            'members' => 1,
                            'created_at' => 1
                        ],
                        'limit' => 10,
                        'sort' => ['name' => 1]
                    ]
                );

                foreach ($cursor as $faction) {
                    $memberCount = 0;
                    if (isset($faction['members']) && is_array($faction['members'])) {
                        $memberCount = count($faction['members']);
                    }

                    $factions[] = [
                        'name' => $faction['name'] ?? 'Unknown',
                        'description' => isset($faction['description']) ? substr($faction['description'], 0, 100) : '',
                        'power' => $faction['power'] ?? 0,
                        'member_count' => $memberCount,
                        'created_at' => isset($faction['created_at']) ? $faction['created_at']->toDateTime()->format('Y-m-d') : null,
                        'source' => 'factions'
                    ];
                }
            }
        } catch (Exception $e) {
            error_log("Database faction search error: " . $e->getMessage());
        }
    }

    if (empty($factions)) {
        echo json_encode([
            'success' => true,
            'factions' => [],
            'message' => 'No factions found matching your search'
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'factions' => $factions,
            'count' => count($factions)
        ]);
    }

} catch (Exception $e) {
    error_log("Faction search error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Search temporarily unavailable'
    ]);
}
?>
