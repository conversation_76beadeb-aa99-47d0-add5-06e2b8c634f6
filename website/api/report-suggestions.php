<?php
/**
 * Enhanced Player Search API (v2)
 *
 * This API provides improved player name suggestions as users type.
 * Features:
 * - Fast partial matching
 * - Returns results sorted by relevance
 * - Only returns players that exist in the player_data collection
 * - Proper error handling and logging
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../includes/db_access.php';
require_once __DIR__ . '/../includes/mongo_security.php';


if (!function_exists('create_mongo_regex_query')) {
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['error' => 'Server configuration error']);
    exit;
}


$search = isset($_GET['q']) ? trim($_GET['q']) : '';


$response = [
    'success' => false,
    'players' => [],
    'query' => $search,
    'message' => ''
];

try {

    $db = new DatabaseAccess();


    if (!$db || !$db->db) {
        throw new Exception("Database connection failed");
    }


    $dbName = $db->db->getDatabaseName();


    if ($dbName !== 'mmc') {

    }


    $collections = [];
    foreach ($db->db->listCollections() as $collectionInfo) {
        $collections[] = $collectionInfo->getName();
    }

    if (!in_array('player_data', $collections)) {
        throw new Exception("Player database not available");
    }


    if (empty($search)) {
        $cursor = $db->db->player_data->find(
            [],
            [
                'projection' => ['playernames' => 1, '_id' => 0],
                'sort' => ['last_login' => -1],
                'limit' => 10
            ]
        );

        $players = [];
        foreach ($cursor as $player) {
            if (isset($player['playernames']) && is_array($player['playernames']) && !empty($player['playernames'])) {
                $players[] = $player['playernames'][0];
            } elseif (isset($player['playernames']) && is_string($player['playernames'])) {
                $players[] = $player['playernames'];
            }
        }

        $response['success'] = true;
        $response['players'] = array_slice(array_unique($players), 0, 10);
        $response['message'] = 'Showing recent players';

        echo json_encode($response);
        exit;
    }


    if (strlen($search) < 2) {
        $response['message'] = 'Please enter at least 2 characters';
        echo json_encode($response);
        exit;
    }


    $search = sanitize_mongo_input($search, 'string', [
        'max_length' => 100,
        'allow_empty' => false
    ]);

    if ($search === null) {
        $response['message'] = 'Invalid search query';
        echo json_encode($response);
        exit;
    }


    $players = [];



    $playerExactQuery = create_mongo_regex_query('playernames', $search, false, 'exact');
    $exactMatches = [];

    $cursor = $db->db->player_data->find(
        $playerExactQuery,
        [
            'projection' => ['playernames' => 1, '_id' => 0],
            'limit' => 10
        ]
    );

    foreach ($cursor as $player) {
        if (isset($player['playernames']) && is_array($player['playernames'])) {
            foreach ($player['playernames'] as $name) {
                if (!empty($name)) {
                    $exactMatches[] = $name;
                }
            }
        } elseif (isset($player['playernames']) && is_string($player['playernames']) && !empty($player['playernames'])) {
            $exactMatches[] = $player['playernames'];
        }
    }


    $playerStartsWithQuery = create_mongo_regex_query('playernames', $search, false, 'start');
    $startsWithMatches = [];

    $cursor = $db->db->player_data->find(
        $playerStartsWithQuery,
        [
            'projection' => ['playernames' => 1, '_id' => 0],
            'limit' => 20
        ]
    );

    foreach ($cursor as $player) {
        if (isset($player['playernames']) && is_array($player['playernames'])) {
            foreach ($player['playernames'] as $name) {
                if (!empty($name) && !in_array($name, $exactMatches)) {
                    $startsWithMatches[] = $name;
                }
            }
        } elseif (isset($player['playernames']) && is_string($player['playernames']) && !empty($player['playernames'])) {
            if (!in_array($player['playernames'], $exactMatches)) {
                $startsWithMatches[] = $player['playernames'];
            }
        }
    }


    $playerContainsQuery = create_mongo_regex_query('playernames', $search, false, 'contains');
    $containsMatches = [];

    $cursor = $db->db->player_data->find(
        $playerContainsQuery,
        [
            'projection' => ['playernames' => 1, '_id' => 0],
            'limit' => 30
        ]
    );

    foreach ($cursor as $player) {
        if (isset($player['playernames']) && is_array($player['playernames'])) {
            foreach ($player['playernames'] as $name) {
                if (!empty($name) && !in_array($name, $exactMatches) && !in_array($name, $startsWithMatches)) {
                    $containsMatches[] = $name;
                }
            }
        } elseif (isset($player['playernames']) && is_string($player['playernames']) && !empty($player['playernames'])) {
            if (!in_array($player['playernames'], $exactMatches) && !in_array($player['playernames'], $startsWithMatches)) {
                $containsMatches[] = $player['playernames'];
            }
        }
    }


    $exactMatches = array_unique($exactMatches);
    $startsWithMatches = array_unique($startsWithMatches);
    $containsMatches = array_unique($containsMatches);


    sort($exactMatches, SORT_STRING | SORT_FLAG_CASE);
    sort($startsWithMatches, SORT_STRING | SORT_FLAG_CASE);
    sort($containsMatches, SORT_STRING | SORT_FLAG_CASE);


    $players = array_merge($exactMatches, $startsWithMatches, $containsMatches);


    if (empty($players)) {


        $simpleQuery = ['playernames' => ['$regex' => $search, '$options' => 'i']];
        $cursor = $db->db->player_data->find(
            $simpleQuery,
            [
                'projection' => ['playernames' => 1, '_id' => 0],
                'limit' => 20
            ]
        );

        foreach ($cursor as $player) {
            if (isset($player['playernames'])) {
                if (is_array($player['playernames'])) {
                    foreach ($player['playernames'] as $name) {
                        if (!empty($name)) {
                            $players[] = $name;
                        }
                    }
                } elseif (is_string($player['playernames']) && !empty($player['playernames'])) {
                    $players[] = $player['playernames'];
                }
            }
        }


        $players = array_unique($players);
    }


    $maxResults = (strlen($search) <= 3) ? 10 : 20;
    $players = array_slice($players, 0, $maxResults);


    $response['success'] = true;
    $response['players'] = $players;
    $response['message'] = count($players) > 0 ? 'Found ' . count($players) . ' matching players' : 'No matching players found';

    echo json_encode($response);
} catch (Exception $e) {
    $response['message'] = 'An error occurred while searching for players';
    $response['error'] = $e->getMessage();

    echo json_encode($response);
}