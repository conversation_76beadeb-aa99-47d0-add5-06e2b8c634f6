# Global ServerName to suppress warnings
ServerName portal.massacremc.net

<VirtualHost *:80>
    ServerName portal.massacremc.net
    Redirect permanent / https://portal.massacremc.net/

    RewriteEngine On
    RewriteCond %{HTTPS} !=on
    RewriteRule ^/?(.*) https://%{SERVER_NAME}/$1 [R,L]

    #SSLEngine on
    #SSLCertificateFile /etc/ssl/certs/massacremc.net.pem
    #SSLCertificateKeyFile /etc/ssl/private/massacremc.net.key

    ProxyPreserveHost On
    
</VirtualHost>

<VirtualHost *:443>
    ServerName portal.massacremc.net
    DocumentRoot "/var/www/html/Massacre-Website"
    ErrorDocument 503 /503.php

    # Security Headers
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options "nosniff"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Access-Control-Allow-Origin "https://portal.massacremc.net"
    Header set Access-Control-Allow-Credentials "true"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Origin, Content-Type, Accept, Authorization"
    
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/massacremc.net.pem
    SSLCertificateKeyFile /etc/ssl/private/massacremc.net.key
    SSLProtocol all -SSLv3 -TLSv1 -TLSv1.1
    SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384
    SSLHonorCipherOrder off
    SSLSessionTickets off

    # Proxy Configuration
    ProxyPreserveHost On
    ProxyRequests Off
    ProxyErrorOverride On

    # Enable Compression
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/json
    AddType application/javascript .js

    # Cache Control
    <FilesMatch "\.(ico|pdf|jpg|jpeg|png|gif|js|css)$">
        Header set Cache-Control "max-age=2592000, public"
    </FilesMatch>

    <FilesMatch "\.css$">
    Header set Content-Type "text/css"
    </FilesMatch>

        # Add cache control headers
    <FilesMatch "\.(js|css|html)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>

    # Protect JavaScript files
<FilesMatch "\.js$">
    Order Allow,Deny
    Allow from all
</FilesMatch>


<FilesMatch "admin\.[a-f0-9]+\.js$">
    Order Allow,Deny
    Allow from all
    Header set Content-Type "application/javascript"
</FilesMatch>

    <FilesMatch "report\.[a-f0-9]+\.js$">
        Order Allow,Deny
        Allow from all
        Header set Content-Type "application/javascript"
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>



    # Directory Configuration
    <Directory "/var/www/html/Massacre-Website">
        Options -Indexes +FollowSymLinks
        AllowOverride FileInfo
        Require all granted
        
        # Restrict HTTP Methods
        <LimitExcept GET POST HEAD>
            Deny from all
        </LimitExcept>

   <FilesMatch "\.env$">
    Order allow,deny
    Deny from all
    </FilesMatch>

    <IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block direct access to JS files when accessed directly
    RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+js/.*\.js [NC]
    RewriteCond %{HTTP_REFERER} !^https://portal\.massacremc\.net/ [NC]
    RewriteRule ^ - [F,L]
    
    # Allow access when properly referred from your site
    RewriteCond %{HTTP_REFERER} ^https://portal\.massacremc\.net/ [NC]
    RewriteRule ^js/.*\.js$ - [L]
</IfModule>

    # Deny access to all Python files
<FilesMatch "\.py$">
    Order deny,allow
    Deny from all
</FilesMatch>

# Deny access to sensitive files by name
<FilesMatch "^(app|admin)\.py$">
    Order deny,allow
    Deny from all
</FilesMatch>

# Protect environment files
<FilesMatch "^\.env$">
    Order deny,allow
    Deny from all
</FilesMatch>

        # Consolidated Rewrite Rules
        RewriteEngine On

        # Force /help to serve help.php instead of help/ directory
        RewriteRule ^help/?$ help.php [L]

        # Redirect specific help subdomain requests only
        RewriteRule ^help/lookup(.*)$ https://help.massacremc.net/lookup$1 [R=301,L]
        RewriteRule ^help/conversations(.*)$ https://help.massacremc.net/conversations$1 [R=301,L]

        RewriteRule ^report$ report.php [L]
        RewriteRule ^services$ services.php [L]
        RewriteRule ^search$ search.php [L]
        RewriteRule ^success$ success.php [L]
        RewriteRule ^appeal_confirmation$ appeal_confirmation.php [L]
        RewriteRule ^player$ player.php [L]
        RewriteRule ^rules$ rules.php [L]
        RewriteRule ^faction$ faction.php [L]
        RewriteRule ^player/([^/]+)$ player.php?username=$1 [L,QSA]
        RewriteRule ^faction/([^/]+)$ faction.php?name=$1 [L,QSA]
        RewriteRule ^appeal$ appeal.php [L]
        RewriteRule ^appeal$ help.php [L]
        RewriteRule ^legal$ legal.php [L]
        RewriteRule ^leaderboards/(kills|balance|vote|kdr)$ leaderboards/$1.php [L]
        RewriteRule ^leaderboards/factions/(kills|balance|strength)$ leaderboards/factions/$1.php [L]
        ErrorDocument 404 /404.php
        ErrorDocument 500 /500.php
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
    </Directory>
    
    # PHP Configuration
    php_value upload_max_filesize 100M
    php_value post_max_size 100M
    
    AllowEncodedSlashes NoDecode
</VirtualHost>
