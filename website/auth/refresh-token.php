<?php
/**
 * Session Token Refresh Endpoint
 * Provides fresh session tokens for authenticated users
 */

require_once __DIR__ . '/../includes/core.php';
require_once __DIR__ . '/../includes/auth.php';

header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Require full session security (auth + token)
    if (!validate_session_security(true)) {
        // Determine reason for client UX
        if (!validate_authenticated_session()) {
            http_response_code(401);
            header('X-Auth-Required: session-expired');
            echo json_encode(['error' => 'Authentication required']);
            exit;
        }
        secure_log("Token refresh failed: Invalid current token", "warning", [
            'user_id' => $_SESSION['discord_user_id'] ?? 'unknown',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
        ]);
        http_response_code(401);
        header('X-Auth-Required: invalid-token');
        echo json_encode(['error' => 'Invalid session token']);
        exit;
    }

    // Generate new session token
    $new_token = generate_session_token();
    $_SESSION['session_token'] = $new_token;
    $_SESSION['session_token_expires_at'] = time() + 1800; // server-side TTL
    $_SESSION['last_activity'] = time();

    secure_log("Session token refreshed", "info", [
        'user_id' => $_SESSION['discord_user_id'],
        'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
    ]);

    header('X-Token-Expires-In: 1800');
    echo json_encode([
        'success' => true,
        'session_token' => $new_token,
        'expires_in' => 1800 // 30 minutes
    ]);

} catch (Exception $e) {
    secure_log("Token refresh error: " . $e->getMessage(), "error");
    
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
