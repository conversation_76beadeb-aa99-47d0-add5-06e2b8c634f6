# syntax=docker/dockerfile:1.4

FROM golang:1.25.0-alpine3.22 AS build

# Set the working directory to /build/core (not just /build)
WORKDIR /build

# Copy everything from production (context = production/)
COPY .. .

# Optional: show workspace status for debug
RUN go work sync && go work use && ls -R

# Move to core folder and build
WORKDIR /build/core
RUN --mount=type=cache,id=gocache,target=/root/.cache/go-build \
    --mount=type=cache,id=gocache,target=/go/pkg/mod \
    go build -o /build/server-executable -v main.go


FROM alpine

RUN adduser -D -s /bin/sh -u 1000 server && \
    mkdir /server && \
    chown 1000:1000 /server

WORKDIR /server

COPY --chown=1000:1000 --from=build /build/server-executable .

EXPOSE 19132/udp

USER server

CMD ["/server/server-executable"]
