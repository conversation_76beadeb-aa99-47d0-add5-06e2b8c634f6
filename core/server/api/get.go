package api

import (
	"fmt"
	"github.com/google/uuid"
	"net/http"
	"server/server/database"
	"server/server/utils"

	"github.com/gin-gonic/gin"
)

func initGetRequests(rg *gin.RouterGroup) {
	rg.GET("/players/:uuid", jwtAuthMiddleware(), func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("uuid"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "invalid UUID format",
			})
			return
		}
		pd, err := database.DB.FindPlayer(id)
		if err != nil {
			// Check if it's a "not found" error
			if _, ok := err.(utils.PlayerDataNotFoundError); ok {
				c.JSON(http.StatusNotFound, gin.H{
					"error": fmt.Sprintf("cannot find player data (identifier: %s)", id.String()),
				})
				return
			}
			// For other errors, return internal server error
			c.<PERSON>(http.StatusInternalServerError, gin.H{
				"error": "internal server error",
			})
			return
		}
		c.<PERSON>(http.StatusOK, gin.H{
			"data": pd,
		})
	})

	rg.GET("/factions/:name", jwtAuthMiddleware(), func(c *gin.Context) {
		name := c.Param("name")
		fd, err := database.DB.FindFaction(name)
		if err != nil {
			// Check if it's a "not found" error
			if _, ok := err.(utils.FactionDataNotFoundError); ok {
				c.JSON(http.StatusNotFound, gin.H{
					"error": fmt.Sprintf("cannot find faction data (identifier: %s)", name),
				})
				return
			}
			// For other errors, return internal server error
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "internal server error",
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"data": fd,
		})
	})
}
