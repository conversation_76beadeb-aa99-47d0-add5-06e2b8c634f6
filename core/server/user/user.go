package user

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/scoreboard"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/golang-collections/collections/stack"
	"github.com/google/uuid"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"math"
	"server/server/cooldown"
	"server/server/database"
	"server/server/font"
	"server/server/language"
	"server/server/utils"
	"strings"
	"sync"
	"time"
)

var (
	userMu sync.Mutex
	users  = map[uuid.UUID]*User{}
)

type User struct {
	pl *player.Player
	h  *world.EntityHandle

	cooldownMap cooldown.MappedCoolDown[PlayerCoolDowns]

	Scoreboard *scoreboard.Scoreboard
	Data       *database.PlayerData
	WTData     WTData
	FirstTime  bool

	FactionInfo FactionInfo
}

type FactionInfo struct {
	DuelInvites           map[*player.Player]bool
	TPARequest            TPARequest
	IgnoreDrilledBlocksAt []mgl64.Vec3
}

type TPARequest struct {
	Target *world.EntityHandle
	To     *world.EntityHandle
}

func newUser(pl *player.Player) (*User, error) {
	if pl == nil {
		panic("New player should not be nil")
	}

	userMu.Lock()
	defer userMu.Unlock()

	ft := false
	d, _ := database.DB.FindPlayer(pl.UUID())
	if d == nil {
		ft = true
		pd := &database.PlayerData{
			UUID:       pl.UUID(),
			Username:   pl.Name(),
			FirstLogin: time.Now(),
			LastLogin:  time.Now(),
			RankId:     database.Player.Shortened(),
			Faction: database.PlayerFaction{
				Stats: database.Stats{
					CrateKeys: map[database.CrateType]int{},
					Kits:      map[database.Rank]time.Time{},
				},
				Home:     map[string]mgl64.Vec3{},
				Backpack: map[int]map[int]database.CustomStack{},
				Bounties: map[uuid.UUID]float64{},
				Role:     database.Member,
			},
		}

		if err := database.DB.CreatePlayer(pd); err != nil {
			return nil, err
		}
		d = pd
	}

	d.ProtocolId = utils.Session(pl).ClientData().GameVersion

	u := &User{
		pl: pl,
		h:  pl.H(),

		cooldownMap: cooldown.NewMappedCoolDown[PlayerCoolDowns](),

		Data:        d,
		FirstTime:   ft,
		FactionInfo: FactionInfo{DuelInvites: map[*player.Player]bool{}},
	}
	users[pl.UUID()] = u
	return u, nil
}

func GetUser(pl *player.Player) *User {
	if users[pl.UUID()] == nil {
		return utils.Panics(newUser(pl))
	}

	userMu.Lock()
	defer userMu.Unlock()

	users[pl.UUID()].pl = pl
	users[pl.UUID()].h = pl.H()

	return users[pl.UUID()]
}

func GetUserByUUID(uuid uuid.UUID) *User {
	userMu.Lock()
	defer userMu.Unlock()

	for _, user := range users {
		if user.Data.UUID == uuid {
			return user
		}
	}
	return nil
}

func Save(pl *player.Player) {
	user := GetUserByUUID(pl.UUID())
	_ = database.DB.SavePlayer(user.Data)
}

func UpdateUserData(pd *database.PlayerData) {
	userMu.Lock()
	defer userMu.Unlock()

	if users[pd.UUID] == nil {
		return
	}
	users[pd.UUID].Data = pd
}

func (u *User) Player() *player.Player {
	return u.pl
}

func (u *User) H() *world.EntityHandle {
	return u.h
}

// IsCoolDownActive sets a cooldown for the given type if it doesn't already exist or renews it if specified.
// It returns true if the cooldown is initially active (to be used for conditional command execution).
func (u *User) IsCoolDownActive(cooldownType PlayerCoolDowns, duration time.Duration, renew, create, sendMessage bool) bool {
	coolDown := u.cooldownMap[cooldownType]

	if coolDown == nil {
		coolDown = cooldown.NewCoolDown()
		u.cooldownMap[cooldownType] = coolDown
	}
	exists := coolDown.Active()
	if create && (renew || !coolDown.Active()) {
		coolDown.Set(duration)
	}

	if sendMessage && exists {
		u.pl.Message(text.Colourf(language.Translate(u.pl).Error.CoolDown, coolDown.Remaining().Seconds()))
	}

	return exists
}

func (u *User) CoolDownTimeRemaining(cooldownType PlayerCoolDowns) time.Duration {
	cd := u.cooldownMap[cooldownType]
	if cd == nil {
		return 0
	}
	return cd.Remaining()
}

func (u *User) SendScoreboard(numOfSpaces int) {
	u.Scoreboard.Set(0, text.Colourf("%v%v", strings.Repeat(" ", numOfSpaces), font.Transform("SEASON 1")))
	u.pl.SendScoreboard(u.Scoreboard)
}

func (u *User) AddItem(its ...item.Stack) bool {
	if len(u.pl.Inventory().Items())+len(its) > 36 {
		u.pl.Message(text.Colourf(language.Translate(u.pl).Error.InventoryFull))
		return false
	}
	for _, it := range its {
		if _, err := u.pl.Inventory().AddItem(it); err != nil {
			if err := u.pl.Inventory().RemoveItem(it); err != nil {
				panic(err)
			}
			u.pl.Message(text.Colourf(language.Translate(u.pl).Error.InventoryFull))
			return false
		}
	}
	return true
}

type WTData struct {
	Volume struct {
		Pos1 cube.Pos
		Pos2 cube.Pos
	}
	Undo stack.Stack
	Redo stack.Stack
}

func (u *User) Selection() map[cube.Pos]world.Block {
	pos1, pos2 := u.WTData.Volume.Pos1, u.WTData.Volume.Pos2
	selection := map[cube.Pos]world.Block{}
	for x := int(math.Min(float64(pos1.X()), float64(pos2.X()))); x <= int(math.Max(float64(pos1.X()), float64(pos2.X()))); x++ {
		for y := int(math.Min(float64(pos1.Y()), float64(pos2.Y()))); y <= int(math.Max(float64(pos1.Y()), float64(pos2.Y()))); y++ {
			for z := int(math.Min(float64(pos1.Z()), float64(pos2.Z()))); z <= int(math.Max(float64(pos1.Z()), float64(pos2.Z()))); z++ {
				selection[cube.Pos{x - pos1.X(), y - pos1.Y(), z - pos1.Z()}] = u.pl.Tx().Block(cube.Pos{x, y, z})
			}
		}
	}
	return selection
}
