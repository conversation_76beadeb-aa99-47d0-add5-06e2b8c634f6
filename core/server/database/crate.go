package database

import (
	"github.com/sandertv/gophertunnel/minecraft/text"
	"golang.org/x/exp/rand"
)

type CrateType int

const (
	Common CrateType = iota
	Rare
	Legendary
	Ancient
	Vote
)

var CrateTypes = []CrateType{Common, Rare, Legendary, Ancient, Vote}

var CrateNames = []string{
	text.Colourf("<bold><gold>Common</gold></bold>"),
	text.Colourf("<bold><gold>Rare</gold></bold>"),
	text.Colourf("<bold><gold>Legendary</gold></bold>"),
	text.Colourf("<bold><gold>Ancient</gold></bold>"),
	text.Colourf("<bold><gold>Vote</gold></bold>"),
}

func (t CrateType) Name() string {
	return CrateNames[t]
}

func RandCrateType() CrateType {
	return CrateTypes[rand.Intn(len(CrateTypes))]
}
