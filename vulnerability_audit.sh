#!/bin/bash

# Automated Vulnerability Audit System for MassacreMC
# Performs comprehensive security scans and generates reports
# Usage: ./vulnerability_audit.sh [install|system|web|network|integrity|malware|full]

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
AUDIT_DIR="/var/log/security/audits"
REPORT_DIR="/var/log/security/reports"
CONFIG_DIR="/etc/massacremc/security"

# Create directories
mkdir -p "$AUDIT_DIR" "$REPORT_DIR" "$CONFIG_DIR"

# Configuration
AUDIT_LOG="$AUDIT_DIR/vulnerability_audit.log"
REPORT_FILE="$REPORT_DIR/vulnerability_report_$(date +%Y%m%d_%H%M%S).html"
EMAIL_REPORT="<EMAIL>"

log_audit() {
    local message="$1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $message" | tee -a "$AUDIT_LOG"
}

# Install required tools
install_audit_tools() {
    log_audit "Installing vulnerability audit tools..."
    
    # Update package list
    apt-get update -qq
    
    # Install security tools
    local tools=(
        "nmap"           # Network scanning
        "nikto"          # Web vulnerability scanner
        "lynis"          # System auditing
        "chkrootkit"     # Rootkit detection
        "rkhunter"       # Rootkit hunter
        "clamav"         # Antivirus
        "fail2ban"       # Intrusion prevention
        "aide"           # File integrity monitoring
        "tiger"          # Security audit tool
        "debsums"        # Package integrity checker
    )
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            log_audit "Installing $tool..."
            apt-get install -y "$tool" >/dev/null 2>&1
        else
            log_audit "$tool already installed"
        fi
    done
    
    # Update ClamAV database
    if command -v freshclam >/dev/null 2>&1; then
        log_audit "Updating ClamAV database..."
        freshclam >/dev/null 2>&1
    fi
}

# System vulnerability scan
scan_system_vulnerabilities() {
    log_audit "Starting system vulnerability scan..."
    
    local scan_results="$AUDIT_DIR/system_scan_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "=== SYSTEM VULNERABILITY SCAN ==="
        echo "Date: $(date)"
        echo "Hostname: $(hostname)"
        echo "OS: $(lsb_release -d 2>/dev/null || cat /etc/os-release | grep PRETTY_NAME)"
        echo ""
        
        # Check for outdated packages
        echo "=== OUTDATED PACKAGES ==="
        apt list --upgradable 2>/dev/null | head -20
        echo ""
        
        # Check for security updates
        echo "=== SECURITY UPDATES ==="
        apt list --upgradable 2>/dev/null | grep -i security | head -10
        echo ""
        
        # System audit with Lynis
        if command -v lynis >/dev/null 2>&1; then
            echo "=== LYNIS SYSTEM AUDIT ==="
            lynis audit system --quiet --no-colors 2>/dev/null | tail -50
            echo ""
        fi
        
        # Check for rootkits
        if command -v chkrootkit >/dev/null 2>&1; then
            echo "=== ROOTKIT SCAN ==="
            chkrootkit 2>/dev/null | grep -E "(INFECTED|Checking|Found)" | head -20
            echo ""
        fi
        
        # RKHunter scan
        if command -v rkhunter >/dev/null 2>&1; then
            echo "=== RKHUNTER SCAN ==="
            rkhunter --check --skip-keypress --report-warnings-only 2>/dev/null | head -20
            echo ""
        fi
        
    } > "$scan_results"
    
    log_audit "System scan completed: $scan_results"
    echo "$scan_results"
}

# Web application vulnerability scan
scan_web_vulnerabilities() {
    log_audit "Starting web application vulnerability scan..."
    
    local web_scan="$AUDIT_DIR/web_scan_$(date +%Y%m%d_%H%M%S).txt"
    local target_urls=(
        "https://portal.massacremc.net"
        "https://staff.massacremc.net"
        "http://localhost"
    )
    
    {
        echo "=== WEB APPLICATION VULNERABILITY SCAN ==="
        echo "Date: $(date)"
        echo ""
        
        for url in "${target_urls[@]}"; do
            echo "=== SCANNING: $url ==="
            
            # Nikto web vulnerability scan
            if command -v nikto >/dev/null 2>&1; then
                echo "--- Nikto Scan ---"
                nikto -h "$url" -Format txt -output - 2>/dev/null | head -30
                echo ""
            fi
            
            # Nmap web service scan
            if command -v nmap >/dev/null 2>&1; then
                local host=$(echo "$url" | sed 's|https\?://||' | cut -d'/' -f1)
                echo "--- Nmap Service Scan ---"
                nmap -sV -sC --script vuln "$host" 2>/dev/null | head -30
                echo ""
            fi
        done
        
    } > "$web_scan"
    
    log_audit "Web scan completed: $web_scan"
    echo "$web_scan"
}

# Network security scan
scan_network_security() {
    log_audit "Starting network security scan..."
    
    local network_scan="$AUDIT_DIR/network_scan_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "=== NETWORK SECURITY SCAN ==="
        echo "Date: $(date)"
        echo ""
        
        # Port scan
        echo "=== OPEN PORTS ==="
        netstat -tuln | grep LISTEN
        echo ""
        
        # Firewall status
        echo "=== FIREWALL STATUS ==="
        iptables -L -n | head -20
        echo ""
        
        # Network connections
        echo "=== ACTIVE CONNECTIONS ==="
        netstat -tn | grep ESTABLISHED | head -20
        echo ""
        
        # Check for suspicious network activity
        echo "=== SUSPICIOUS NETWORK ACTIVITY ==="
        netstat -tn | grep ESTABLISHED | awk '{print $5}' | cut -d':' -f1 | sort | uniq -c | sort -nr | head -10
        echo ""
        
    } > "$network_scan"
    
    log_audit "Network scan completed: $network_scan"
    echo "$network_scan"
}

# File integrity check
check_file_integrity() {
    log_audit "Starting file integrity check..."
    
    local integrity_check="$AUDIT_DIR/integrity_check_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "=== FILE INTEGRITY CHECK ==="
        echo "Date: $(date)"
        echo ""
        
        # Check package integrity
        if command -v debsums >/dev/null 2>&1; then
            echo "=== PACKAGE INTEGRITY ==="
            debsums -c 2>/dev/null | head -20
            echo ""
        fi
        
        # Check critical system files
        echo "=== CRITICAL FILE PERMISSIONS ==="
        ls -la /etc/passwd /etc/shadow /etc/hosts /etc/sudoers 2>/dev/null
        echo ""
        
        # Check for SUID/SGID files
        echo "=== SUID/SGID FILES ==="
        find / -type f \( -perm -4000 -o -perm -2000 \) -exec ls -la {} \; 2>/dev/null | head -20
        echo ""
        
        # Check for world-writable files
        echo "=== WORLD-WRITABLE FILES ==="
        find / -type f -perm -002 -exec ls -la {} \; 2>/dev/null | head -10
        echo ""
        
    } > "$integrity_check"
    
    log_audit "File integrity check completed: $integrity_check"
    echo "$integrity_check"
}

# Malware scan
scan_malware() {
    log_audit "Starting malware scan..."
    
    local malware_scan="$AUDIT_DIR/malware_scan_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "=== MALWARE SCAN ==="
        echo "Date: $(date)"
        echo ""
        
        # ClamAV scan
        if command -v clamscan >/dev/null 2>&1; then
            echo "=== CLAMAV SCAN ==="
            clamscan -r /var/www /tmp /home --infected --summary 2>/dev/null
            echo ""
        fi
        
        # Check for suspicious processes
        echo "=== SUSPICIOUS PROCESSES ==="
        ps aux | grep -E "(wget|curl|nc|netcat)" | grep -v grep
        echo ""
        
        # Check for suspicious files
        echo "=== SUSPICIOUS FILES ==="
        find /tmp /var/tmp -name "*.sh" -o -name "*.py" -o -name "*.pl" 2>/dev/null | head -10
        echo ""
        
    } > "$malware_scan"
    
    log_audit "Malware scan completed: $malware_scan"
    echo "$malware_scan"
}

# Generate HTML report
generate_html_report() {
    local system_scan="$1"
    local web_scan="$2"
    local network_scan="$3"
    local integrity_check="$4"
    local malware_scan="$5"
    
    log_audit "Generating HTML report: $REPORT_FILE"
    
    cat > "$REPORT_FILE" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>MassacreMC Vulnerability Audit Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .critical { border-left: 5px solid #e74c3c; }
        .warning { border-left: 5px solid #f39c12; }
        .info { border-left: 5px solid #3498db; }
        .success { border-left: 5px solid #27ae60; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .summary { background: #ecf0f1; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>MassacreMC Vulnerability Audit Report</h1>
        <p>Generated: $(date)</p>
        <p>Hostname: $(hostname)</p>
    </div>
    
    <div class="summary">
        <h2>Executive Summary</h2>
        <ul>
            <li>System Scan: $([ -f "$system_scan" ] && echo "Completed" || echo "Failed")</li>
            <li>Web Application Scan: $([ -f "$web_scan" ] && echo "Completed" || echo "Failed")</li>
            <li>Network Security Scan: $([ -f "$network_scan" ] && echo "Completed" || echo "Failed")</li>
            <li>File Integrity Check: $([ -f "$integrity_check" ] && echo "Completed" || echo "Failed")</li>
            <li>Malware Scan: $([ -f "$malware_scan" ] && echo "Completed" || echo "Failed")</li>
        </ul>
    </div>
    
    <div class="section info">
        <h2>System Vulnerability Scan</h2>
        <pre>$([ -f "$system_scan" ] && cat "$system_scan" || echo "Scan not available")</pre>
    </div>
    
    <div class="section warning">
        <h2>Web Application Scan</h2>
        <pre>$([ -f "$web_scan" ] && cat "$web_scan" || echo "Scan not available")</pre>
    </div>
    
    <div class="section info">
        <h2>Network Security Scan</h2>
        <pre>$([ -f "$network_scan" ] && cat "$network_scan" || echo "Scan not available")</pre>
    </div>
    
    <div class="section warning">
        <h2>File Integrity Check</h2>
        <pre>$([ -f "$integrity_check" ] && cat "$integrity_check" || echo "Check not available")</pre>
    </div>
    
    <div class="section critical">
        <h2>Malware Scan</h2>
        <pre>$([ -f "$malware_scan" ] && cat "$malware_scan" || echo "Scan not available")</pre>
    </div>
    
    <div class="section success">
        <h2>Recommendations</h2>
        <ul>
            <li>Update all outdated packages immediately</li>
            <li>Review and fix any identified vulnerabilities</li>
            <li>Implement additional security measures as needed</li>
            <li>Schedule regular vulnerability scans</li>
            <li>Monitor security logs continuously</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    log_audit "HTML report generated: $REPORT_FILE"
}

# Send email report
send_email_report() {
    if command -v mail >/dev/null 2>&1 && [ -n "$EMAIL_REPORT" ]; then
        log_audit "Sending email report to $EMAIL_REPORT"
        {
            echo "MassacreMC Vulnerability Audit Report"
            echo "Generated: $(date)"
            echo ""
            echo "Report file: $REPORT_FILE"
            echo ""
            echo "Please review the attached report for security findings."
        } | mail -s "MassacreMC Vulnerability Audit Report" "$EMAIL_REPORT"
    fi
}

# Main audit function
run_full_audit() {
    log_audit "Starting comprehensive vulnerability audit..."
    
    # Install tools if needed
    install_audit_tools
    
    # Run all scans
    local system_scan=$(scan_system_vulnerabilities)
    local web_scan=$(scan_web_vulnerabilities)
    local network_scan=$(scan_network_security)
    local integrity_check=$(check_file_integrity)
    local malware_scan=$(scan_malware)
    
    # Generate reports
    generate_html_report "$system_scan" "$web_scan" "$network_scan" "$integrity_check" "$malware_scan"
    send_email_report
    
    log_audit "Comprehensive vulnerability audit completed"
    echo "Report generated: $REPORT_FILE"
}

# Command line interface
case "${1:-full}" in
    "install")
        install_audit_tools
        ;;
    "system")
        scan_system_vulnerabilities
        ;;
    "web")
        scan_web_vulnerabilities
        ;;
    "network")
        scan_network_security
        ;;
    "integrity")
        check_file_integrity
        ;;
    "malware")
        scan_malware
        ;;
    "full")
        run_full_audit
        ;;
    *)
        echo "Usage: $0 {install|system|web|network|integrity|malware|full}"
        echo ""
        echo "Commands:"
        echo "  install   - Install required audit tools"
        echo "  system    - Run system vulnerability scan"
        echo "  web       - Run web application scan"
        echo "  network   - Run network security scan"
        echo "  integrity - Run file integrity check"
        echo "  malware   - Run malware scan"
        echo "  full      - Run complete audit (default)"
        exit 1
        ;;
esac
